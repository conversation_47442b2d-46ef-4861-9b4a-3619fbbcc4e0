<template>
	<div class="page ">
		<top-back title="銀行卡/存摺詳情"></top-back>

		<div class="cot">
			<div class="item">
				<div class="t">持卡人姓名</div>
				<input disabled v-model="bankInfo.realname" />
			</div>
      <div class="item">
        <div class="t">銀行卡/存摺卡號</div>
        <input disabled v-model="bankInfo.bank_num" />
      </div>
      <div class="item">
        <div class="t">銀行名稱</div>
        <input disabled v-model="bankInfo.bank_address" />
      </div>
			<div class="item">
				<div class="t">分行名稱</div>
				<input disabled v-model="bankInfo.bank_name" />
			</div>
			<div class="item">
				<div class="t">分行代碼</div>
				<input disabled v-model="bankInfo.bank_code" />
			</div>
		</div>
		
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "addBankCard",
		props: {},
		data() {
			return {
				bankInfo: {},
			};
		},
		components: {},
		created() {
      this.bankInfo = this.$storage.get("bankDetail");
		},
		computed: {},
		methods: {
		},
	};
</script>

<style scoped lang="less">

	.page {
		padding: 0.6rem 0.15rem 0;
		min-height: 100vh;
		background: #f7f7f7;

		.cot {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
			border-radius: 0.07rem;
			padding: 0.2rem 0.15rem;

			.item {
				margin-bottom: 0.2rem;

				.t {
					font-weight: 600;
					color: #0e1028;
				}

				&.last {
					margin-bottom: 0;
				}

				input {
					width: 100%;
					border-bottom: 0.01rem solid #cecece;
					height: 0.3rem;
					color:#000;
					&::placeholder {
						color: #8a8a8a;
						font-weight: 500;
						font-size: 0.12rem;
					}
				}
			}
		}
	}
</style>