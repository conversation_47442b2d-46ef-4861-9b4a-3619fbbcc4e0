<template>
	<div class="page">
		<top-back title="實名認證"></top-back>

		<div v-if="showFlag">
			<template v-if="isEdit">
				<div class="editing">
					<!-- <div class="top">
						<div class="icon rzwc animate__animated animate__fadeIn"></div>
						<div class="t">
							{{ $t("new").a8 }}
						</div>
					</div> -->
					<div class="upload">
						<div class="t">請上傳您的身分證正反面照片</div>
						<div class="flex flex-b">
							<div class="item flex flex-c">
								<div v-if="!showFrontcard">
									<div class="icon sf animate__animated animate__fadeIn"></div>
									<div class="t1">正面</div>
								</div>
					
								<img class="animate__animated animate__fadeIn" v-if="showFrontcard" :src="showFrontcard" />
								<input class="inp" accept="image/*" type="file" @change="uploadFile($event, 1)" />
							</div>
							<div class="item flex flex-c">
								<div v-if="!showBackcard">
									<div class="icon sf animate__animated animate__fadeIn"></div>
									<div class="t1">反面</div>
								</div>
								<img class="animate__animated animate__fadeIn" v-if="showBackcard" :src="showBackcard" />
								<input class="inp" accept="image/*" type="file" @change="uploadFile($event, 2)" />
							</div>
						</div>
					</div>
					<div class="txt">請依下列資料完成實名認證</div>
					<div class="bg">
						<div class="ipt">
							<!-- <div class="t1">姓名</div> -->
							<input v-model="form.true_name" placeholder-style="color: #999" placeholder="請輸入姓名" />
						</div>
						<div class="ipt">
							<!-- <div class="t1">身分證號</div> -->
							<input v-model="form.card_id" placeholder-style="color: #999" placeholder="請輸入身分證號" />
						</div>
						<div class="b-btn animate__animated animate__fadeIn" @click="submit">提交</div>
					</div>
				</div>
			</template>
			
			<template v-if="!isEdit">
				<div class="edited">
					<div class="top">
						<div class="animate__animated animate__fadeIn flex flex-c">
							<van-icon v-if="userInfo.is_true == 1" name="checked" size="50" color="#7f7f7f" />
							<img v-else src="../../assets/v2/rzcg.png" style="width: 1.34rem;height: 1.4rem;"/>
						</div>
						<div class="t">{{ userInfo.is_true == 1 ? "已實名" : "審核中" }}</div>
					</div>
          <div class="upload">
            <div class="flex flex-b">
              <div class="item flex flex-c">
                <img class="animate__animated animate__fadeIn" :src="showPic1" />
              </div>
              <div class="item flex flex-c">
                <img class="animate__animated animate__fadeIn" :src="showPic2" />
              </div>
            </div>
          </div>
					<div>
						<div class="t1">姓名</div>
						<div class="ipt">{{ form.true_name || "-" }}</div>
					</div>
					<div>
						<div class="t1">身分證號</div>
						<div class="ipt">{{ form.card_id || "-" }}</div>
					</div>
				</div>
			</template>
			<div class="tips">
				我們將確保您提交的實名資訊真實有效，您的個人資料將受到嚴格保護，絕對不會有任何外泄風險，如若發生任何異常，我們將承擔一切責任，並竭誠為您解決問題，確保您的資訊安全無虞。
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "authInfo",
		props: {},
		data() {
			return {
				isEdit: false,
				showFrontcard: "",
				showBackcard: "",
				form: {
					frontcard: "",
					backcard: "",
					true_name: "",
					card_id: "",
				},
				userInfo: {},
        showPic1: '',
        showPic2: '',
				showFlag:false,
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {},
		methods: {
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						if (res.data.frontcard) {
							this.showPic1 = this.$server.url.imgUrls + res.data.frontcard;
							this.showPic2 = this.$server.url.imgUrls + res.data.backcard;
						}
						this.form.true_name = res.data.realname;
						this.form.card_id = res.data.id_card;

						//0未认证 1已实名 2审核失败 3审核中
						if (res.data.is_true == 1 || res.data.is_true == 3) {
							this.isEdit = false;
						}
						if (res.data.is_true == 0 || res.data.is_true == 2) {
							this.isEdit = true;
						}
					}
					this.showFlag = true
				});
			},
			submit() {
				if (!this.form.true_name) {
					this.$toast("請輸入姓名");
					return;
				}
				if (!this.form.card_id) {
					this.$toast("請輸入身分證號");
					return;
				}

				if (!this.form.frontcard || !this.form.backcard) {
					this.$toast("請上傳您的身分證照片");
					return;
				}
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/user/shiming", {
						id_card: this.form.card_id,
						realname: this.form.true_name,
						frontcard: this.form.frontcard,
						backcard: this.form.backcard,
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							this.initData();
						}
					});
			},
			uploadFile(e, type) {
				var file = e.target.files[0];
				var that = this;
				var formdata = new FormData();
				formdata.append("card", file);
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/common/upload1", formdata)
					.then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast("上傳成功");
							if (type == 1) {
								// 正面
								this.showFrontcard = this.$server.url.imgUrls + res.data; //显示用
								this.form.frontcard = res.data; //提交用
							} else {
								// 反面
								this.showBackcard = this.$server.url.imgUrls + res.data;
								this.form.backcard = res.data;
							}
						}
					})
					.catch((data) => {});
			},
		},
	};
</script>

<style scoped lang="less">
	.inp {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		opacity: 0;
	}

	.page {
		padding: 0.5rem 0.15rem 0.2rem;
		background: #f7f7f7;
		min-height: 100vh;
		.edited {
			.top {
				padding: 0.3rem 0;
				.icon {
					margin: 0 auto 0.15rem;
				}
				.t {
					text-align: center;
					margin-top: 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #000000;
				}
			}

			.t1 {
				font-weight: 600;
				color: #0e1028;
				margin-bottom: 0.05rem;
			}

			.ipt {
				border-radius: 0.04rem;
				margin-bottom: 0.2rem;
				padding: 0.1rem;
				font-weight: 500;
				font-size: 0.12rem;
				color: #1b1b1b;
				border: 0.01rem solid #cecece;
			}

      .upload {
        padding: 0.1rem 0;
        .t {
          margin: 0.1rem 0;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 0.15rem;
          color: #333333;
        }

        .item {
          position: relative;
          text-align: center;
          background: #FFFFFF;
          box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
          border-radius: 0.07rem;
          height: 1.03rem;
          width: 48%;

          img {
            width: 100%;
            height: 100%;
            border-radius: 0.12rem;
            object-fit: contain;
          }

          .icon {
            margin: 0 auto 0.1rem;
          }

          .t1 {
            font-size: 0.12rem;
            text-align: center;
          }
        }
      }
		}

		.editing {
			.txt {
				margin: 0.15rem 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #333333;
			}
			.bg{
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
				border-radius: 0.07rem;
				padding: 0.1rem;
			}
			.top {
				padding: 0.3rem 0;
				.icon {
					margin: 0 auto 0.15rem;
				}
				.t {
					text-align: center;
					font-weight: 500;
					font-size: 0.16rem;
					color: #444444;
				}
			}
			.ipt {
				padding: 0 0 0.2rem;
				.t1 {
					font-weight: 600;
					color: #0e1028;
				}
				input {
					margin-top: 0.05rem;
					width: 100%;
					background: transparent;
					height: 0.42rem;
					border-bottom: 0.01rem solid #cecece;
					&::placeholder {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #182133;
					}
				}
			}

			.upload {
				padding: 0.1rem 0;
				.t {
					margin: 0.1rem 0;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #333333;
				}

				.item {
					position: relative;
					text-align: center;
					background: #FFFFFF;
					box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
					border-radius: 0.07rem;
					height: 1.03rem;
					width: 48%;

					img {
						width: 100%;
						height: 100%;
						border-radius: 0.12rem;
						object-fit: contain;
					}

					.icon {
						margin: 0 auto 0.1rem;
					}

					.t1 {
						font-size: 0.12rem;
						text-align: center;
					}
				}
			}

			.b-btn {
				margin: 0.1rem 0;
			}
		}

		.tips {
			font-size: 0.12rem;
			line-height: 0.2rem;
			margin-top: 0.2rem;
			color: #7f7f7f;
		}
	}
</style>