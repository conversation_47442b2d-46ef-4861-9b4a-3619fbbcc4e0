<template>
	<div class="page text-center">
		<div class="logo" style="opacity: 0">
			<div>
				<img class="img" :src="$cfg.logo" />
				<div class="title">{{$cfg.title}}</div>
			</div>
		</div>
		<!-- <div class="loadingImg">
			<img src="../../assets/login/startLoading.png" />
		</div> -->
		<div class="bottom">
			<div class="icon jzz animate__animated animate__rotateOut animate__infinite" v-if="false"></div>

			<div class="flex-1">
				<div class="txt">{{ $t("new").t38 }}({{ percent }}%)</div>
				<!--  color="linear-gradient(to right, #6970AF, #C4C2EF)" -->
				<div class="line">
					<van-progress stroke-width="8" track-color="#F5F5F5" color="#E10414" pivot-text="" :percentage="percent" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "start",
		props: {},
		data() {
			return {
				cfg: {},
				percent: 10,
			};
		},
		components: {},
		mounted() {
			this.setInt();
			
		},
		methods: {
			setInt() {
				setTimeout(() => {
					this.percent = 25;
					setTimeout(() => {
						this.percent = 50;
						setTimeout(() => {
							this.percent = 75;
							setTimeout(() => {
								this.percent = 100;
								setTimeout(() => {
									this.startTime();
								}, 500);
							}, 500);
						}, 500);
					}, 500);
				}, 500);
			},
			startTime() {
				if (localStorage.getItem("tokend") && localStorage.getItem("account")) {
					this.$toPage("/home/<USER>");
				} else {
					this.$toPage("/login/start1");
				}
			},
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post('/common/config', {
					type: 'all'
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/logo.png");
				}
				this.cfg = val;
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		min-height: 100vh;
		background: url("../../assets/start124.png");
    background-size: cover;
		.logo {
			width: 100%;
			height:3.66rem;
			// background:url('../../assets/login/startLoading2.png') center no-repeat;
			background-size:cover;
			font-weight: 800;
			font-size: .2rem;
			color: #FFFFFF;
			
			img {
				width: 1.17rem;
				object-fit: contain;
				margin-bottom: .2rem;
			}
		}
		.loadingImg{
			margin-top:.2rem;
			img{width:1.92rem;}
		}
		.bottom {
			width: 100%;
			position: fixed;bottom:.4rem;left:0;
			.txt {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #707070;
				margin: 0.1rem 0;
				text-align: center;
			}
			.line{
				padding:0 .3rem;
			}
		}
	}
</style>