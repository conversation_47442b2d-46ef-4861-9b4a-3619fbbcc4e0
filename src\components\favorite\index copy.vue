<template>
  <!-- 自选 -->
  <div class="page ">
    <div class="header flex flex-b">
      <div class="icon logo animate__animated animate__fadeIn">
        <img :src="$cfg.logo" />
      </div>

      <div class="flex">
        <div
          class="icon sou2 animate__animated animate__fadeIn"
          @click="$toPage('/favorite/search')"
        ></div>
        <div
          class="icon tz animate__animated animate__fadeIn"
          @click="$toPage('/information/userInfo')"
        ></div>
      </div>
    </div>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="26" :loading="loading">
        <div class="title flex flex-b">
          <div>{{ $t("new").a1 }}</div>

          <template v-if="chooseList.length">
            <div
              class="icon bjbtn animate__animated animate__fadeIn"
              v-if="!show"
              @click="changeList"
            ></div>
            <div v-if="show" @click="cancle">{{ $t("new").a2 }}</div>
          </template>
        </div>

        <!-- 指数列表 -->
        <div class="index">
          <!-- <div class="t">
            {{ $t("newt").t47 }}
          </div> -->
          <div class="nums flex flex-b">
            <div
              class="nums-item"
              :class="{ center: i == 1 }"
              v-for="(item, i) in list"
              :key="i"
              @click="
                $toDetail(`/market/stockDetailzs?symbol=${item.stock_id}`, item)
              "
            >
              <div class="name">
                {{ item.ko_name }}
              </div>
              <div class="t flex flex-c" :class="{ die: item.gain < 0 }">
                {{ $formatMoney(item.close, 2) }}
                <div
                  class="icon animate__animated animate__fadeIn"
                  :class="item.gain < 0 ? 'down1' : 'up1'"
                ></div>
              </div>
              <div class="t1" :class="{ die: item.gain < 0 }">
                {{ item.gain <= 0 ? "" : "+"
                }}{{ $formatMoney(item.gainValue, 2) }} ({{
                  item.gain <= 0 ? "" : "+"
                }}{{ item.gain }}%)
              </div>
            </div>
          </div>
        </div>

        <div class="cot">
          <no-data v-if="isShow"></no-data>

          <div class="list" v-if="chooseList.length">
            <div class="flex flex-b titles">
              <div class="flex-1">{{ $t("newt").t57 }}</div>
              <div class="flex-1 t-r">{{ $t("newt").t58 }}</div>
              <div class="flex-1 t-r flex flex-e" @click="changeListup">
                {{ $t("newt").t59 }}
                <div class="icon" :class="isUp ? 'zf' : 'df'"></div>
              </div>
            </div>

            <div
              class="list-item flex flex-b"
              v-for="(item, index) in chooseList"
              :key="index"
              @click="
                $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)
              "
            >
              <div class="flex-1 flex">
                <div
                  v-if="item.showIcon"
                  class="icon wxz animate__animated animate__fadeIn"
                  :class="{ xz: item.choose }"
                  @click.stop="changeItem(index)"
                ></div>
                <div>
                  <div class="name">{{ item.name || "-" }}</div>
                  <div class="code">{{ item.symbol || "-" }}</div>
                </div>
              </div>
              <div class=" price flex flex-e flex-1">
                {{ $formatMoney(item.price) || 0 }}
                <div
                  class="icon down animate__animated animate__fadeIn"
                  :class="{
                    up: item.gain > 0,
                  }"
                ></div>
              </div>
              <div
                class="flex-1 per red t-r"
                :class="{
                  green: Number(item.gain) < 0,
                }"
              >
                <div class="t">
                  {{ item.gainValue > 0 ? "+" : "" }}{{ $formatMoney(item.gainValue) || 0 }}
                </div>
                <div class="t">
                  {{ item.gain > 0 ? "+" : "" }}{{ item.gain || 0 }}%
                </div>
              </div>
            </div>
          </div>

          <div class="btns flex flex-b" v-if="show">
            <div class="btn btn2 " @click="chooseAll">
              {{ $t("new").a3 }}
            </div>
            <div class="btn btn1" @click="delItem">
              {{ $t("new").a4 }}
            </div>
          </div>

          <div class="btn-box" v-if="!show">
            <div class="b-btn" @click="$toPage('/favorite/search')">
              {{ $t("new").t36 }}
            </div>
          </div>
        </div>
      </van-skeleton>
    </van-pull-refresh>
    <tab-bar :current="3"></tab-bar>

    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "favorite",
  props: {},
  data() {
    return {
      isUp: false, //默认涨幅在前
      list: [],
      loading: true,
      loading1: true,
      isShow: false,
      isLoading: false,
      show: false,
      chooseList: [],
      lastSymbol: "",
    };
  },
  computed: {},
  created() {
    this.getMine();
    this.getIndexList();
  },
  mounted() {
    // this.$refs.firstLoading.open();
  },
  methods: {
    onRefresh() {
      this.isShow = false;
      this.getMine();
    },
    changeList() {
      if (!this.chooseList.length) return;
      this.chooseList.forEach((item) => {
        item.showIcon = true;
      });
      this.show = true;
    },
    cancle() {
      this.chooseList.forEach((item) => {
        item.showIcon = false;
      });
      this.show = false;
    },
    changeItem(index) {
      this.chooseList.forEach((item, i) => {
        if (index == i) {
          item.choose = !item.choose;
        }
      });
    },
    chooseAll() {
      this.chooseList.forEach((item) => {
        item.choose = true;
      });
    },
    delItem() {
      let arr = this.chooseList.filter((item) => item.choose);
      if (arr.length) {
        this.lastSymbol = arr[arr.length - 1].symbol; //记录已选中最后一项的值
        // console.log("lastSymbol", this.lastSymbol);
        this.$refs.loading.open(); //开启加载
        arr.forEach((item) => {
          this.removeOptional(item);
        });
      }
    },
    getIndexList() {
      // 这里默认展示韩国的前三个
      this.$server.post("/transaction/gszk", { type: "gngs" }).then((res) => {
        this.loading1 = false;
        if (res.status == 1) {
          let arr = [];
          res.data.forEach((item) => {
            arr.push({
              ko_name: item.details
                ? item.details.ko_name
                : item.code.toLocaleUpperCase(),
              time: this.$formatDate(
                "YYYY.MM.DD hh:mm:ss",
                new Date().getTime()
              ),
              gainValue: item.close - item.prev_close,
              gain: (
                ((item.close - item.prev_close) / item.prev_close) *
                100
              ).toFixed(2),
              ...item,
            });
          });
          this.list = arr;
        }
      });
    },
    changeListup() {
      this.isUp = !this.isUp;
      let arr = [];
      let arr1 = [];
      this.chooseList.forEach((item) => {
        if (item.gain > 0) {
          arr.push(item);
        } else {
          arr1.push(item);
        }
      });

      if (this.isUp) {
        arr.sort((a, b) => b.gain - a.gain);
        arr1.sort((a, b) => b.gain - a.gain);
        this.chooseList = [...arr, ...arr1]; //涨在前、高在前
      } else {
        arr.sort((a, b) => a.gain - b.gain);
        arr1.sort((a, b) => a.gain - b.gain);
        this.chooseList = [...arr1, ...arr]; //跌在前、低在前
      }
    },
    getMine() {
      this.$server.post("/transaction/Optionallist", { offset: 0 }).then((res) => {
        this.isLoading = false; //下拉刷新状态
        // this.$refs.firstLoading.close(); //关闭初始加载的效果
        this.loading = false;
        if (res.status == 1) {
          if (this.show) {
            // 如果是已经选择删除，但是列表还有数据
            res.data.forEach((item) => {
              item.showIcon = true;
              item.choose = false;
            });
          } else {
            res.data.forEach((item) => {
              item.showIcon = false;
              item.choose = false;
            });
          }

          this.chooseList = res.data;
          this.changeListup(); //重新加载排序，涨在前

          if (!res.data.length) {
            this.show = false;
            this.isShow = true;
          }
          // console.log("this.chooseList ", this.chooseList);
        }
      });
    },
    removeOptional(item) {
      this.$server
        .post("/transaction/removeOptional", { symbol: item.symbol })
        .then((res) => {
          if (res.status == 1) {
            if (this.lastSymbol == item.symbol) {
              this.$refs.loading.close(); //删除最后一项成功，结束加载中
              this.getMine();
            }
          }
        });
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.5rem 0;
  min-height: 100vh;
}
.btn-box {
  padding: 0.2rem 0.1rem;
  .b-btn {
    margin: 0;
  }
}

.index {
  .t {
    font-weight: 500;
    color: #1e1e1e;
    padding: 0 0.1rem 0.1rem;
  }
}

.nums {
  text-align: center;
  padding: 0.1rem 0;
  // border-top: 0.01rem solid #f5f5f5;
  border-bottom: 0.01rem solid #f5f5f5;

  .nums-item {
    width: 32%;
    &.center {
      border-left: 0.02rem solid #bbc5c1;
      border-right: 0.02rem solid #bbc5c1;
    }
    .name {
      font-weight: 600;
      font-size: 0.12rem;
      margin-bottom: 0.05rem;
    }
    .icon {
      margin-left: 0.05rem;
    }
    .t {
      font-weight: 500;
      font-size: 0.16rem;
      color: #c5585e;
      margin: 0.1rem 0;
      &.die {
        color: #4f8672;
      }
    }

    .t1 {
      font-size: 0.12rem;
      color: #c5585e;
      &.die {
        color: #4f8672;
      }
    }
  }
}

.red {
  color: #c04649;
}
.green {
  color: #4f8672;
}

.header {
  width: 100vw;
  height: 0.5rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background: #fff;
  padding: 0 0.1rem;
  .t {
    font-weight: 500;
    font-size: 0.18rem;
    color: #000000;
    text-align: center;
    line-height: 0.5rem;
  }
  .sou2 {
    margin-right: 0.1rem;
  }
}

.title {
  padding: 0.1rem;
  background: #ffffff;
  box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);
  div {
    font-weight: 500;
    color: #181818;
  }
}

.cot {
  .list {
    .titles {
      padding: 0.1rem;
      border-bottom: 0.01rem solid #f5f5f5;
      div {
        font-size: 0.12rem;
        color: #757575;
      }
      .icon {
        margin-left: 0.05rem;
      }
    }
    .list-item {
      padding: 0.1rem;
      // border-bottom: 0.02rem solid #ececec;
      border-bottom: 0.01rem solid #f5f5f5;
      .wxz {
        margin-right: 0.05rem;
      }
      .name {
        font-weight: bold;
        // margin-left: 0.05rem;
      }
      .code {
        font-weight: 500;
        font-size: 0.12rem;
        color: #464646;
        // margin-left: 0.05rem;
      }
      .price {
        font-weight: bold;
        // font-size: 0.18rem;
        .icon {
          margin-left: 0.05rem;
        }
      }
      .per {
        font-weight: bold;
        // font-size: 0.12rem;
      }
    }
  }
}
.btns {
  margin: 0.2rem 0.1rem;
  .btn {
    width: 48%;
    color: #6970af;
    border-radius: 0.06rem;
    font-weight: 500;
    text-align: center;
    padding: 0.1rem 0;
    &.btn1 {
      background: #6970af;
      color: #ffffff;
    }
    &.btn2 {
      border: 0.01rem solid #6970af;
    }
  }
}
</style>
