<template>
	<div class="page ">
		<top-back :title="$t('positionDetail').title"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="bbg">
				<div class="wbg">
					<div class="flex flex-b cy-top" :class="{ red: parseFloat(currentItem.yingkui) >= 0, green: parseFloat(currentItem.yingkui) < 0, }">
						<div class="">
							<div class="name flex">
								{{ item.stock_name || "-" }}
								<span v-if="item.sell_time">{{$t('已平仓')}}</span>
								<span v-else>{{$t('持仓中')}}</span>
							</div>
							<div class="code">{{ item.stock_code || "-" }}</div>
						</div>

						<div>
							<div class="flex">
								<div class="code">{{ $t("position").a2 }}</div>
								<div class="t">{{stockType=='try'?'%':''}}{{ Number(currentItem.gain).toFixed(2) }}{{stockType=='try'?'':'%'}}</div>
							</div>
							<div class="t1">
								{{ Number(currentItem.yingkui) > 0 ? "+" : "" }}
								{{ isNaN(Number(currentItem.yingkui).toFixed(2)) ? 0 : $formatMoney(currentItem.yingkui) }}
							</div>
						</div>
					</div>
				</div>

				<div class="list">
					<div class="cot">
						<div class="title">{{ $t("new").a76 }}</div>
						<div class="list-item" v-if="false">
							<div class="item-left">{{ $t("positionDetail").txt2 }}</div>
							<div class="item-right" v-if="parseInt(item.buyzd) === 1">
								{{ $t("positionDetail").txt14 }}
							</div>
							<div class="item-right" v-if="parseInt(item.buyzd) === 2">
								{{ $t("positionDetail").txt15 }}
							</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt3 }}</div>
							<div class="item-right">{{ item.strategy_num }}</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt5 }}</div>
							<div class="item-right">{{ $formatMoney(item.buy_price, 2) }}</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt6 }}</div>
							<div class="item-right">{{ $formatMoney(item.stock_num, 0) }}</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt4 }}</div>
							<div class="item-right"> {{ $formatDate("DD-MM-YYYY hh:mm", item.buy_time*1000) }} </div>
						</div>
						<div class="list-item" v-if="item.sell_time">
							<div class="item-left">{{ $t("positionDetail").txt13 }}</div>
							<div class="item-right">{{ $formatMoney(item.sell_price, 2) }}</div>
						</div>
						<div class="list-item" v-if="item.sell_time">
							<div class="item-left">{{ $t("positionDetail").txt12 }}</div>
							<div class="item-right">{{ $formatDate("DD-MM-YYYY hh:mm", item.sell_time*1000) }} </div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt7 }}</div>
							<div class="item-right">{{ $formatMoney(item.credit) }}</div>
						</div>
						<!-- <div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt8 }}</div>
							<div class="item-right">{{ $formatMoney(item.ganggang) }}</div>
						</div> -->
						<div class="list-item" v-if="item.sell_time">
							<div class="item-left">{{ $t("positionDetail").txt9 }}</div>
							<div class="item-right">
								{{ $formatMoney( parseFloat(item.market_value) + parseFloat(item.yingkui) ) }}
							</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt10 }}</div>
							<div class="item-right">
								{{ item.type == 1 ? $t("positionDetail").txt17 : $t("positionDetail").txt16 }}
							</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt19 }}</div>
							<div class="item-right">
								{{ item.sell_time ? $formatMoney(item.sell_poundage) || 0 : $formatMoney(item.buy_poundage) || 0 }}
							</div>
						</div>
						<div class="list-item">
							<div class="item-left">{{ $t("positionDetail").txt18 }}</div>
							<div class="item-right">
								{{ parseFloat(item.yingkui) > 0 ? "+" : "" }}
								{{ isNaN(Number(item.yingkui).toFixed(2)) ? "0" : $formatMoney(item.yingkui) }}
								<!-- {{(item.sell_price-item.buy_price)*item.cj_num==0?0:$formatMoney(parseFloat((item.sell_price-item.buy_price)*item.cj_num).toFixed(2))}} -->
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
	</div>
</template>
<script>
	export default {
		name: "positionDetail",
		data() {
			return {
        stockType: 'try',
				isLoading: false,
				currentItem: {},
				item: {},
				statusStr: [
					this.$t("newt").a,
					this.$t("newt").a1,
					this.$t("newt").a2,
					this.$t("newt").a3,
					"",
				],
			};
		},
		computed: {},
		created() {
      this.stockType = this.$route.query.stockType;
			this.currentItem = this.$storage.get("currentItem");
			this.item = this.currentItem;
			//this.getDetail();
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.getDetail();
			},
			// 获取详情
			getDetail() {
				this.$server.post("/trade/stockdetail", {
					id: this.currentItem.id,
					type: this.stockType,
				}).then((res) => {
					this.isLoading = false;
					if (res.status == 1) {
						this.item = res.data;
					}
				});
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.7rem 0rem 0.1rem;
		min-height: 100vh;
	}

	.bbg {
		
		margin: 0 0.15rem;
	}

	.wbg {
		background: #ffffff;
		border-radius: 0.1rem;
		margin-bottom: .15rem;
		.cy-top {
			padding: 0.12rem;

			.name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.18rem;
				color: #333333;
				margin-bottom: 0.05rem;
				span{
					display: inline-block;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #E10414;
					padding: .02rem .1rem;
					margin-left:.06rem;
				}
			}
			
			&.red{
				.name{
					span{
						background: #FFE8E9;
						border-radius: 0.04rem;
					}
				}
			}
			
			&.green{
				.name{
					span{
						color: #00bb51;
						background: rgba(0, 187, 81, 0.1);
					}
				}
			}
			.code {
				font-size: 0.12rem;
				color: #989898;
			}

			.t {
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.13rem;
				text-align: right;
			}

			.t1 {
				font-size: 0.12rem;
				margin-top: 0.05rem;
				text-align: right;
			}
		}

	}

	.list {
		background: #ffffff;
		border-radius: 0.1rem;
		.cot {
			padding: 0.05rem 0;
			.title{
				padding: 0.12rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.18rem;
				color: #333333;
			}
			.list-item {
				display: flex;
				align-items: center;
				padding: 0.12rem;

				.item-left {
					width: 40%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #999999;
				}

				.item-right {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #333333;
				}
			}
		}
	}
</style>