<template>
	<div class="page ">
		<top-back :title="$t('new').b"></top-back>

		<div class="money animate__animated animate__fadeIn text-center">
			<div class="" v-if="false">
				<div class="t"> {{ $t("newt").a76 }} </div>
				<div class="t1">{{$currency}}{{ $formatMoney(totalAssets) }} </div>
			</div>
			<div class="flex flex-b">
				<div class="flex-1">
					<div class="t"> {{ $t("可用资金") }} </div>
					<div class="t2">{{$currency}}{{ $formatMoney(userInfo.try) }} </div>
				</div>
				<div class="flex-1" v-if="false">
					<div class="t"> {{ $t("冻结资金") }} </div>
					<div class="t2">{{$currency}}{{ $formatMoney(freezeAssets) }} </div>
				</div>
			</div>
		</div>

		<div class="cot">
			<div class="ipt">
				<div class="t"> {{ $t("充值金额") }} </div>
				<div class="money-list">
					<!-- 充值金额 -->
					<div class="inner flex flex-b">
						<div class="money-item" v-for="(item, index) in moneyList" :key="index"
							:class="{ active: currmentIndex == index }" @click="changeMoney(index)">
							{{ $formatMoney(item.money) }}
						</div>
					</div>
				</div>
				
			</div>

			<div class="ipt">
				<div class="t">
					{{ $t("自定义金额") }}
				</div>
				<div class="flex flex-b">
					<input class="input flex-1" v-model="money" type="number" :placeholder="$t('请输入充值金额')" />
				</div>
			</div>

<!--			<div class="ipt" v-if="rpassword">-->
<!--				<div class="t">-->
<!--					{{ $t("资金密码") }}-->
<!--				</div>-->
<!--				<div class="flex flex-b">-->
<!--					<input class="input flex-1" v-model="password" type="password" :placeholder="$t('请输入资金密码')" />-->
<!--				</div>-->
<!--			</div>-->

			<div class="big_btn animate__animated animate__fadeIn" @click="toKe">
				{{ $t("new").a30 }}
			</div>

			<div class="tips">
				<div class="t1">
					{{ $t("new").a43 }}：{{ $t("new").a44 }}{{ open }}-{{ close }}
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "recharge",
		props: {},
		data() {
			return {
				loading: true,
				open: "",
				close: "",
				currmentIndex: -1,
				money: "",
				userInfo: {},
				moneyList: [{
						money: "10000"
					},
					{
						money: "50000"
					},
					{
						money: "500000"
					},
					{
						money: "1000000"
					},
					{
						money: "2000000"
					},
					{
						money: "5000000"
					},
				],
				type: 0,
				minMoney: 0,
				minrecharge: 0,
				password: "",
				rpassword: "",
				totalAssets: 0, //总资产
				freezeAssets: 0, //冻结资金
			};
		},
		components: {},
		created() {
			this.getTotalAssets();
			this.getUserInfo();
			this.getConfig();
		},
		computed: {},
		methods: {
      async toKe() {
        this.$refs.loading.open();
        const res = await this.$server.post("/common/config", {
          type: "all"
        });
        let val = {};
        res.data.forEach((vo) => {
          val[vo.name] = vo.value;
        });
        // this.kfUrl = val.kefu;
        this.$refs.loading.close();
        this.$openUrl(val.kefu); //重新获取
      },
			changeMoney(index) {
				this.currmentIndex = index;
				this.money = this.moneyList[index].money.replace(/,/g, "");
			},
			chongzhi() {
				// 测试跳转通道页
				//   setTimeout(() => {
				//     this.$toPage(
				//       `/information/rechargeChannel?money=${this.money}&type=${this.type}`
				//     );
				//   }, 100);
				//   return;

				if (this.userInfo.is_true !== 1) {
					this.$toast(this.$t("new").a45);
					setTimeout(() => {
						this.$toPage("/information/authInfo");
					}, 2000);
					return;
				}

				if (!this.money) {
					this.$toast(this.$t("new").a42);
					return;
				}

				let val = parseInt(this.money.replace(/\,/g, ""));
				if (val < this.minrecharge) {
					this.$toast(this.$t("new").a46 + this.$formatMoney(this.minrecharge));
					return;
				}

				let parmes = {
					money: val,
					type: this.$stockType,
				};

				// 有通道密码
				if (this.rpassword) {
					if (!this.password) {
						this.$toast(this.$t("请输入资金密码"));
						return;
					}

					parmes = {
						money: val,
						type: this.$stockType,
						rpassword: this.password,
					};
				}

				this.$refs.loading.open(); //开启加载
				this.$server.post("/user/ischongzhi", parmes).then((res) => {
					this.$refs.loading.close();

					if (res.status == 1) {
						//   this.$toast(this.$t("充值请联系客服"));
						//   return;

						setTimeout(() => {
							this.$toPage(
								`/information/rechargeChannel?money=${this.money}&type=${this.type}`
							);
						}, 2000);
					}
				});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo").then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						this.isChg = !!res.data.passwords;
					}
				});
			},
			getConfig() {
				this.$server.post("/common/config", {
					type: this.$stockType,
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							var row = list[a];
							if (row.name === "srecharge") {
								this.open = row.value;
							}
							if (row.name === "erecharge") {
								this.close = row.value;
							}
							if (row.name === "minrecharge") {
								this.minrecharge = row.value;
							}
							// if (row.name === 'kefu') {
							//   this.customer = row.value;
							// }

							if (row.name === "rpassword") {
								this.rpassword = row.value;
							}


						}
					}
				});
			},
			// 获取总资产
			async getTotalAssets() {
				//this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo");
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let krw = Number(res.data.Germany || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: this.$stockType,
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: this.$stockType,
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 日内交易的申请冻结 接口报错
				const res3 = await this.$server.post("/trade/urnjylist", {
					type: this.$stockType,
				});
				let dayDelFreeze = 0;
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					res3.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit));
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDelFreeze = total;
				}

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: this.$stockType,
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 日内交易持仓
				const res6 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					buy_type: 1,
				});
				let dayDealFreeze = 0;
				delete res6.data.ccsz;
				delete res6.data.fdyk;
				let dataArr1 = res6.data;
				if (dataArr1.length) {
					let arr = [];
					dataArr1.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					followingFreeze +
					dayDelFreeze +
					bigDealFreeze +
					positionFreeze +
					dayDealFreeze;
				// 总资产
				this.totalAssets = krw + this.freezeAssets;
				// /this.$refs.loading.close();


			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0.1rem 0;
		min-height: 100vh;
	}

	.tips {
		padding: 0.15rem 0;
		.t {
			font-size: 0.12rem;
			color: #636363;
		}

		.t1 {
			font-size: 0.12rem;
			color: #636363;
		}
	}

	.money {
		padding: 0.2rem 0;
		.t {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #999999;
		}

		.t1 {
			font-weight: 500;
			font-size: 0.18rem;
			color: #272727;
		}
		.t2 {
			margin-top: 0.1rem;
			font-family: OPPOSans, OPPOSans;
			font-weight: 600;
			font-size: 0.22rem;
			color: #333333;
		}

	}

	.cot {
		.mt10 {
			margin-top: .1rem;
		}

		.money-list {
			padding: 0.12rem;
			/* border-radius: .06rem;
			border: 1px solid #E7E7E7;
			padding:.12rem .12rem 0; */
			.title {
				font-weight: 600;
				font-size: 0.14rem;
				color: #000000;
				margin-bottom: 0.1rem;
			}

			.inner {
				flex-wrap: wrap;
				.money-item {
					width: 32%;
					height: 0.36rem;
					background: rgba(225,4,20,0.05);
					border-radius: 0.04rem;
					line-height: 0.36rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #333333;
					text-align: center;
					margin-bottom: 0.1rem;

					&.active {
						color: #fff;
						background: #E10414;
						;
					}
				}
			}
		}

		.ipt {
			margin-top: 0.1rem;
			background: #FFFFFF;
			border-radius: 0.13rem;
			.t {
				padding: 0.12rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
				color: #0C061C;
				border-bottom: 0.01rem solid #E4EAF1;
			}

			.input {
				margin: 0.12rem;
				background: transparent;
				margin-top: 0.1rem;
				border-radius: 0.04rem;
				background: #F5F7FA;
				padding: 0.15rem 0.1rem;

				&::placeholder {
					font-size: 0.14rem;
					color: #909090;
				}
			}
		}
	}
</style>