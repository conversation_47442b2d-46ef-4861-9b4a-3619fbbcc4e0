<template>
	<div class="pages">
		<!-- <van-list v-model="loading" :finished="finished" :finished-text="$t('new').a51" :loading-text="$t('new').a"
			@load="onLoad"> -->
			<no-data v-if="!articleList.length"></no-data>
			<!-- 单独展示前两个 -->
			<div class="one-list flex flex-b">
				<div class="one-item" v-for="(item, index) in articleList" :key="index" @click="toNewsDetail(item)" v-show="index < 2">
					<img v-if="item.img" :src="item.img" alt="" />
					<img v-else src="../../../assets/home/<USER>" alt="" />

					<div class="t">{{ item.title }}</div>
					<div class="time">
						{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created*1000) }}
					</div>
				</div>
			</div>
			<div class="news-list" v-if="articleList.length">
				<div class="news-item" v-for="(item, index) in articleList" :key="index" @click="toNewsDetail(item)"
					v-show="index >= 2">
					<div class="flex flex-b">
						<img v-if="item.img" :src="item.img" alt="" />
						<img v-else src="../../../assets/home/<USER>" alt="" />
						<div class="flex-1">
							<div class="t">{{ item.title }}</div>
							<div class="time">
								{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created*1000) }}
							</div>
						</div>
						
						
					</div>
				</div>
			</div>
			<!-- </van-list> -->
			<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newsList",
		props: {},
		data() {
			return {
				isLoading: false,
				loading: false,
				finished: false,
				articleList: [],
				page: 0,
			};
		},
		components: {},
		created() {},
		mounted() {
			this.getNews();
		},
		computed: {},
		methods: {
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "twd"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			onLoad() {
				// this.page += 1;
				// this.getNews();
			},
			getNews() {
				this.$refs.loading.open();

				this.$server
					.post("/common/newss", {
						exchange: "tw",
						lang: "cn",
					})
					.then((res) => {
						this.$refs.loading.close();
						this.isLoading = false; //下拉刷新状态
						this.loading = false;
						let arr = res.data.result;

						// this.articleList = [...this.articleList, ...arr];
						this.articleList = arr;
						if (arr.length == 0) {
							this.finished = true; //结束列表加载
						}
					});
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
		},
	};
</script>

<style scoped lang="less">
	.pages {
		margin: 0 0.1rem;
		padding: 0.1rem;
		background: #FFFFFF;
		box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
		border-radius: 0.07rem;
	}

	.one-list {
		margin-bottom: 0.1rem;
		.one-item {
			width: 48%;

			img {
				width: 100%;
				border-radius: 0.08rem;
			}

			.t {
				// font-weight: 600;
				color: #000000;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}

			.time {
				font-size: 0.12rem;
				color: #6f6f6f;
				margin-top: 0.1rem;
			}
		}
	}

	.news-list {
		.news-item {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #ededed;

			&:last-child {
				border-bottom: 0;
			}

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #182133;
			}

			.time {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #5D6C8D;
				margin-top: 0.1rem;
			}

			img {
				width: 0.8rem;
				height: 0.5rem;
				border-radius: 0.04rem;
				margin-right: 0.1rem;
			}
		}
	}
</style>