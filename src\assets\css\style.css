@charset "utf-8";
html,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
input,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: baseline;
  background: transparent;
}
body {
  min-width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #000000;
}
* {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  box-sizing: border-box;
  font-size: 0.14rem;
  font-weight: 500;
  font-family: PingFang SC, PingFang SC;
}
.van-dialog__header {
  color: #000;
}
.van-pull-refresh {
  overflow: unset !important;
}
.flex {
  display: flex;
  align-items: center;
}
.flex.flex-b {
  justify-content: space-between;
}
.flex.flex-c {
  justify-content: center;
}
.flex.flex-e {
  justify-content: flex-end;
}
.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.t-center,
.t-c {
  text-align: center;
}
.t-right,
.t-r {
  text-align: right;
}
.no-data {
  text-align: center;
  margin: 1rem 0;
}
.top-pad {
  padding-top: 0.6rem;
}
.b-btn {
  background: #549D7E;
  border-radius: 0.04rem;
  height: 0.4rem;
  line-height: 0.4rem;
  text-align: center;
  font-size: 0.12rem;
  color: #FFFFFF;
  margin-top: 0.3rem;
}
.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.icon.bg {
  width: 100%;
  height: 1.65rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zy1 {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.by {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jl {
  width: 0.24rem;
  height: 0.24rem;
  background: url('../home/<USER>') no-repeat center / 100%;
}
.icon.ss {
  width: 0.24rem;
  height: 0.24rem;
  background: url('../home/<USER>') no-repeat center / 100%;
}
.icon.addzx {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.cutzx {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.cz {
  width: 0.2rem;
  height: 0.2rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.tx {
  width: 0.2rem;
  height: 0.2rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zj {
  width: 0.2rem;
  height: 0.2rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sx {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.bageye {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.bagby {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.lines {
  width: 0.91rem;
  height: 0.61rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jzxl {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zxl {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sczx {
  width: 0.2rem;
  height: 0.2rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.wxz {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.xz {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.gb {
  width: 0.3rem;
  height: 0.3rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.yxz {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zd {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jzx {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.bjzx {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zq {
  width: 0.08rem;
  height: 0.1rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.dq {
  width: 0.08rem;
  height: 0.1rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.dq {
  width: 0.08rem;
  height: 0.1rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.gm {
  width: 50%;
  height: 0.25rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.hbg {
  width: 100%;
  height: 1.5rem;
  background: url("../home/<USER>") no-repeat;
  background-size: 100% 100%;
}
.icon.zjmx {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jta {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zy {
  width: 0.24rem;
  height: 0.22rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.rjt {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.close {
  width: 0.3rem;
  height: 0.3rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m1 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m2 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m3 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m4 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m5 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m6 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.m7 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jtr {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.tz {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.tz1 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.kf1 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zk {
  width: 0.2rem;
  height: 0.2rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.mby {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.mzy {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.xzl {
  width: 0.22rem;
  height: 0.22rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h1 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h2 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h3 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h4 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h5 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h6 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h7 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h8 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h9 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h10 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h11 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.h12 {
  width: 0.44rem;
  height: 0.44rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zxadd {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.bjbtn {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zf {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.df {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.back {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jt1 {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.dj {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jzz {
  width: 0.58rem;
  height: 0.58rem;
  background: url("../home/<USER>") no-repeat center / 100%;
  margin: 0 auto;
}
.icon.user {
  width: 0.52rem;
  height: 0.52rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.set {
  width: 0.26rem;
  height: 0.26rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jt2 {
  width: 0.22rem;
  height: 0.22rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.checked {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.nocheck {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sf {
  width: 0.34rem;
  height: 0.34rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.add {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jj {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sou {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
  margin-right: 0.05rem;
}
.icon.copy {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.down {
  width: 0.08rem;
  height: 0.05rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.up {
  width: 0.08rem;
  height: 0.05rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.bk {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sz {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.jt {
  width: 0.1rem;
  height: 0.1rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.xl {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.up1 {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.down1 {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sou2 {
  width: 0.2rem;
  height: 0.2rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.tm {
  width: 0.14rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.s1 {
  width: 0.1rem;
  height: 0.1rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.x1 {
  width: 0.1rem;
  height: 0.1rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.ysc {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.gd0 {
  width: 1.26rem;
  height: 0.8rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.gd1 {
  width: 1.26rem;
  height: 0.8rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.gd2 {
  width: 1.26rem;
  height: 0.8rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.dd1 {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.zz1 {
  width: 0.12rem;
  height: 0.12rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.rns {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.kset {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sou2 {
  width: 0.18rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.usd {
  width: 0.34rem;
  height: 0.34rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.qh {
  width: 0.39rem;
  height: 0.39rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.thb {
  width: 0.34rem;
  height: 0.34rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.del {
  width: 0.16rem;
  height: 0.16rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.back1 {
  width: 0.1rem;
  height: 0.18rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.sou1 {
  width: 0.16rem;
  height: 0.17rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.rz {
  width: 0.12rem;
  height: 0.14rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.logo {
  width: 1.35rem;
  height: 0.35rem;
}
.icon.logo img {
  width: 100%;
  height: 100%;
}
.icon.a {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.ac {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.a1 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.ac1 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.a2 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.ac2 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.a3 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.ac3 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.a4 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.ac4 {
  width: 0.24rem;
  height: 0.24rem;
  background: url("../home/<USER>") no-repeat center / 100%;
}
.icon.success {
  width: 0.97rem;
  height: 0.97rem;
  background: url("../login/sc.png") no-repeat center / 100%;
}
