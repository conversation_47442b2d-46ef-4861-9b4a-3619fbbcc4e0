<template>
	<div class="page ">
		<top-back :title="$t('new').b1"></top-back>
		
		<div class="money animate__animated animate__fadeIn text-center">
			<div class="" v-if="false">
				<div class="t"> {{ $t("newt").a76 }} </div>
				<div class="t1">{{$currency}}{{ $formatMoney(totalAssets) }} </div>
			</div>
			<div class="flex flex-b">
				<div class="flex-1">
					<div class="t"> {{ $t("可用资金") }} </div>
					<div class="t2">{{$currency}}{{ $formatMoney(userInfo.try) }} </div>
				</div>
				<div class="flex-1" v-if="false">
					<div class="t"> {{ $t("冻结资金") }} </div>
					<div class="t2">{{$currency}}{{ $formatMoney(freezeAssets) }} </div>
				</div>
			</div>
		</div>
		
		<div class="cot">
			<div class="ipt">
				<div class="title">
					{{ $t("new").a26 }}
				</div>
				<div  class="flex flex-b">
					<input class="input flex-1" @input="money=money.replace(/[^\d]/g,'')" v-model="money" type="number" :placeholder="$t('new').a27" />
				</div>
			</div>
			<div class="ipt">
				<div class="title">
					{{ $t("new").a28 }}
				</div>
				<div class="flex flex-b">
					<input class="input flex-1" v-model="moneyPwd" type="password" :placeholder="$t('new').a29" />
				</div>
			</div>
			
			<!-- 选择银行卡 -->
			<div class="list">
				<div class="list-title" @click="$toPage('/information/addBankCard')">
					{{ $t("new").a25 }}
				</div>
				<div class="list-item" v-for="(item, index) in bankList" :key="index" @click="itemObj(item)">
					<div class="item-name">
						{{ item.bank_name }}
						<span>({{ $t("cashOut").txt6 }}{{ item.bank_num.substring(item.bank_num.length - 4) }})</span>
					</div>
					<div class="icon checked animate__animated animate__fadeIn" v-if="bankId && bankId == item.id">
					</div>
					<div class="icon nocheck animate__animated animate__fadeIn" v-else></div>
				</div>
			</div>
			
			<div class="big_btn animate__animated animate__fadeIn" @click="submit">
				{{ $t("new").a30 }}
			</div>

			<div class="tips" v-if="false">
				<div class="t">{{ $t("new").b32 }}</div>
				<div class="t1">{{ $t("new").b33 }}</div>
				<div class="t1">{{ $t("new").b34 }}</div>
				<div class="t1">{{ $t("new").b35 }}</div>
				<div class="t1">{{ $t("new").b36 }}</div>
				<div class="t1">{{ $t("new").b37 }}</div>
			</div>
		</div>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "cashOut",
		props: {},
		data() {
			return {
				bankList: [],
				userInfo: {},
				isChg: "",
				money: "",
				moneyPwd: "",
				bankId: "",
				totalAssets: 0,//总资产
				freezeAssets: 0,//冻结资金
			};
		},
		components: {},
		created() {
			this.getTotalAssets();
			this.getUserInfo();
			this.initData();
		},
		computed: {},
		methods: {
			itemObj(item) {
				this.bankId = item.id;
			},
			submit() {
				let that = this;
				// 沒有設置密碼前往設置
				if (!this.isChg) {
					this.$toast(this.$t("new").a31);
					setTimeout(() => {
						this.$toPage("/information/fundPass");
					}, 2000);
					return;
				}

				// 没有银行卡跳转添加
				if (this.bankList.length == 0) {
					this.$toast(this.$t("new").a32);
					setTimeout(() => {
						this.$toPage("/information/addBankCard");
					}, 2000);
					return;
				}

				// 如果沒有銀行卡，前往添加
				if (!this.bankId) {
					this.$toast(this.$t("new").a32);
					return;
				}

				if (!this.money) {
					this.$toast(this.$t("new").a33);
					return;
				}

				if (!this.moneyPwd) {
					this.$toast(this.$t("new").a34);
					return;
				}

				if (this.moneyPwd == this.userInfo.passwords) {
					this.$refs.loading.open(); //开启加载
					this.$server
						.post("/user/withdrawal", {
							money: this.money,
							bankid: this.bankId,
							passwords: this.moneyPwd,
							type: this.$stockType,
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.$toast(this.$t("new").a7);
								setTimeout(() => {
									this.$router.go(-1);
								}, 2000);
							}
						});
				} else {
					// 输入的资金密码和设置的不一致
					this.$toast(this.$t("new").a35);
				}
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo").then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						this.isChg = !!res.data.passwords;
					}
				});
			},
			initData() {
				this.$server.post("/user/cardList", {
					size: 200,
					page: 1,
				},(failres) => {
					that.bankList = [];
				}).then((res) => {
					var arr = [];
					for (var i in res.data) {
						var row = res.data[i];
						if (row.bank_name != "TRC" && row.bank_name != "ERC") {
							arr.push(row);
						}
					}
					this.bankList = arr;
				});
			},
			// 获取总资产
			async getTotalAssets() {
				//this.$refs.loading.open(); //开启加载
				
				const res = await this.$server.post("/user/getUserinfo");
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let krw = Number(res.data.Germany || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: this.$stockType,
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}
				
				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: this.$stockType,
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);
				
					subscriptionProfit = total + total1;
				}
				
				// 日内交易的申请冻结 接口报错
				const res3 = await this.$server.post("/trade/urnjylist", {
					type: this.$stockType,
				});
				let dayDelFreeze = 0;
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					res3.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit));
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDelFreeze = total;
				}
				
				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});
				
					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}
				
				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: this.$stockType,
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}
				
				// 日内交易持仓
				const res6 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					buy_type: 1,
				});
				let dayDealFreeze = 0;
				delete res6.data.ccsz;
				delete res6.data.fdyk;
				let dataArr1 = res6.data;
				if (dataArr1.length) {
					let arr = [];
					dataArr1.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDealFreeze = total;
				}
				
				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					followingFreeze +
					dayDelFreeze +
					bigDealFreeze +
					positionFreeze +
					dayDealFreeze;
				// 总资产
				this.totalAssets = krw + this.freezeAssets;
				// /this.$refs.loading.close();
				
				
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.12rem 0;
		min-height: 100vh;
	}

	.tips {
		padding: 0.15rem;

		.t {
			color: #888888;
		}

		.t1 {
			font-size: 0.12rem;
			color: #888888;
			line-height: 0.2rem;
		}
	}
	
	.money {
		padding: 0.2rem 0;
		.t {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #999999;
		}
	
		.t1 {
			font-weight: 500;
			font-size: 0.18rem;
			color: #272727;
		}
		.t2 {
			margin-top: 0.1rem;
			font-family: OPPOSans, OPPOSans;
			font-weight: 600;
			font-size: 0.22rem;
			color: #333333;
		}
	
	}
	
	.cot {
		.mt10{
			margin-top:.1rem;
		}

		.ipt {
			margin-top: 0.1rem;
			background: #FFFFFF;
			border-radius: 0.13rem;
			.title {
				padding: 0.12rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
				color: #0C061C;
				border-bottom: 0.01rem solid #E4EAF1;
			}
		
			.input {
				margin: 0.12rem;
				background: transparent;
				margin-top: 0.1rem;
				border-radius: 0.04rem;
				background: #F5F7FA;
				padding: 0.15rem 0.1rem;
		
				&::placeholder {
					font-size: 0.14rem;
					color: #909090;
				}
			}
		}

		.list {
			margin-top: 0.1rem;
			background: #FFFFFF;
			border-radius: 0.13rem;
			.list-title {
				padding: 0.12rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
				color: #0C061C;
				border-bottom: 0.01rem solid #E4EAF1;
			}

			.list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0.1rem 0.12rem 0.12rem;
				.nocheck {
					width: 0.18rem;
					height: 0.18rem;
					border-radius: 50%;
				}

				.item-name {
					display: flex;
					align-items: center;

					span {
						padding-left: 0.05rem;
					}
				}
			}
		}
	}
</style>