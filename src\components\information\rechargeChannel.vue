<template>
  <div class="page ">
    <top-back title="儲值"></top-back>
    <div class="cot">
      <div class="money">
        <div class="t1 t-c">
          {{ $formatMoney(money) }}
        </div>
        <div class="t flex flex-c">
          付款金額
        </div>
      </div>

      <div class="nav-box flex flex-b" v-if="logList.length > 1">
        <div
          class="nav-item"
          v-for="(item, index) in logList"
          :key="index"
          :class="{ active: currmentIndex == index }"
          @click="changeNav(item, index)"
        >
          通道{{ index + 1 }}
        </div>
      </div>

      <div class="list" v-if="logList[currmentIndex]">
        <div class="item flex flex-b">
          <div class="flex">
            <div class="t">銀行戶名</div>
            <div class="t1">{{ logList[currmentIndex].name || "-" }}</div>
          </div>
          <div
            class="icon copy animate__animated animate__fadeIn"
            @click="copy(logList[currmentIndex].name)"
          ></div>
        </div>

        <div class="item flex flex-b">
          <div class="flex">
            <div class="t">銀行帳號</div>
            <div class="t1">{{ logList[currmentIndex].bankcard || "-" }}</div>
          </div>
          <div
            class="icon copy animate__animated animate__fadeIn"
            @click="copy(logList[currmentIndex].bankcard)"
          ></div>
        </div>

        <div class="item flex flex-b">
          <div class="flex">
            <div class="t">銀行名稱</div>
            <div class="t1">{{ logList[currmentIndex].bankname || "-" }}</div>
          </div>
          <div
            class="icon copy animate__animated animate__fadeIn"
            @click="copy(logList[currmentIndex].bankname)"
          ></div>
        </div>

        <div class="item flex flex-b">
          <div class="flex">
            <div class="t">分行名稱</div>
            <div class="t1">{{ logList[currmentIndex].bankperson || "-" }}</div>
          </div>
          <div
            class="icon copy animate__animated animate__fadeIn"
            @click="copy(logList[currmentIndex].bankperson)"
          ></div>
        </div>

        <div class="item flex flex-b">
          <div class="flex">
            <div class="t">分行代碼</div>
            <div class="t1">{{ logList[currmentIndex].bankcode || "-" }}</div>
          </div>
          <div
            class="icon copy animate__animated animate__fadeIn"
            @click="copy(logList[currmentIndex].bankcode)"
          ></div>
        </div>

        <!-- 上传凭证 -->
        <div class="upload animate__animated animate__fadeIn">
          <div class="up-box flex flex-c">
            <div v-if="!showFrontcard">
              <div class="t">+</div>
              <div class="t1">請上傳憑證</div>
            </div>
            <img v-if="showFrontcard" :src="showFrontcard" />
            <input
              class="inp"
              accept="image/*"
              type="file"
              @change="uploadFile($event)"
            />
          </div>
        </div>

        <div class="b-btn animate__animated animate__fadeIn" @click="chongzhi">
          確認
        </div>
      </div>
    </div>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "rechargeChannel",
  props: {},
  data() {
    return {
      show: false,
      money: "",
      type: 0,
      logList: [
        // {
        //   name: "name",
        //   bankcard: "bankcard",
        //   bankname: "bankname",
        // },
        // {
        //   name: "name",
        //   bankcard: "bankcard",
        //   bankname: "bankname",
        // },
      ],
      currmentIndex: 0,
      showFrontcard: "",
      form: {
        frontcard: "",
      },
      userInfo: {},
    };
  },
  components: {},
  created() {
    this.money = this.$route.query.money;
    // this.type = this.$route.query.type;
    this.initData();
    this.getUserInfo();
  },
  computed: {},
  methods: {
    initData() {
      this.$server
        .post("/common/recharge_channel", { type: "twd" })
        .then((res) => {
          let arr = [];
          for (let key in res.data) {
            let obj = res.data[key];

            if (obj.name) {
              if (obj.name.indexOf(":") > -1) {
                let arr = obj.name.split(":");
                obj.title = arr[0];
                obj.name = arr[1];
              }
              arr.push(obj);
            }
          }
          this.logList = arr;
          this.logList = this.logList.filter((item, index) => index < 4);
        });
    },
    getUserInfo() {
      this.$server.post("/user/getUserinfo", { type: "twd" }).then((res) => {
        if (res.status == 1) {
          this.userInfo = res.data;
        }
      });
    },
    changeNav(item, index) {
      this.currmentIndex = index;
    },
    chongzhi() {
      if (!this.showFrontcard) {
        this.$toast("請上傳憑證");
        return;
      }

      let per = {
        money: this.money,
        type: "twd",
        rjpz: this.form.frontcard,
      };

      this.$refs.loading.open(); //开启加载

      this.$server.post("/user/chongzhi", per).then((res) => {
        this.$refs.loading.close();
        if (res.status == 1) {
          this.$toast(this.$t(res.msg));
          this.showFrontcard = "";
        }
      });
    },
    uploadFile(e) {
      var file = e.target.files[0];
      var that = this;
      var formdata = new FormData();
      formdata.append("card", file);
      this.$refs.loading.open(); //开启加载

      this.$server
        .post("/common/upload1", formdata)
        .then((res) => {
          this.$refs.loading.close();
          if (res.status == 1) {
            this.$toast("上傳成功");
            // 正面
            this.showFrontcard = this.$server.url.imgUrl + res.data; //显示用
            this.form.frontcard = res.data; //提交用
          }
        })
        .catch((data) => {});
    },
    copy(text) {
      let textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.readOnly = "readOnly";
      document.body.appendChild(textarea);
      textarea.select(); // 选中文本内容
      textarea.setSelectionRange(0, text.length); // 设置选定区的开始和结束点
      this.$toast("複製成功");
      var result = document.execCommand("copy"); //将当前选中区复制到剪贴板
      textarea.remove();
    },
  },
};
</script>

<style scoped lang="less">
.inp {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  opacity: 0;
}
.page {
  padding: 0.5rem 0rem 0;
  min-height: 100vh;
}

.cot {
  .money {
    padding: 0.1rem 0rem 0.2rem;
    .t {
      color: #6b6b6b;
      margin-top: 0.1rem;
      .icon {
        margin-left: 0.05rem;
      }
    }
    .t1 {
      font-family: Poppins, Poppins;
      font-weight: bold;
      font-size: 0.3rem;
      color: #39324b;
    }
  }

  .money1 {
    border-bottom: 0.01rem solid #ececec;
    padding: 0 0.15rem 0.15rem;
    .t {
      font-size: 0.12rem;
      color: #606060;
    }
    .t1 {
      font-weight: 500;
      color: #333333;
    }
  }

  .nav-box {
    padding: 0 0.1rem;
    background: #ffffff;
    // box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);
    margin: 0 0 0.15rem;
    .nav-item {
      padding: 0.1rem 0;
      flex: 1;
      font-size: 0.12rem;
      color: #8e8e8e;
      text-align: center;
      position: relative;
      &::after {
        content: "";
        width: 50%;
        height: 0.02rem;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        background: transparent;
      }
      &.active {
        color: #549d7e;
        &::after {
          background: #549d7e;
        }
      }
    }
  }

  .list {
    padding: 0 0.15rem 0.1rem;
    .item {
      padding: 0.15rem 0.1rem;
      border: 0.01rem solid #cecece;
      border-radius: 0.04rem;
      margin-bottom: 0.15rem;
      .t {
        font-size: 0.12rem;
        color: #909090;
      }
      .t1 {
        font-size: 0.14rem;
        color: #549d7e;
        margin-left: 0.05rem;
      }
    }
  }
  .upload {
    padding: 0.2rem 0 0;
    .up-box {
      width: 1.2rem;
      height: 1.2rem;
      position: relative;
      margin: 0 auto;
      border-radius: 0.04rem;
      border: 0.01rem solid #cecece;

      text-align: center;
      .t {
        font-weight: bold;
        font-size: 0.2rem;
      }
      .t1 {
        font-weight: 500;
        font-size: 0.12rem;
        color: #353535;
      }
      img {
        width: 1rem;
        height: 1rem;
      }
    }
  }
}
</style>
