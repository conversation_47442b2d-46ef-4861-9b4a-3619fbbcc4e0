<template>
	<div class="page">
		<top-back title="指数"></top-back>
		<van-skeleton title :row="6" :loading="loading1">
			<div class="zx-cot">
				<div class="zx-list">
					<div class="zx-item flex flex-b" v-for="(item, i) in indexList" :key="i">
						<div class="name">{{ item.symbol }}</div>
						<div class="price" :class="Number(item.gain) > 0 ? 'red' : 'green'">
							{{ $formatMoney(item.price) }}
						</div>
						<div class="per" :class="Number(item.gain) > 0 ? 'red' : 'green'">
							<div class="flex">
								<div class="icon" :class="Number(item.gain) > 0 ? 'up' : 'down'"></div>
								{{ $formatMoney(item.gainValue) }}
							</div>
							<div class="flex">
								{{ item.gain > 0 ? "+" : "" }}{{ item.gain }}%
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-skeleton>
	</div>
</template>

<script>
	export default{
		data(){
			return{
				indexList: [],
				loading1:true,
				marketType: 'taiwan', // 默认台湾市场
			}
		},
		created(){
			const market = this.$route.query.market;
			if (market === 'us') {
				this.marketType = 'us';
			} else {
				this.marketType = 'taiwan';
			}
		},
		computed: {
			apiMarketType() {
				return this.marketType === 'taiwan' ? 'twd' : 'usd';
			}
		},
		mounted() {
			this.getIndexList();
		},
		methods:{
			getIndexList() {
				this.$server
					.post("/parameter/zhishu", {
						type: this.apiMarketType
					})
					.then((res) => {
						this.loading1 = false;
						if (res && res.data) {
							this.indexList = res.data;
							// /parameter/zhishu 返回数据字段：symbol, price, gain, gainValue, icon
						}
					});
			},
		}
	}
</script>

<style lang="less" scoped>
	.page{
		padding: 0.5rem 0 0;
	}
	.zx-cot {
		margin: 0.08rem;
		.zx-list {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
			border-radius: 0.07rem;
			.zx-item {
				padding: 0.1rem;
				border-bottom: 0.01rem solid #F6F8FE;
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #182133;
				}

				.price {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.17rem;
				}

				.per {
					display: flex;
					flex-direction: column;
					align-items: flex-end;

					div {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
					}

					.icon {
						margin-right: 0.05rem;
					}
				}
			}
		}
	}
</style>