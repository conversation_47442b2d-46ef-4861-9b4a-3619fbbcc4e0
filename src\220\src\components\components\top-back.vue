<template>
	<div class=""><!-- 二三级顶部菜单 -->
		<div class="header flex flex-c" :class="{ login: isLogin }" @click="goBack">
			<div class="icon back animate__animated animate__fadeIn"></div>
			<div class="t">{{ title }}</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "topBack",
		props: {
			title: {
				type: String,
				default: "",
			},
			isLogin: {
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {};
		},
		components: {},
		created() {},
		computed: {},
		methods: {
			goBack() {
				this.$router.go(-1);
				/* let _this = this
				let a = window.history.back()
				if (a == undefined) {
					_this.$router.push({
						path: "/home/<USER>"
					});
				}
				return; */
			},
		},
	};
</script>

<style scoped lang="less">
	.header {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0rem 0.12rem;
		background: #F3F2F2;
		height: 0.5rem;

		&.login {
			background: #fff;
		}

		.icon {
			position: absolute;
			top: .15rem;
			left: .16rem;
		}

		.t {
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.17rem;
			color: #111111;
		}
	}
</style>