<template>
  <div class="page ">
    <top-back title="詳情"></top-back>
    <div class="top">
      <div class="t">{{ info.title }}</div>
      <div class="t1">
        {{ $formatDate("YYYY-MM-DD hh:mm:ss", info.created*1000) }}
      </div>
    </div>

    <!-- <img class="img" :src="info.img" alt="" /> -->

    <div class="cot" v-html="info.content"></div>
  </div>
</template>

<script>
export default {
  name: "newsDetail",
  props: {},
  data() {
    return {
      info: {},
    };
  },
  components: {},
  methods: {},
  created() {
    let data = this.$storage.get("newsDetail");
    let cont = data.content;
    cont = cont.replace(/\&lt;/g, "<");
    cont = cont.replace(/\&gt;/g, ">");
    cont = cont.replace(/\&quot;/g, '"');
    cont = cont.replace(/\<img/g, '<img style="width:100%"');
    this.info = { ...data, content: cont };
  },
  computed: {},
};
</script>

<style scoped lang="less">
.page {
  padding: 0.6rem 0.1rem 0.1rem;
}

.img {
  width: 100%;
  border-radius: 0.04rem;
  margin-bottom: 0.1rem;
}

.top {
  margin-bottom: 0.1rem;
  .t {
    font-weight: 500;
    font-size: 0.14rem;
    color: #000000;
  }
  .t1 {
    font-weight: 500;
    font-size: 0.12rem;
    // color: #ff0000;
    color: #999;
    margin-top: 0.1rem;
  }
}

::v-deep .cot table {
  width: 100% !important;
}

::v-deep .cot .image-hero__padding {
  padding: 0 !important;
}

::v-deep .cot p,
::v-deep .cot div,
::v-deep .cot pre,
::v-deep .cot font {
  width: 100%;
  font-size: 0.14rem;
  font-weight: 500;
  line-height: 0.24rem;
  color: #000;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica,
    Segoe UI, Arial, Roboto, "PingFang SC", miui, "Hiragino Sans GB",
    "Microsoft Yahei", sans-serif;

  &::nth-child(n) {
    width: 100% !important;
  }
}
</style>
