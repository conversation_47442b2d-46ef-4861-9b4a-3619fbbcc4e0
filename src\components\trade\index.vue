<template>
	<div class="page ">
		<div class="topHead">
			<div class="flex flex-b">
				<div class="flex">
					<div class="icon user"></div>
<!--					<div class="name">{{ userInfo.realname }}</div>-->
					<!-- {{userInfo.account}} -->
				</div>
				<div class="top-tab flex">
					<div class="item" :class="{ active: marketIndex === 0 }"  @click="marketIndex=0">台灣</div>
					<div class="item" :class="{ active: marketIndex === 1 }" @click="marketIndex=1">美國</div>
				</div>
				<div class="flex">
					<div class="icon ss" @click="$toPage('/favorite/search')"></div>
					<div class="icon xx" @click="$toPage('/information/userInfo')"></div>
				</div>
			</div>
			<!-- 收益 v-if="currmentIndex === 1 || currmentIndex === 2"-->
			<template>
				<div class="money">
					<div class="flex flex-b tops" v-if="false">
						<div class="flex-1">
							<div class="t flex" @click="show = !show">
								總資產
								<div class="icon animate__animated animate__fadeIn" :class="show ? 'bageye' : 'bagby'">
								</div>
							</div>
							<div class="num">
								{{ show ? $formatMoney(totalAssets) || 0 : "****" }}
							</div>
							<!-- <div class="txt">
								<span class="">{{ $t("今日盈亏") }} </span>
								<span
									:class="percent > 0 ? 'red' : 'green'">{{ percent > 0 ? "+" : "" }}{{ percent }}%</span>
							</div> -->
						</div>
					</div>
					<div class="sz-num ">
						<div class="flex flex-b">
							<div class="t">持倉市值</div>
							<div class="t1">{{ $formatMoney(szAssets) || 0}}</div>
						</div>
						<div class="flex flex-b">
							<div class="t">可用資金</div>
							<div class="t1">{{ $formatMoney(userInfo.twd) || 0}}</div>
						</div>
						<!-- <div>
							<div class="t">盈利資金</div>
							<div class="t1">
								{{ $formatMoney(ykAssets) || 0}}
							</div>
						</div> -->
						<!-- <div class="flex-column-item">
							<div class="t1">{{ $formatMoney(freezeAssets) || 0}}</div>
							<div class="t">凍結資金</div>
						</div> -->
						<div class="flex flex-b">
							<div class="t">總資金</div>
							<div class="t1">{{ $formatMoney(totalAssets) || 0}}</div>
						</div>
					</div>
				</div>
			</template>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<!-- 充值 -->
			<!-- <div class="btns flex flex-b">
				<div class="flex-1" @click="$toPage('/information/recharge')">
					<div class="icon h8"></div>
					<div class="t">儲值</div>
				</div>
				<div class=" flex-1" @click="$toPage('/information/cashOut')">
					<div class="icon h9"></div>
					<div class="1">提領</div>
				</div>
			</div> -->
			<div class="bg">
				<!-- 切换列表内容显示 -->
				<div class="nav-box flex flex-a">
					<div class="nav-item flex flex-c" v-for="(item, i) in navList" :key="i"
						@click="changeNav(item.type)" :class="{ active: currmentIndex == item.type }">
						{{ item.name }}
					</div>
				</div>
				<!-- 申购 -->
				<div class="sg-list" v-if="currmentIndex === 0">
					<van-skeleton title :row="26" :loading="loading1">
						<no-data v-if="!xinguList.length"></no-data>
						<div class="sg-item" v-for="(item, index) in xinguList" :key="index">
							<div class="top flex flex-b">
								<div class="">
									<div class="name">{{ item.stock_name || "-" }}</div>
									<div class="code">{{ item.stock_code || "-" }}</div>
								</div>
								<div class="status animate__animated animate__fadeIn">
									{{ $t(item.xgstate) }}
								</div>
							</div>
							<div class="data flex flex-b">
								<div class="data-item flex flex-b">
									<div class="t">承銷價</div>
									<div class="t1 ">
										{{ $formatMoney(item.apply_price) || "-" }}
									</div>
								</div>
								<div class="data-item flex flex-b">
									<div class="t">中籤張數</div>
									<div class="t1">{{ $formatMoney(Number(item.lucky_total) / 1000, 0) || "-" }}
									</div>
								</div>
								<div class="data-item flex flex-b">
									<div class="t">申購張數</div>
									<div class="t1">{{ $formatMoney(Number(item.apply_total) / 1000, 0) || "-" }}
									</div>
								</div>
								<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
									<div class="t">應認繳金額</div>
									<div class="t1">
										{{ $formatMoney(item.subs_value) || "-" }}
									</div>
								</div>
								<!-- <div class="data-item flex flex-b ">
									<div class="t">報酬率</div>
									<div class="t1 price">{{ item.newstock.rate }}%</div>
								</div> -->
								<div class="data-item flex flex-b ">
									<div class="t">市值</div>
									<div class="t1 price">
										{{item.xgstate == "待中签"? "-": $formatMoney(item.market_value)}}
									</div>
								</div>
								<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
									<div class="t">已認繳金額</div>
									<div class="t1">
										{{ $formatMoney(item.rjmoney) || "-" }}
									</div>
								</div>
							</div>
						</div>
					</van-skeleton>
				</div>
				<!-- 競拍 -->
				<div class="sg-list" v-if="currmentIndex === 4">
					<van-skeleton title :row="26" :loading="loading5">
						<no-data v-if="!xinguListJp.length"></no-data>
						<div class="sg-item" v-for="(item, index) in xinguListJp" :key="index">
							<div class="top flex flex-b">
								<div class="">
									<div class="name">{{ item.stock_name || "-" }}</div>
									<div class="code">{{ item.stock_code || "-" }}</div>
								</div>
								<div class="status animate__animated animate__fadeIn">
									{{ $t(item.xgstate)=='待中籤'?'待中標':$t(item.xgstate)=='未中籤'?'未中標':$t(item.xgstate) }}
								</div>
							</div>

							<div class="data flex flex-b">
								<div class="data-item flex flex-b">
									<div class="t">競拍價</div>
									<div class="t1 ">
										{{ $formatMoney(item.apply_price) || "-" }}
									</div>
								</div>
                <div class="data-item flex flex-b">
                  <div class="t">競拍張數</div>
                  <div class="t1">{{ $formatMoney(Number(item.apply_total) / 1000, 0) || "-" }}
                  </div>
                </div>
								<div class="data-item flex flex-b">
									<div class="t">得標張數</div>
									<div class="t1">{{ $formatMoney(Number(item.lucky_total) / 1000, 0) || "-" }}
									</div>
								</div>
								<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
									<div class="t">得標繳納金額</div>
									<div class="t1">
										{{ $formatMoney(item.subs_value) || "-" }}
									</div>
								</div>
								<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
									<div class="t">已認繳金額</div>
									<div class="t1">
										{{ $formatMoney(item.rjmoney) || "-" }}
									</div>
								</div>
                <div class="data-item flex flex-b">
                  <div class="t">市價</div>
                  <div class="t1" v-if="item.nowPrice">{{ $formatMoney(item.nowPrice) || "-" }}</div>
                  <div class="t1" v-else>-</div>
                </div>
								<div class="data-item flex flex-b " v-if="item.lucky_total">
									<div class="t">市值</div>
									<div class="t1 price">
										{{item.xgstate == "待中签"? "-": $formatMoney(item.lucky_total*item.nowPrice,2) }}
									</div>
								</div>
								<div class="data-item flex flex-b " v-if="item.stock_code">
									<div class="t">報酬率</div>
									<div class="t1 price">
										{{$formatMoney((item.nowPrice-item.apply_price)/item.apply_price*100,2) }}%
									</div>
								</div>

								<!-- <div class="data-item flex flex-b ">
									<div class="t">報酬率</div>
									<div class="t1 price">{{ item.newstock.rate }}%</div>
								</div> -->


							</div>
						</div>
					</van-skeleton>
				</div>
				<!-- 进行中 -->
				<div class="cy-list" v-if="currmentIndex === 1">
					<van-skeleton title :row="26" :loading="loading3">
						<no-data v-if="!positionList.length"></no-data>
						<div class="cy-item" v-for="(item, index) in positionList" :key="index" @click="sellItem(item)">
							<div class="flex flex-b">
								<div>
									<div class="name">{{ item.stock_name }}</div>
									<div class="code">{{ item.stock_code }}</div>
								</div>
								<div class="flex-column-item" style="align-items: flex-end;">
									<div class="flex sy">
										<div class="t">總損益</div>
										<div class="flex"
											:class="{red: parseFloat(item.yingkui) >= 0,green: parseFloat(item.yingkui) < 0,}">
											<div class="per">{{ item.yingkuiBi > 0 ? "+" : "" }}{{ item.yingkuiBi }}%
											</div>
											<div style="margin-left: .1rem;">
												{{ parseFloat(item.yingkui) > 0 ? "+" : ""}}{{ $formatMoney(item.yingkui,2) }}
											</div>
										</div>
									</div>
									<!-- <div class="time">{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.buy_time * 1000) }}	</div> -->
								</div>
							</div>
							<div class="inner flex flex-b">
								<div class="inner-item">
									<div class="t1">總張數</div>
									<div class="t2">{{ $formatMoney(item.stock_num/1000,0) }}</div>
								</div>
								<div class=" inner-item">
									<div class="t1">買入價</div>
									<div class="t2">
										{{ $formatMoney(item.buy_price) }}
									</div>
								</div>
								<div class="inner-item">
									<div class="t1">持倉市值</div>
									<div class="t2">
										{{$formatMoney(parseFloat(item.market_value) + parseFloat(item.yingkui))}}
									</div>
								</div>
								<div class="inner-item">
									<div class="t1">成本</div>
									<div class="t2">
										{{ $formatMoney(item.buy_price * item.stock_num) }}
									</div>
								</div>
							</div>
							<!-- <div class="btn">{{ statusStr[item.status] }}</div> -->
							<div class="flex flex-b btns">
								<div class="btn flex flex-c" @click.stop="goItem(item)">詳情</div>
								<div class="btn flex flex-c bt">
									{{ item.status == 3 ? "掛單中" : "持倉中" }}
								</div>
							</div>
						</div>
					</van-skeleton>
				</div>
				<!-- 已终止 -->
				<div class="cy-list" v-if="currmentIndex === 2">
					<van-skeleton title :row="26" :loading="loading3">
						<no-data v-if="!positionCloseList.length"></no-data>
						<div class="cy-item" v-for="(item, index) in positionCloseList" :key="index"
							@click="goItem(item)">
							<div class="flex flex-b">
								<div>
									<div class="name">{{ item.stock_name }}</div>
									<div class="code">{{ item.stock_code }}</div>
								</div>
								<div class="flex-column-item" style="align-items: flex-end;">
									<div class="flex sy">
										<div class="t">總損益</div>
										<div class="flex"
											:class="{red: parseFloat(item.yingkui) >= 0,green: parseFloat(item.yingkui) < 0,}">
											<div class="per">{{ item.yingkuiBi > 0 ? "+" : "" }}{{ item.yingkuiBi }}%
											</div>
											<div style="margin-left: .1rem;">
												{{ parseFloat(item.yingkui) > 0 ? "+" : ""}}{{ $formatMoney(item.yingkui,2) }}
											</div>
										</div>
									</div>
									<!-- <div class="time">{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.sell_time * 1000) }}</div> -->
								</div>
							</div>
							<div class="inner flex flex-b">
								<div class="flex flex-b inner-item">
									<div class="t1">總張數</div>
									<div class="t2">{{ $formatMoney(item.stock_num/1000,0) }}</div>
								</div>
								<div class="flex flex-b inner-item">
									<div class="t1">成交價</div>
									<div class="t2">
										{{ $formatMoney(item.sell_price) }}
									</div>
								</div>
								<div class="flex flex-b inner-item">
									<div class="t1">持倉市值</div>
									<div class="t2">
										{{ $formatMoney(parseFloat(item.market_value) + parseFloat(item.yingkui) )}}
									</div>
								</div>
								<div class="inner-item">
									<div class="t1">成本</div>
									<div class="t2">
										{{ $formatMoney(item.buy_price * item.stock_num) }}
									</div>
								</div>
							</div>
							<!-- <div class="btn pc">{{ statusStr[item.status] }}</div> -->
							<!-- <div class="flex flex-b btns">
								<div class="btn flex flex-c" @click.stop="goItem(item)">{{ $t("详情") }}</div>
								<div class="btn flex flex-c bt">{{ statusStr[item.status] }}</div>
							</div> -->
						</div>
					</van-skeleton>
				</div>
				<!-- 跟单记录 -->
				<div class="" v-if="currmentIndex === 3">
					<van-skeleton title :row="26" :loading="loading2">
						<no-data v-if="!gdData.length"></no-data>
						<div class="gd-list" v-if="gdData.length">
							<div class="gd-item" v-for="(item, index) in gdData" :key="index">
								<div class="flex flex-b">
									<div class="">
										<div class="name">{{ item.name || "-" }}</div>
										<div class="code">{{ item.sender || "-" }}</div>
									</div>
									<div class="flex flex-e">
										<div class="status" :class="{ end: item.status }">
											{{ item.status ? "已終止" : "進行中" }}
										</div>
									</div>
								</div>
								<div class="center flex flex-b">
									<div class="flex-column-item">
										<div class="t">日盈利</div>
										<div class="flex" :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
											{{ item.dprofit || "-" }}
											<div class="icon" :class="item.dprofit.indexOf('-') > -1 ? 'down' : 'up'">
											</div>
										</div>
									</div>
									<div class="flex-column-item">
										<div class="t">總盈利</div>
										<div :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
											{{ item.aprofit || "-" }}
										</div>
									</div>
								</div>
								<!-- 内层列表 v-if="item.isShow"-->
								<template v-if="false">
									<div class="inner-list" v-if="item.allstock && item.allstock.length">
										<!-- <div class="flex flex-b title">
											<div class="">{{ $t("new").a66 }}</div>
											<div class="t-r">{{ $t("new").a67 }}</div>
										</div> -->
										<div class="inner-item flex flex-b" v-for="(items, i) in item.allstock" :key="i"
											v-if="items">
											<div class="t1">
												{{ items.symbol || items.code || "-" }}
											</div>
											<div class="t-r" :class="items.gain < 0 ? 'green' : 'red'">
												{{ items.gain > 0 ? "+" : "" }}{{ items.gain }}
											</div>
										</div>
									</div>
								</template>
								<!-- <div class="flex flex-b pad10">
									<div class="sylb flex" @click="changeGdshow(index)">
										{{ item.isShow ? $t("收起") : $t("展开") }}
										<div class="icon animate__animated animate__fadeIn"
											:class="item.isShow ? 's1' : 'x1'"></div>
									</div>
								</div> -->
								<!-- 时间 -->
								<div class="time flex flex-b flex-1">
									<div class="">
										<span>買入時間</span>
										{{ $formatDate("YYYY/MM/DD", item.buy_time * 1000) }}
									</div>
									<img src="../../assets/v2/line01.png" style="width: 0.27rem;height: 0.01rem;"
										alt="" />
									<div class="icon tm animate__animated animate__fadeIn"></div>
									<img src="../../assets/v2/line02.png" style="width: 0.27rem;height: 0.01rem;"
										alt="" />
									<div class="">
										<span class="">傳承天數</span>{{ item.scday }}
									</div>
								</div>
								<div class="info">
									<div class="flex flex-b">
										<div class="t1">投資金額</div>
										<div class="t2">{{ $formatMoney(item.money) || 0 }}</div>
									</div>
									<!-- <div class="flex flex-b">
										<div class="t1">買入價格</div>
										<div class="t2">{{ $formatMoney(item.price) || 0 }}</div>
									</div>
									<div class="flex flex-b">
										<div class="t1">買入數量</div>
										<div class="t2">-</div>
									</div> -->
									<div class="flex flex-b">
										<div class="t1">昨日收益</div>
										<div class="t2">{{ item.dprofit || "-" }}</div>
									</div>
									<div class="flex flex-b">
										<div class="t1">總收益</div>
										<div class="t2">{{ item.aprofit || "-" }}</div>
									</div>
									<!-- <div class="flex flex-b">
										<div class="t1">策略單號</div>
										<div class="t2">{{ item.strategy_num || item.buy_time+''+Number(item.money) }}</div>
									</div> -->
								</div>
							</div>
						</div>
					</van-skeleton>
				</div>
			</div>
			<!-- 持仓中弹出层 -->
			<van-popup v-model="show1" round position="center" :style="{ width: '90%' }" :round='true'>
				<div class="popup">
					<div class="flex flex-b top">
						<div class="title">
							{{ statusStr[detailItem.status] }}
						</div>
						<div class="icon close" @click="show1 = false"></div>
					</div>
					<div class="pdd">
						<div class="flex-column-item num">
							<div class="">
								<div class="t num-font" :class="{ red: Number(detailItem.yingkui) >= 0 }">
									{{ Number(detailItem.yingkui) > 0 ? "+" : ""}}{{isNaN(Number(detailItem.yingkui).toFixed(2)) ? 0: $formatMoney(detailItem.yingkui)}}
								</div>
								<div class="t1">當前收益</div>
							</div>
						</div>
						<div class="flex flex-b">
							<div class="bt flex flex-c" @click="show1 = false">取消</div>
							<div class="bt bt1 flex flex-c" @click="sellstrategy(detailItem.id, detailItem.status)">
								{{ detailItem.status == 3 ? "掛單中" : "平倉" }}
							</div>
						</div>
					</div>
				</div>
			</van-popup>
		</van-pull-refresh>
		<tab-bar :current="2"></tab-bar>
		<loading ref="loading" />
	</div>
</template>

<script>
	import * as echarts from "echarts";
	export default {
		name: "trade",
		props: {},
		data() {
			return {
				marketIndex: 0, // 0: 台灣, 1: 美國
				percent: "",
				chartData: [],
				chartData1: [],
				show: false,
				show1: false,
				myChart: null,
				statusStr: ["持倉中", "已平倉", "準備平倉", "掛單中"],
				loading1: true,
				loading2: true,
				loading3: true,
				loading4: true,
				loading5: true,
				isLoading: false,
				cfg: {},
				detailItem: {},
				look: true,
				positionList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	yingkui: 1000,
					// 	yingkuiBi: 1000,
					// 	stock_num: 1000,
					// 	buy_price: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 1,
					// }
				],
				positionCloseList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	yingkui: 1000,
					// 	yingkuiBi: 1000,
					// 	stock_num: 1000,
					// 	buy_price: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 1,
					// }
				],
				gdData: [
					// {
					//   dprofit: "10%",
					//   name: "name",
					//   code: "code",
					//   yprofit: "100%",
					//   isShow: false,
					//   allstock: [
					//     {
					//       symbol: "symbol",
					//       code: "code",
					//       gain: 10,
					//     },
					//   ],
					//   buy_time: "2024-03-02",
					//   locktime: "2024-03-02",
					// },
				],
				xinguList: [
					// {
					//   stock_name: "stock_name",
					//   stock_code: "stock_code",
					//   xgstate: "审核中",
					//   apply_price: 1000,
					//   lucky_total: 1000,
					//   apply_total: 1000,
					//   rjmoney: 1000,
					//   buy_time: "2024-03-02",
					//   market_value: 1000,
					//   status: 1,
					// },
				],
				// 台灣市场导航列表
				taiwanNavList: [{
						name: "申購",
						type: 0,
					},
					{
						name: "競拍",
						type: 4,
					},
					{
						name: "傳承基金",
						type: 3,
					},
					{
						name: "庫存",
						type: 1,
					},
					{
						name: "歷史記錄",
						type: 2,
					},
				],
				// 美國市场导航列表（不包含竞拍）
				usNavList: [{
						name: "申購",
						type: 0,
					},
					{
						name: "傳承基金",
						type: 3,
					},
					{
						name: "庫存",
						type: 1,
					},
					{
						name: "歷史記錄",
						type: 2,
					},
				],
				currmentIndex: 1,
				positionlistinteval: null,
				userInfo: {},
				freezeAssets: 0,
				szAssets: 0,
				ykAssets: 0,
				totalAssets: 0,
				xinguListJp: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	apply_price: 1000,
					// 	lucky_total: 1000,
					// 	apply_total: 1000,
					// 	subs_value: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	rjmoney:20,
					// 	status: 1,
					// }
				],
			};
		},
		components: {},
		computed: {
			// 根据当前选择的市场返回对应的导航列表
			navList() {
				return this.marketIndex === 0 ? this.taiwanNavList : this.usNavList;
			},
			// 根据当前选择的市场返回对应的市场类型
			currentMarketType() {
				return this.marketIndex === 0 ? 'twd' : 'usd';
			}
		},
		watch: {
			// 监听市场切换
			marketIndex(newVal, oldVal) {
				if (newVal !== oldVal) {
					// 重新获取数据
					this.initData();
					this.getTotalAssets();
					// 如果当前在竞拍页面且切换到美國市场，则切换到申购页面
					if (newVal === 1 && this.currmentIndex === 4) {
						this.currmentIndex = 0;
						this.getXingu();
					}
				}
			}
		},
		created() {
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.marketIndex = 1;
			} else {
				this.marketIndex = 0; // 默认台湾
			}

			if(this.$route.query.zqShow){
				this.currmentIndex = 0
				this.getXingu();
			}
			this.getConfig();
			this.initData();
			this.changeTime();
			this.getTotalAssets()
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		beforeDestroy() {
			clearInterval(this.positionlistinteval);
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.getConfig();
				this.initData();
			},
			// 获取配置
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: this.currentMarketType
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/login/logo.png");
				}
				this.cfg = val;
			},
			changeGdshow(index) {
				let arr = this.gdData.map((item, i) => {
					if (index == i) {
						item.isShow = !item.isShow;
					}
					return item;
				});
				this.gdData = arr;
			},
			getGd() {
				this.$server
					.post("/trade/userproductlist", {
						type: this.currentMarketType
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading2 = false;
						if (res.status == 1) {
							res.data.forEach((item) => {
								item.isShow = false;
								if (item.allstock && item.allstock.length) {
									item.allstock = item.allstock.filter(Boolean);
								}
							});
							this.gdData = res.data;
						}
					});
			},
			goItem(item) {
				this.$refs.loading.open(); //开启加载
				this.$storage.save("currentItem", item);
				setTimeout(() => {
					this.$refs.loading.close();
					if (item.nowprice) {
						this.$toPage("/trade/positionDetail?nowprice=" + item.nowprice);
					} else {
						this.$toPage("/trade/positionDetail");
					}
				}, 1000);
			},
			sellItem(item) {
				this.detailItem = item;
				this.show1 = true;
			},
			changeTime() {
				this.positionlistinteval = setInterval(() => {
					this.changeType();
				}, 10 * 1000);
			},
			changeType() {
				this.initData();
			},
			changeNav(index) {
				this.currmentIndex = index;
				clearInterval(this.positionlistinteval);
				this.$refs.loading.open(); //开启加载

				switch (index) {
					case 0:
						this.getXingu();
						break;
					case 1:
					case 2:
						this.positionList = [];
						this.positionCloseList = [];
						this.changeType();
						this.changeTime();
						break;
					case 3:
						this.getGd();
						break;
					case 4:
						this.getXinguJp();
						break;
					default:
						break;
				}
			},
			// 平仓
			sellstrategy(id, status) {
				if (status == 3) {
					// 撤单
					this.cancelstrategy(id);
					return;
				}
				this.$server
					.post("/trade/sell_stock", {
						id,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							this.show1 = false;
							this.changeNav(2); //平倉成功切換顯示已平倉
							this.initData(true);
						}
					});
			},
			cancelstrategy(id) {
				this.$server
					.post("/trade/cancel_stock", {
						id,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							this.show1 = false;
							this.initData(true);
						}
					});
			},
			// 新股
			getXingu() {
				this.$server
					.post("/trade/usernewstocklist", {
						type: "twd",
						buy_type: 0
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading1 = false;
						if (res.status == 1) {
							this.xinguList = res.data;
						}
					});
			},
			// 競拍
			getXinguJp() {
				this.$server
					.post("/trade/usernewstocklist", {
						type: "twd",
						buy_type: 1
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading5 = false;
						if (res.status == 1) {
							this.xinguListJp = res.data;
							this.xinguListJp.forEach(item=>{
								if(item.stock_code){
									this.getDetail(item)
								}
							})
						}
					});
			},
			getDetail(item){
				this.$server
					.post("/trade/stockdetails", {
						type: "twd",
						symbol: item.stock_code
					})
					.then((res) => {
						this.$set(item,'nowPrice',res.data.price)
					});
			},
			async getTotalAssets() {
				// this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
						this.userCard = this.userInfo.agent_id + '' +this.userInfo.id
						let a
						for (a=0;a<6-this.userCard.length;a++){
							this.userCard = '0' + this.userCard
						}
				}
				let abalance = Number(res.data.twd || 0); //用户可用余额

				// 获取跟单盈亏
				// const res1 = await this.$server.post("/trade/userproductlist", {
				//   type: "twd",
				// });
				// let followingFreeze = 0; //量化跟单的认缴冻结
				// if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
				//   let arr = [];
				//   res1.data.forEach((item) => {
				//     if (item.status == 0) {
				//       arr.push(Number(item.money)); //跟单冻结的资金
				//     }
				//   });
				//   let total = arr.reduce((a, b) => a + b, 0);
				//   followingFreeze = total;
				// }

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: "twd",
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 競拍列表的投資
				const res3 = await this.$server.post("/trade/usernewstocklist", {
					type: "twd",
					buy_type: 1,
				});
				let subscriptionProfit1 = 0; //新股申请的认缴冻结
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					let arr1 = [];
					res3.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit1 = total + total1;
				}

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: "twd",
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: "twd",
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					bigDealFreeze +
					positionFreeze +
					subscriptionProfit1;
				//  + followingFreeze
				// 总资产
				this.totalAssets = abalance + this.freezeAssets;
				this.$refs.loading.close();
			},
			// 持仓&p平仓列表
			initData() {
				let url = "";
				if (this.currmentIndex == 1) {
					url = "/trade/userstocklist";
				} else {
					url = "/trade/userstocklists";
				}

				this.$server.post(url, {
					type: "twd"
				}).then(async (res) => {
					this.isLoading = false; //下拉刷新状态
					this.loading3 = false;
					if (res.status == 1) {
						let arr = res.data;

						let szArr = []; //列表市值
						let ykArr = []; //列表盈亏
						let arr1 = []; //認繳的资金  买入本金+盈利(持仓冻结)
						// let arr2 = [];

						arr.forEach((item) => {
							szArr.push(Number(item.market_value) + Number(item.yingkui));
							ykArr.push(Number(item.yingkui));
							arr1.push(
								Number(item.buy_price) * Number(item.stock_num) +
								Number(item.yingkui)
							);
							// arr2.push(Number(item.buy_price) * Number(item.stock_num));

							item.yingkuiBi = ((item.yingkui / item.market_value) * 100).toFixed(
								2
							);
						});
						if(this.currmentIndex == 1){
							this.szAssets = szArr.reduce((a, b) => a + b, 0);
						}
						this.ykAssets = ykArr.reduce((a, b) => a + b, 0);
						this.freezeAssets = arr1.reduce((a, b) => a + b, 0);

						// let total2 = arr2.reduce((a, b) => a + b, 0);
						// this.percent = ((this.ykAssets / (total2 || 1)) * 100).toFixed(2); //总盈亏比例

						const res1 = await this.$server.post("/user/getUserinfo", {
							type: "twd",
						});
						if (res.status == 1) {
							this.userInfo = res1.data;
						}

						// 总资产 可用+持仓资金
						// this.totalAssets = Number(this.userInfo.twd) + this.freezeAssets;

						if (this.currmentIndex == 1) {
							this.positionList = arr;
						} else {
							this.positionCloseList = arr;
						}

						this.chartData = [
							// {
							//   value: this.totalAssets || 0,
							//   name: "",
							// },
							{
								value: this.szAssets || 0,
								name: "",
							},
							{
								value: this.ykAssets || 0,
								name: "",
							},
							{
								value: this.userInfo.dollar || 0,
								name: "",
							},
						];
						// this.getEcharts();
					}
					this.$refs.loading.close(); //关闭加载
				});
			},
			getEcharts() {
				let that = this;
				if (that.myChart !== null) {
					echarts.dispose(that.myChart);
				}

				let chartDom = document.getElementById("main");
				that.myChart = echarts.init(chartDom);
				let option;
				option = {
					color: ["#6970AF", "#F1BABB", "#A2DDDD"], // 顺时针
					tooltip: {
						trigger: "item",
					},
					// 頂部圖例
					legend: {
						top: "5%",
						left: "center",
						show: false,
					},
					series: [{
						name: "",
						type: "pie",
						// radius: ['40%', '70%'], //圆环
						radius: "100%",
						center: ["50%", "50%"],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: "center",
						},
						emphasis: {
							label: {
								show: true,
								fontSize: "40",
								fontWeight: "bold",
							},
						},
						labelLine: {
							show: false,
						},
						data: this.chartData,
					}, ],
				};

				option && that.myChart.setOption(option);
			},
		},
	};
</script>

<style scoped lang="less">
	.btns {
		padding: 0.15rem 0 0.1rem;
		text-align: center;

		.icon {
			margin: 0 auto 0.05rem;
		}

		.t {
			font-size: 0.12rem;
			color: #000000;
		}
	}

	.page {
		min-height: 100vh;
		padding: 0 0 0.6rem;

		.topHead {
			width: 100%;
			height: 2.5rem;
			background: url('../../assets/v1/hbg.png') no-repeat center/100%;
			padding: 0.3rem 0.16rem;
		.top-tab{
						grid-column-gap: 0.12rem;
						.item{
							font-size: 0.16rem;
							color: #efecec;
							&.active{
								font-weight: bold;
								font-size: 0.17rem;
								color: #ffffff;
							}
						}
			}
			.name {
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}

			.xx {
				margin-left: 0.16rem;
			}
		}

		.nav-box {
			.nav-item {
				padding: 0 0.12rem;
				height: 0.3rem;
				background: #D8E3F6;
				border-radius: 0.13rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #405476;
				position: relative;

				// &::after {
				// 	content: "";
				// 	width: 50%;
				// 	height: 0.02rem;
				// 	position: absolute;
				// 	bottom: 0;
				// 	left: 50%;
				// 	transform: translateX(-50%);
				// 	background: transparent;
				// }

				&.active {
					background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
					color: #fff;
					// &::after {
					// 	background: #549d7e;
					// }
				}
			}
		}

		#main {
			width: 0.66rem;
			height: 0.66rem;
			border-radius: 50%;
		}

		.money {
			margin-top: 0.3rem;
			padding: 0.16rem 0.1rem;
			background: rgba(24, 33, 51, 0.3);
			box-shadow: 0rem 0rem 0.1rem 0rem rgba(220, 162, 92, 0.5);
			border-radius: 0.13rem;
			border: 0.02rem solid #0DCCC8;

			.tops {

				// background: linear-gradient(90deg, #469d6f, #184856);
				.t {
					font-size: 0.12rem;
					color: #ffffff;

					.icon {
						margin-left: 0.1rem;
					}
				}

				.num {
					font-weight: 600;
					font-size: 0.16rem;
					color: #ffffff;
					margin: 0.05rem 0;
				}
			}

			.sz-num {
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-top: 0.05rem;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.15rem;
					color: #FFFFFF;
				}
			}
		}

		.bg {
			padding: 0.1rem;
			margin-top: -0.6rem;
			background: #F6F8FE;
			border-radius: 0.15rem 0.15rem 0rem 0rem;
			position: relative;
			z-index: 999;
		}
	}

	.cy-list {
		margin-top: 0.1rem;

		.cy-item {
			padding: 0.16rem 0rem;
			border-bottom: 0.01rem solid #dedede;

			.name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #182133;
			}

			.code {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #69717D;
				margin-top: 0.05rem;
			}

			.sy {
				div {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #DA5B4C;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #DA5B4C;
					margin-right: 0.05rem;
				}
			}

			.time {
				font-size: 0.12rem;
				color: #a8a8a8;
				text-align: right;
				margin-top: 0.05rem;
			}

			.btns {
				padding: 0.16rem 0 0;

				.btn {
					height: 0.39rem;
					background: rgba(7, 139, 222, 0.05);
					border-radius: 0.2rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #078bde;
					width: 48%;
					text-align: center;

					&.bt {
						background: #078bde;
						color: #fff;
					}
				}
			}

			.inner {
				padding: 0.16rem 0 0;
				flex-wrap: wrap;

				.inner-item {
					line-height: 0.24rem;
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 48%;

					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #69717D;
					}

					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #182133;
					}
				}
			}
		}
	}

	.red {
		color: #cf2829;
	}

	.green {
		color: #60bb74;
	}

	.popup {
		background: #FFFFFF;
		border-radius: 0.12rem;

		.top {
			padding: 0.16rem;
			border-bottom: 0.01rem soild #F3F3F3;

			.title {
				font-size: 0.16rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.17rem;
				color: #000000;
			}
		}

		.pdd {
			padding: 0 0.15rem 0.15rem;
			text-align: center;

			.num {
				margin: 0.15rem 0;
			}

			.icon {
				margin-right: 0.15rem;
				width: 0.24rem;
				height: 0.24rem;
			}

			.t {
				font-family: DINPro, DINPro;
				font-weight: 500;
				font-size: 0.33rem;
				color: #DCA25C;
				color: #60bb74;

				&.red {
					color: #cf2829;
				}
			}

			.t1 {
				margin-top: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.16rem;
				color: #333333;
			}

			.bt {
				width: 48%;
				margin-top: 0.15rem;
				height: 0.39rem;
				background: rgba(7, 139, 222, 0.05);
				border-radius: 0.2rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #078bde;

				&.bt1 {
					color: #fff;
					background: #078bde;
				}
			}
		}
	}

	.gd-list {
		margin-top: 0.1rem;

		.gd-item {
			margin-bottom: 0.15rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;

			.name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #182133;
			}

			.code {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #69717D;
				margin-top: 0.05rem;
			}

			.center {
				background: #F6F8FE;
				border-radius: 0.06rem;
				padding: 0.1rem 0.6rem;
				line-height: 0.24rem;
				margin: 0.1rem 0;

				div {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}

			.status {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #078bde;

				&.end {
					color: #000;
				}
			}

			.inner-list {
				flex-wrap: wrap;

				div {
					font-size: 0.12rem;
					font-weight: 500;
					color: #999999;
				}

				.inner-item {
					padding: 0.1rem 0;

					.t1 {
						font-size: 0.12rem;
						color: #686868;
					}

					.t-r {
						font-size: 0.12rem;
					}

					.red {
						color: #ba3b3a;
					}

					.green {
						color: #68a03d;
					}
				}
			}

			.red {
				color: #cf2829;
			}

			.green {
				color: #549d7e;
			}

			.time {
				margin-bottom: 0.1rem;

				div,
				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #9FA9BA;
				}
			}

			.info {
				line-height: 0.24rem;
				background: #F6F8FE;
				border-radius: 0.06rem;
				padding: 0.1rem;

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #9FA9BA;
				}

				.t2 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #182133;
				}
			}

			.sylb {
				font-size: 0.12rem;
				color: #1b1b1b;
				margin: 0.05rem auto;

				.icon {
					margin-left: 0.05rem;
				}
			}
		}
	}

	.sg-list {
		margin-top: 0.1rem;

		.sg-item {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #EAEDF8;

			.top {
				margin-bottom: 0.1rem;

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #182133;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #69717D;
					margin-top: 0.05rem;
				}

				.status {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #078bde;
					text-align: center;

					&.pc {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.13rem;
						color: #999999;
					}
				}
			}

			.data {
				flex-wrap: wrap;

				.data-item {
					line-height: 0.3rem;
					width: 46%;
					border-right: 0.01rem soild #DBDBDB;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #69717D;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #182133;
				}

				.price {
					color: #DA5B4C;
				}

			}
		}
	}
</style>