<template>
	<!-- 存股借券 -->
	<div class="page">
		<!-- <top-back title="存股借券"></top-back> -->
		<top-back title="融資借款"></top-back>

		<div class="nav-box flex">
			<div class="nav-item" v-for="(item, index) in navList" :key="index"
				:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
				{{ item.name }}
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot">
					<div class="list" v-show="currmentIndex == 1">
						<div class="list-item" v-for="(item, index) in scrollList" :key="index" @click="xd(item)">
							<div class="flex flex-b name">
								<div class="">
									<div class="t">{{ item.name }}</div>
									<div class="code">{{ item.code }}</div>
								</div>
								<div class="but">下單</div>
							</div>
							<div class="middle">
								<div class="flex flex-b">
									<div class="t1">參考價格</div>
									<div class="t2">{{ $formatMoney(item.price) }}</div>
								</div>
								<div class="flex flex-b">
									<div class="t1">參考費率</div>
									<div class="t2">{{ item.lx }}%</div>
								</div>
							</div>
							<div class="items">
								<div class="flex flex-b">
									<div class="t3">出借天數</div>
									<div class="t4">{{ item.day }}</div>
								</div>
								<div class="flex flex-b">
									<div class="t3">需求張數</div>
									<div class="t4">
										{{ item.min == 0 ? "無門檻" : item.min / 10000 + "萬" }}
									</div>
								</div>
								<div class="flex flex-b">
									<div class="t3">類型</div>
									<div class="t4 green">封閉型</div>
								</div>
							</div>
						</div>
						<no-data v-if="!scrollList.length"></no-data>
					</div>

					<!--  -->
					<div class="list" v-show="currmentIndex == 2">
						<div class="list-item" v-for="(item, index) in stockList" :key="index">
							<div class="flex flex-b name">
								<div class="">
									<div class="t">{{ item.stock_name }}</div>
									<div class="code">{{ item.stock_code }}</div>
								</div>

								<div class="but">{{ item.xgstate }}</div>
							</div>

							<div class="middle">
								<div class="flex flex-b">
									<div class="t1">借券收益</div>
									<div class="t2">
										{{ $formatMoney((item.credit * item.lx) / 100) }}
									</div>
								</div>

								<div class="flex flex-b">
									<div class="t1">借券利率</div>
									<div class="t2">{{ item.lx }}%</div>
								</div>
							</div>

							<div class="items">
								<div class="flex flex-b">
									<div class="t3">借券天數</div>
									<div class="t4">{{ item.day }}</div>
								</div>
								<div class="flex flex-b">
									<div class="t3">借券張數</div>
									<div class="t4">{{ $formatMoney(item.zhang, 0) }}</div>
								</div>

								<div class="flex flex-b">
									<div class="t3">借券市值</div>
									<div class="t4">{{ $formatMoney(item.credit) }}</div>
								</div>
								<!-- <div class="flex flex-b">
              <div class="t3">借券時間</div>
              <div class="t4">{{ formatTime(item.buy_time) || "-" }}</div>
            </div> -->
							</div>
						</div>

						<no-data v-if="!stockList.length"></no-data>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<!-- 弹出-->
		<van-popup v-model="show" position="center" round :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-price t-c">借券</div>

				<div class="pad">
					<div class="ipt">
						<div class="tt">買入張數</div>
						<input class="ipt" type="number" placeholder="請輸入購買張數" v-model="quantity" />
					</div>
					<div class="b-btn" @click="qrXd" style="">確定</div>
				</div>
			</div>
			<div class="icon close" @click="show = false"></div>
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "coupons",
		data() {
			return {
				loading: true,
				isLoading: false,
				show: false,
				navList: [{
						name: "借券列表",
						type: 1
					},
					{
						name: "借券持倉",
						type: 2
					},
				],
				currmentIndex: 1,
				stockList: [
					// {
					//   stock_name: "name",
					//   stock_code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 1000,
					// },
					// {
					//   stock_name: "name",
					//   stock_code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 100,
					// },
				],
				scrollList: [
					// {
					//   name: "name",
					//   code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 100,
					// },
					// {
					//   name: "name",
					//   code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 100,
					// },
				],
				stockId: null,
				quantity: null,
			};
		},
		computed: {
			formatTime() {
				return (val) => {
					let str = val.split(" ");
					return str[0];
				};
			},
		},
		created() {
			this.getScrollList();
		},
		methods: {
			onRefresh() {
				this.currmentIndex = 1;
				this.getScrollList();
			},
			qrXd() {
				if (!this.quantity || this.quantity == 0)
					return uni.showToast({
						title: "請輸入購買張數",
						icon: "none",
					});
				this.$server
					.post("/trade/tojiequan", {
						id: this.stockId,
						zhang: this.quantity,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.show = false;
						}
						if (res.msg) {
							this.$toast(res.msg);
						}
					});
			},
			xd(item) {
				this.show = true;
				this.stockId = item.id;
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.$refs.loading.open(); //开启加载

				if (index == 2) {
					this.getStockList();
				} else {
					this.getScrollList();
				}
			},

			getScrollList() {
				this.$server.post("/trade/cgjqlist", {
					type: "twd"
				}).then((res) => {
					this.$refs.loading && this.$refs.loading.close(); //开启加载
					this.loading = false;
					this.isLoading = false;

					if (res.status == 1) {
						this.scrollList = res.data;
					}
				});
			},
			getStockList() {
				this.$server.post("/trade/user_cgjqlist", {
					type: "twd"
				}).then((res) => {
					this.$refs.loading && this.$refs.loading.close(); //开启加载

					if (res.status == 1) {
						this.stockList = res.data;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 1rem 0.1rem 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	.nav-box {
		margin: 0 0 0.1rem;
		position: fixed;
		top: 0.5rem;
		left: 0;
		width: 100%;
		background-color: #fff;
		z-index: 999;

		.nav-item {
			padding: 0.1rem 0;
			flex: 1;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #405476;
			text-align: center;
			position: relative;
			&::after {
				content: "";
				width: 100%;
				height: 0.02rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}
		
			&.active {
				color: #dba25c;
		
				&::after {
					background: #dba25c;
				}
			}
		}
	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background-color: #fff;
		// padding: 0.15rem;
		border-radius: 0.08rem;
		position: relative;

		.pad {
			padding: 0.2rem 0.15rem;
		}

		.btips {
			padding: 0.15rem 0;
			line-height: 0.24rem;

			.t2 {
				color: #a91111;
			}
		}

		.pop-title {
			font-size: 0.16rem;
			text-align: center;
		}

		.pop-price {
			padding: 0.15rem 0;
			background: #f6f8fe;

			.t {
				font-size: 0.14rem;
				color: #9a9fa5;

				span {
					color: #60bb74;
				}
			}

			.t1 {
				font-size: 0.16rem;
				color: #000000;
				margin-bottom: 0.05rem;
			}
		}

		.ipt {
			.tt {
				font-size: 0.14rem;
				color: #0e1028;
			}

			input {
				background: transparent;
				border-radius: 0.04rem;
				border: 0.01rem solid #cecece;
				height: 0.42rem;
				line-height: 0.42rem;
				padding: 0 0.1rem;
				// margin-left: 0.1rem;
				margin: 0.1rem 0;
				width: 100%;

				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}

			.t2 {
				font-size: 0.12rem;
				color: #7da1ef;
			}

			.mt10 {
				margin-top: 0.05rem;
			}
		}

		.pop-num {
			margin-top: 0.15rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				background: #f8f8f8;
				border-radius: 0.06rem;
				border: 0.01rem solid #b8b8b8;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #606060;
				}
			}
		}

		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;

			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}

		.b-btn {
			width: 100%;
		}
	}

	.cot {
		.list {
			.list-item {
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
				border-radius: 0.07rem;
				padding: 0.1rem;
				margin-bottom: 0.15rem;

				.but {
					background: #dba25c;
					border-radius: 0.04rem;
					font-size: 0.12rem;
					color: #ffffff;
					padding: 0.05rem 0.1rem;
				}

				.name {
					.t {
						font-size: 0.14rem;
						color: #000000;
					}

					.code {
						font-size: 0.12rem;
						color: #909090;
					}
				}

				.middle {
					background: rgba(246, 248, 254, 1);
					border-radius: 0.06rem;
					padding: 0.1rem;
					line-height: 0.24rem;
					margin: 0.1rem 0;

					.t1 {
						font-size: 0.12rem;
						color: #8e8e8e;
					}

					.t2 {
						font-size: 0.12rem;
						color: #cc4749;
					}
				}

				.items {
					line-height: 0.24rem;

					.t3 {
						font-size: 0.12rem;
						color: #8e8e8e;
					}

					.t4 {
						font-size: 0.12rem;
						color: #000000;

						&.green {
							color: #549d7e;
						}
					}
				}
			}
		}
	}
</style>