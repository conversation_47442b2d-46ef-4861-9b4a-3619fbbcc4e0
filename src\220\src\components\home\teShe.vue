<template>
  <div class="">
    <van-skeleton title :row="20" :loading="loading">
      <div class="list">
        <div class="tt">
          <!-- <div class="flex flex-b titles">
            <div class="flex-1">{{ $t("newt").t57 }}</div>
            <div class="flex-1 t-r">{{ $t("newt").t58 }}</div>
            <div class="flex-1 t-r">{{ $t("newt").t59 }}</div>
          </div> -->

          <div class="cot">
            <div class="flex flex-b tab">
              <div
                class="tab-item"
                :class="{
                  active: type == item.type,
                  't-c': i == 1 || i == 2,
                  't-r': i == 3,
                }"
                v-for="(item, i) in typeList"
                :key="i"
                @click="changeType(item.type)"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="lists">
          <div
            class="list-item flex flex-b"
            v-for="(item, idx) in list"
            :key="idx"
            @click="$toDetail(`/market/stockDetail?symbol=${item.code}`, item)"
          >
            <div class="flex flex-2">
              <div class="num">{{ idx + 1 }}</div>
              <div>
                <div class="name">
                  {{ item.ko_name }}
                </div>
                <div class="code">
                  {{ item.code }}
                </div>
              </div>
            </div>
            <div class="flex-1 t-r">
              <div
                class="price red"
                :class="{
                  green: Number(item.returns) < 0,
                }"
              >
                {{ $formatMoney(item.close) || 0 }}
              </div>

              <!-- <div
                  class="t red"
                  :class="{
                    green: Number(item.gainValue) < 0,
                  }"
                >
                  {{ item.gainValue > 0 ? "+" : "" }}{{ item.gainValue || 0 }}
                </div> -->
            </div>

            <div class="flex-1 flex flex-e">
              <div
                class="per red-bg"
                :class="{
                  'green-bg': Number(item.returns) < 0,
                }"
              >
                {{ item.returns > 0 ? "+" : "" }}{{ item.returns.toFixed(2) }}%
              </div>
            </div>
          </div>

          <div class="bt" @click="$toPage('/market/index')">
            {{ $t("更多") }}
          </div>
        </div>
      </div>
    </van-skeleton>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "teShe",
  components: {},
  data() {
    return {
      loading: true,
      type: "returns_top",
      typeList: [
        {
          name: this.$t("newt").t60,
          type: "returns_top",
        },
        {
          name: this.$t("newt").t61,
          type: "returns_bottom",
        },
        {
          name: this.$t("newt").t62,
          type: "new_high_price",
        },
        {
          name: this.$t("newt").t63,
          type: "new_low_price",
        },
        // {
        //   name: '上限',
        //   type: 'upper_limit_price'
        // },
        // {
        //   name: '下限',
        //   type: 'lower_limit_price'
        // },
        // {
        //   name: '突破上限',
        //   type: 'out_of_upper_limit_price'
        // },
        // {
        //   name: '突破下限',
        //   type: 'out_of_lower_limit_price'
        // }
      ],
      list: [],
    };
  },
  created() {},
  mounted() {
    this.getInfo();
  },
  onLoad() {},
  methods: {
    changeType(type) {
      this.type = type;
      this.getInfo();
    },
    getInfo() {
      this.$refs.loading.open(); //开启加载
      this.$server.post("/parameter/tsxm", { type: this.type }).then((res) => {
        this.$emit("upData");

        this.$refs.loading && this.$refs.loading.close();
        this.loading = false;
        if (res.status == 1) {
          let arr = [];
          res.data.data.forEach((item, i) => {
            if (item.returns) {
              item.returns = item.returns;
            }
            if (item.week_returns) {
              item.returns = item.week_returns;
            }
            if (item.month_returns) {
              item.returns = item.month_returns;
            }
            if (item.three_month_returns) {
              item.returns = item.three_month_returns;
            }
            if (item.six_month_returns) {
              item.returns = item.six_month_returns;
            }

            //只展示前面5个
            // if (i <= 4) {
            //   arr.push(item);
            // }
            arr.push(item);
          });

          // 展示前面10条记录
          let Arr = arr.filter((item, i) => i < 10);

          this.list = Arr;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.titles {
  padding: 0.1rem;
  border-bottom: 0.01rem solid #f5f5f5;
  div {
    font-size: 0.12rem;
    color: #757575;
  }
}

.lists {
  background: #ffffff;
  border-radius: 0.24rem;
  margin: 0.1rem 0.15rem;
  padding: 0 0.15rem 0.1rem;

  .bt {
    background: #eff0f4;
    border-radius: 0.06rem;
    width: 100%;
    height: 0.4rem;
    line-height: 0.4rem;
    margin: 0.15rem 0 0.1rem;
    font-weight: 500;
    font-size: 0.14rem;
    color: #8e8e8e;
    text-align: center;
  }

  .list-item {
    padding: 0.15rem 0;
    border-bottom: 0.01rem solid #ebebeb;
    &:last-child {
      border-bottom: 0;
    }
    .icon {
      margin-right: 0.05rem;
    }
    .num {
      font-weight: 600;
      font-size: 0.16rem;
      color: #000000;
      margin-right: 0.1rem;
    }
    .name {
      font-weight: 600;
      font-size: 0.16rem;
      color: #000000;
    }
    .code {
      font-size: 0.12rem;
      color: #989898;
      margin-top: 0.05rem;
    }
    .t {
      font-size: 0.12rem;
      margin-top: 0.05rem;
    }
    .price {
      font-weight: 600;
      font-size: 0.16rem;
    }
    .per {
      border-radius: 0.3rem;
      padding: 0.05rem 0.1rem;
      font-size: 0.12rem;
      color: #fff;
      width: 80%;
      text-align: center;
    }
  }
}

.red {
  color: #df4645;
}
.green {
  color: #3561e5;
}

.red-bg {
  background-color: #df4645;
}
.green-bg {
  background-color: #3561e5;
}

.list {
  .tt {
    .cot {
      padding: 0.1rem 0.15rem;
      .tab {
        .tab-item {
          width: 25%;
          font-weight: 500;
          font-size: 0.16rem;
          color: #8e8e8e;
          &.active {
            font-weight: 600;
            color: #000000;
          }
        }
      }
    }
  }
}
</style>
