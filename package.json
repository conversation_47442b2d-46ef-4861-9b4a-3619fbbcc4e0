{"name": "hs", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@ethersproject/address": "^5.0.8", "@ethersproject/contracts": "^5.0.8", "@ethersproject/networks": "^5.0.6", "@ethersproject/providers": "^5.0.17", "@ethersproject/solidity": "^5.0.7", "@uniswap/sdk": "^3.0.3", "@vant/area-data": "^1.1.1", "axios": "^0.19.0", "core-js": "^3.4.3", "crypto-js": "^3.1.9-1", "echarts": "^5.5.0", "es6-promise": "^4.2.8", "html2canvas": "^1.0.0-rc.5", "klinecharts": "^8.3.6", "npm": "^10.4.0", "qrcode": "^1.4.4", "qrcodejs2": "0.0.2", "qs": "^6.9.1", "uglifyjs-webpack-plugin": "^1.3.0", "vant": "^2.12.13", "vue": "^2.6.10", "vue-awesome-mui": "^1.7.3", "vue-clipboard2": "^0.3.1", "vue-kline": "^1.2.2", "vue-qr": "^2.4.0", "vue-resource": "^1.5.1", "vue-router": "^3.1.3", "vuex": "^3.1.2", "webpack": "^4.47.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "babel-plugin-import": "^1.13.0", "less": "^3.10.3", "less-loader": "^5.0.0", "vue-i18n": "^8.15.3", "vue-template-compiler": "^2.6.10"}, "browserslist": ["> 1%", "last 2 versions"]}