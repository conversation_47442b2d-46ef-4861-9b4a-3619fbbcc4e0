<template>
	<div class="page ">
		<top-back title="通知"></top-back>

		<div class="list-box">
			<div class="list-item flex flex-b" v-for="(item, i) in list" :key="i" @click="toMsgInfo(item)">
				<div>
					<div class="t">{{ item.title }}</div>
					<div class="time">{{ item.create_time }}</div>
				</div>
				<div class="icon jt"></div>
			</div>
			<no-data v-if="show"></no-data>
		</div>
		<first-loading ref="firstLoading"></first-loading>
	</div>
</template>

<script>
	export default {
		name: "userInfo",
		data() {
			return {
				show: false,
				list: [
					// {
					//   title: "通知",
					//   create_time: "2024-01-26",
					// },
				],
			};
		},
		computed: {},
		components: {},
		created() {
			this.initData();
		},
		mounted() {
			this.$refs.firstLoading.open();
		},
		methods: {
			toMsgInfo(item) {
				this.$toPage(`/information/msgInfo?id=${item.id}`);
			},
			initData() {
				this.$server.post("/user/notice", {
					type: "twd"
				}).then((res) => {
					this.$refs.firstLoading.close();
					if (res.status == 1) {
						this.list = res.data;
						if (!this.list.length) {
							this.show = true;
						}
						// 获取列表成功，标识已读
						//   let account = uni.getStorageSync("account");
						//   uni.setStorageSync(account, this.list.length);
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0.1rem;
		min-height: 100vh;
	}

	.list-box {
		.list-item {
			margin: 0.1rem 0;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;
			.t {
				font-weight: bold;
			}

			.time {
				font-size: 0.12rem;
				color: #999;
				margin-top: 0.1rem;
			}
		}
	}
</style>