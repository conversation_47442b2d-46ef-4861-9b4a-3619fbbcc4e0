<template>
	<div class="page ">
		<!-- <top-menu></top-menu> -->
		<div class="topHead">
			<div class="flex flex-b" style="padding: 0.2rem 0.16rem;">
				<div class="flex" @click="$toPage('/information/index')">
					<div class="icon user"></div>
					<!-- <div>
						<div class="name">{{ userInfo.realname }}</div>
						<div class="code">{{ userInfo.account }}</div>
					</div> -->
				</div>
				<div class="top-tab flex">
					<div class="item" :class="{ active: tabIndex === 0 }"  @click="tabIndex=0">台灣</div>
					<div class="item" :class="{ active: tabIndex === 1 }" @click="tabIndex=1">美國</div>
				</div>
				<div class="flex">
					<div class="icon ss" @click="$toPage('/favorite/search')"></div>
					<div class="icon xx" style="position: relative" @click="$toPage('/information/userInfo')">
            <span v-if="readNum>0" style="width: .08rem;height: .08rem;border-radius: 50%;background: #ff0000;margin-left: -.05rem;display: block;"></span>
          </div>
				</div>
			</div>
			<van-skeleton title :loading="loading1">
				<!-- 指數顯示 -->
				<van-swipe class="my-swipe" :autoplay="10000" indicator-color="white">
					<!-- <van-swipe-item>
						<img src="../../assets/001.png" style="width: 100%;">
					</van-swipe-item> -->
					<van-swipe-item>
						<img src="../../assets/002.png" style="width: 100%;">
					</van-swipe-item>
				</van-swipe>
				<!-- <div class="zx">
					<div class="zx-cot">
						<div class="zx-list flex flex-b">
							<div class="zx-item flex" v-for="(item, i) in indexList" :key="i" v-if="i<5">
								<div class="name">{{ item.symbolName }}</div>
								<div class="price" :class="Number(item.change) > 0 ? 'red' : 'green'">
									{{ $formatMoney(item.price) }}
								</div>
								<div class="flex flex-b per" :class="Number(item.change) > 0 ? 'red' : 'green'">
									<div class="flex mr10">
										<div class="icon" :class="Number(item.change) > 0 ? 'up' : 'down'"></div>
										{{ $formatMoney(item.change) }}
									</div>
									<div class="flex">
										{{ item.changePercent }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div> -->
			</van-skeleton>
			<!-- <div class="flex flex-b title">
				<div>
					<div class="t01">智慧，選股票</div>
					<div class="t02"><span>精選投資</span>打造</div>
					<div class="t02">被動收入的黃金比例</div>
				</div>
				<img src="../../assets/v2/indexIcon.png" style="width: 0.85rem;height: 0.73rem;" />
			</div> -->
			<!-- <van-skeleton title :row="6" :loading="loading2">
				<div class="news02 flex">
					資訊
					<div class="news-list" v-for="(item,i) in articleList" :key="i" @click="toNewsDetail(item)"
						v-if="i<5">
						<div class="t">{{ item.title }}</div>
						<div class="time">{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created*1000) }}
						</div>
					</div>
				</div>
			</van-skeleton> -->
			<!-- 功能列表 -->
		</div>
		<div class="icon hbg" v-if="false">
			<div class="flex flex-b">
				<div class="icon kf1 animate__animated animate__fadeIn" @click="getConfig()"></div>
				<div class="title">首頁</div>
				<div class="icon tz1 animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')"></div>
			</div>

			<div class="flex flex-b tips">
				<div class="info">
					<div class="t">您好，{{ userInfo.realname || userInfo.account }}</div>
					<div class="t1">歡迎來到{{ $cfg.title }}</div>
				</div>
				<div class="dl-btn" v-if="isLogin" @click="$toPage('/login/login')">
					立即登入
				</div>
			</div>
		</div>
		<div class="func">
			<div class="tab flex flex-b">
				<div class="tab-item" v-for="(item, i) in currentTabList" :key="i"
					@click="handleTabClick(item)">
					<div class="icon animate__animated animate__fadeIn" :class="item.icon"></div>
					<div class="t">{{ item.name }}</div>
				</div>
			</div>
		</div>

		<div class="flex flex-b titles" @click="handleTabClick({url:'/favorite/moreIndexList'})">
			<div class="tit">指數行情</div>
			<div class="icon more"></div>
		</div>
		<van-skeleton title :row="6" :loading="loading1">
			<!-- 指數顯示 -->
			<div class="index">
				<div class="nums flex">
					<div class="nums-item" v-for="(item, i) in currentIndexList.slice(0, 5)" :key="i"
						@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
						<div class="flex">
							<div class="icon" :class="item.icon"></div>
							<div class="name">{{ item.symbol }}</div>
						</div>
						<div class="flex flex-b">
							<div class="t">{{ $formatMoney(item.price, 2) }}</div>
							<img src="../../assets/v2/greenLine.png" alt="" v-if="item.gain < 0" style="width: 0.57rem;height: 0.18rem;"/>
							<img src="../../assets/v2/redLine.png" alt="" v-else style="width: 0.57rem;height: 0.18rem;" />
						</div>
						<div class="t1 red flex flex-e" :class="{ 'green': item.gain < 0 }">
							<div class="icon animate__animated animate__fadeIn" :class="item.gain < 0 ? 'down1' : 'up1'" style="margin-right: 0.05rem;"></div>
							{{ item.gain <= 0 ? "" : "+" }}{{ $formatMoney(item.gainValue, 2) }}
							{{ item.gain <= 0 ? "" : "+" }}{{ item.gain }}%
						</div>
					</div>
				</div>
			</div>
		</van-skeleton>

    <template v-if="tabIndex === 0">
			<div class="flex flex-b titles" @click="$toPage('/home/<USER>')">
				<div class="tit">IPO專區</div>
				<div class="icon more"></div>
			</div>

			<div class="ipo">
				<img src="../../assets/v2/ipo.png" style="width: 1.28rem;height: 0.76rem;">
				<div style="margin-left: .25rem;">
					<div class="flex">
						<div class="items">
							<div class="flex-only">
								<div class="num">96</div>
								<div class="fh">%</div>
							</div>
							<div class="text">正報酬率</div>
						</div>
						<div class="items" style="margin-left: .61rem;">
							<div class="flex-only">
								<div class="num">80</div>
								<div class="fh">%</div>
							</div>
							<div class="text">平均獲利</div>
						</div>
					</div>
					<div class="texta">
						IPO都是專業團隊基於大量市場研究，以及評估得出的優質企業。 想掌握市場脈動參與募集就對了！
					</div>
				</div>
			</div>
		</template>

		<div class="flex flex-b titles">
			<div class="tit">熱門股票</div>
			<div class="icon more" @click="$toPage('/favorite/moreList')"></div>
		</div>
		<van-skeleton title :row="6" :loading="loading2">
			<div class="rm-list">
				<!-- <div class="titls flex flex-b">
					<div class="flex-1">名稱</div>
					<div class="flex-1 t-c">價格</div>
					<div class="flex-1 t-c flex flex-c">
						成交量
						<div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div>
					</div>
					<div class="flex-1 t-r flex flex-e">
						漲跌
						<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
					</div>
				</div> -->
				<div class="nav flex ">
					<div class="navItem flex flex-c" :class="{active:navItem==1}" @click="changeList(1)">漲幅榜</div>
					<div class="navItem flex flex-c" :class="{active:navItem==2}" @click="changeList(2)">跌幅榜</div>
					<div class="navItem flex flex-c" :class="{active:navItem==0}" @click="changeList(0)">成交額</div>
				</div>
				<no-data v-if="!stocklist.length"></no-data>
				<div class="rmList">
					<div class="rm-item flex flex-b" v-for="(item, i) in stocklist" :key="i"
						@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)" v-show="i < 10">
						<div class="flex-1">
							<div class="name">{{ item.local_name }}</div>
							<div class="code">{{ item.symbol }}</div>
						</div>
						<div class="flex-1 t-c price" :class="item.gain > 0 ? 'red' : 'green'">
							{{ $formatMoney(item.price) || "-" }}
						</div>
						<div class="flex-1 t-c">
							<!-- {{ $formatMoney(Number(item.volume) / 1000000) }} M -->
							{{ $formatMoney(Number(item.volume / 1000000)) || "-" }} M
						</div>
						<div class="flex-1 t-r">
							<div class="flex flex-e per" :class="Number(item.gain) > 0 ? 'red' : 'green'">
								<div class="icon animate__animated animate__fadeIn"
									:class="Number(item.gain) > 0 ? 'up' : 'down'"></div>
								{{ $formatMoney(item.gain) || "-" }}
							</div>
							<div class="per" :class="Number(item.gain) > 0 ? 'red' : 'green'">
								{{ item.gainValue || "-" }}%
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-skeleton>
		<div class="flex flex-b titles">
			<div class="tit">精選資訊</div>
			<div class="icon more" @click="$toPage('/home/<USER>')"></div>
		</div>
		<van-skeleton title :row="6" :loading="loading2">
			<div class="news">
				<!--資訊 -->
				<!-- <van-swipe class="news-list" :autoplay="3000" indicator-color="#1FAC8C">
					<van-swipe-item class="news-item" v-for="(item, i) in articleList" :key="i"
						@click="toNewsDetail(item)">
						<div class="flex flex-b">
							<img v-if="item.img" :src="item.img" alt="" />
							<img v-else src="../../assets/home/<USER>" alt="" />
							<div class="flex-1 txt">
								<div class="t">{{ item.title }}</div>
								<div class="time">
									{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created * 1000) }}
								</div>
							</div>
						</div>
					</van-swipe-item>
				</van-swipe> -->
				<div class="news-list02">
					<div class="news-item02 flex flex-b" v-for="(item, i) in articleList" :key="i"
						@click="toNewsDetail(item)">
						<img v-if="item.img" :src="item.img" alt="" />
						<div class="flex-1 txt">
							<div class="t">{{ item.title }}</div>
							<div class="time">
								{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created*1000) }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-skeleton>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
		<tab-bar :current="0"></tab-bar>

		<van-popup v-model="zqShow" round mode="center" :style="{ width: '80%' }" border-radius="14">

			<van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
				<van-swipe-item v-for="item in zhongList">
					<div class="zqbg">
						<img src="../../assets/yh.gif"
							style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;" />
						<div class="title">恭喜您{{item.buy_type==0?'中籤':'得標'}}</div>

						<div class="flex flex-b wd-100" style="margin-top: 50rpx;">
							<div class="item">股票名稱</div>
							<div class="item1">{{item.stock_name}}</div>
						</div>
						<div class="flex flex-b wd-100">
							<div class="item">股票代碼</div>
							<div class="item1">{{item.stock_code}}</div>
						</div>
						<div class="flex flex-b wd-100">
							<div class="item">{{item.buy_type==0?'中籤張數':'得標張數'}}</div>
							<div class="item1">{{item.lucky_total/1000}}</div>
						</div>
						<!-- <div class="flex flex-b wd-100">
							<div class="item">{{item.buy_type==0?'支付金額':'認繳金額'}}</div>
							<div class="item1">{{$formatMoney(item.lucky_total*item.apply_price)}}</div>
						</div> -->

						<div class="btns" @click="tozqDetail(item.buy_type==0?0:4)">{{item.buy_type==0?'申購詳情':'競拍詳情'}}</div>
					</div>
				</van-swipe-item>

			</van-swipe>


		</van-popup>
	</div>
</template>

<script>
	import {
		init,
		dispose,
		extension
	} from "klinecharts";
	import * as echarts from 'echarts';

	export default {
		name: "home",
		props: {},
		data() {
			return {
				loading1: true,
				loading2: true,
				tabIndex:0,
				indexList: [],
				usIndexList: [], // 美股指数列表
				isLogin: false,
				kfUrl: "",
				// tabList: [
				//   {
				//     name: "新股申購",
				//     icon: "h1",
				//     url: "/home/<USER>",
				//   },
				//   {
				//     name: "大宗交易",
				//     icon: "h2",
				//     url: "/home/<USER>",
				//   },

				//   {
				//     name: "新股競拍",
				//     icon: "h4",
				//     url: "/home/<USER>",
				//   },
				//   {
				//     name: "ETF",
				//     icon: "h5",
				//     url: "/home/<USER>",
				//   },
				//   {
				//     name: "存股借券",
				//     icon: "h6",
				//     url: "/home/<USER>",
				//   },
				//   {
				//     name: "實名認證",
				//     icon: "h7",
				//     url: "/information/authInfo",
				//   },
				//   {
				//     name: "儲值",
				//     icon: "h8",
				//     url: "/information/recharge",
				//   },
				//   {
				//     name: "提領",
				//     icon: "h9",
				//     url: "/information/cashOut",
				//   },
				//   {
				//     name: "金融卡管理",
				//     icon: "h10",
				//     url: "/information/bankList",
				//   },
				//   {
				//     name: "我的持倉",
				//     icon: "h11",
				//     url: "/trade/index",
				//   },
				//   {
				//     name: "資金流水",
				//     icon: "h12",
				//     url: "/information/fundRecord",
				//   },
				// ],
				// 台灣市场功能列表
				taiwanTabList: [{
						name: "數位當沖",
						icon: "h1",
						url: "/home/<USER>",
					}, {
						name: "股票市場",
						icon: "h2",
						url: "/favorite/index",
					},
					{
						name: "庫存查詢",
						icon: "h3",
						url: "/trade/index",
					},
					{
						name: "端午福籤",
						icon: "h4",
						url: "/home/<USER>",
					},
					{
						name: "主力競拍",
						icon: "h5",
						url: "/home/<USER>",
					},
					{
						name: "庫藏紅利",
						icon: "h6",
						url: "/home/<USER>",
					},
					{
						name: "傳承基金",
						icon: "h7",
						url: "/home/<USER>",
					},
					{
						name: "慈善融資",
						icon: "h8",
						url: "/information/loan",
					},
					{
						name: "雲端管家",
						icon: "h9",
						url: "kefu",
					},
				],
				// 美國市场功能列表
				usTabList: [{
						name: "數位當沖",
						icon: "h1",
						url: "/home/<USER>", // 美股当冲页面
					}, {
						name: "股票市場",
						icon: "h2",
						url: "/favorite/index", // 美股市场页面
					},
					{
						name: "新股申購",
						icon: "h4",
						url: "/home/<USER>",
					},
					{
						name: "傳承基金",
						icon: "h7",
						url: "/home/<USER>",
					}
				],
				userInfo: {},
				articleList: [],
				stocklist: [],
				mType: "TAI",
				sortIndex: 1,
				show1: true,
				navItem: 1,
				chart: '',
				show: false,
				zqShow:false,
				zhong: '',
				zhongList: [],
        readNum: 0,
			};
		},
		components: {},
		computed: {
			// 根据当前选择的市场返回对应的功能列表
			currentTabList() {
				return this.tabIndex === 0 ? this.taiwanTabList : this.usTabList;
			},
			// 根据当前选择的市场返回对应的指数列表
			currentIndexList() {
				return this.tabIndex === 0 ? this.indexList : this.usIndexList;
			},
			// 根据当前选择的市场返回对应的股票列表类型
			currentMarketType() {
				return this.tabIndex === 0 ? 'twd' : 'usd';
			}
		},
		watch: {
			// 监听市场切换
			tabIndex(newVal, oldVal) {
				if (newVal !== oldVal) {
					// 重新获取股票列表
					this.getMarkListStock();
				}
			}
		},
		created() {},
		mounted() {
      this.readData()
			this.getUserInfo();
			this.getNews();
			this.getIndexList();

			if (localStorage.getItem("tokend")) {
				this.isLogin = false;
			} else {
				this.isLogin = true;
			}
			this.getMarkListStock();
			this.getClickZhong()
		},
		methods: {
      readData() {
        this.$server.post("/user/notice", {
          type: "twd"
        }).then((res) => {
          if (res.status == 1) {
            let list = res.data;
            let length = list.length
            let a
            for (a = 0; a < length; a++) {
              let oldRead = {id:[]}
              if(localStorage.getItem("readMsg")){
                oldRead = JSON.parse(localStorage.getItem("readMsg"))
              }
              let hasValue = oldRead.id.includes(list[a].id.toString())
              if (!hasValue) {
                this.readNum += 1
              }
            }
          }
        });
      },
			// 处理功能tab点击
			handleTabClick(item) {
				if (item.url === 'kefu') {
					this.getConfig();
				} else {
					// 根据当前市场类型添加参数
					const marketType = this.tabIndex === 0 ? 'taiwan' : 'us';
					const urlWithParams = `${item.url}?market=${marketType}`;
					this.$toPage(urlWithParams);
				}
			},
			tozqDetail(type) {
				const marketType = this.tabIndex === 0 ? 'taiwan' : 'us';
				this.$router.push({
					path: `/trade/index?zqShow=${type}&market=${marketType}`
				})
			},
			getClickZhong() {
				let _this = this;
				let now = new Date();
				let unixEpoch = new Date(0);
				let diff = now - unixEpoch;
				let fullDay = Math.floor(diff / (1000 * 60 * 60 * 24))
				if (window.localStorage.getItem('zhongDayNew')) {
					if (parseInt(window.localStorage.getItem('zhongDayNew')) < fullDay) {
						_this.getZhong(fullDay)
					}
				} else {
					_this.getZhong(fullDay)
				}
			},
			getZhong(fullDay) {
				let _this = this;
				this.$server
					.post("/transaction/is_zhongqian").then((str) => {
						if (str) {
							if (str.data.length > 0) {
								_this.zqShow = true
								_this.zhong = str.data[0]
								_this.zhongList = str.data
								window.localStorage.setItem('zhongDayNew', fullDay.toString())
							}else{
                this.$server
                    .post("/trade/usernewstocklist", {
                      type: "twd",
                      buy_type: 1
                    })
                    .then((res) => {
                      if (res.status == 1) {
                        res.data.forEach(item=>{
                          if(item.status==1){
                            _this.zqShow = true
                            _this.zhong = item
                            _this.zhongList = res.data
                            window.localStorage.setItem('zhongDayNew', fullDay.toString())
                          }
                        })
                      }
                    });
              }
						}
					});
			},
      toTips(){
        this.$toast("資格審核進行中...");
      },
			setChartOption(item, symbol, downup) {

				let sltDom = echarts.getInstanceByDom(document.getElementById("chart" + symbol))
				if (sltDom == null) {
					sltDom = echarts.init(document.getElementById("chart" + symbol));
				}
				// 缩略图模拟数据
				let option = {
					xAxis: {
						type: "category",
						data: item.map(item1 => item1.timestamp),
						show: false,
					},
					grid: [{
						//蜡烛图的位置
						left: "0%",
						right: "0%",
						top: "0%",
						bottom: "0%",
						height: "100%",
						x: "0",
						y: "0",
					}, ],
					yAxis: {
						type: "value",
						scale: true, //自适应显示
						show: false,
					},
					series: [{
						data: item.map(item1 => item1.close),
						type: "line",
						smooth: true,
						// 设置渐变红色的填充和线条颜色
						areaStyle: {
							color: downup > 0 ? '#ba3b3a' : '#68a03d',
							opacity: 0.2
						},
						lineStyle: {
							color: downup > 0 ? '#ba3b3a' : '#68a03d',
							width: 1.5
						},
						showSymbol: false, // 取消线上标记点
					}, ],
				};
				// 渲染图表
				sltDom.setOption(option);
			},
			getMarkListStock() {
				// 根据当前选择的市场获取股票列表
				this.$server
					.post("/parameter/top", {
						type: this.currentMarketType
					})
					.then((res) => {
						this.stocklist = res.data;
						this.changeList(1)
					});
			},
			changeList(type) {
				this.navItem = type
				if (type == 0) {
					this.show = !this.show;
					// 成交額 排序
					if (this.show) {
						this.stocklist = this.stocklist.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.stocklist = this.stocklist.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else if (type == 1) {
					this.show1 = !this.show1;
					// 漲跌排序
					this.stocklist = this.stocklist.sort(
						(a, b) => Number(b.gain) - Number(a.gain)
					);
				} else {
					this.stocklist = this.stocklist.sort(
						(a, b) => Number(a.gain) - Number(b.gain)
					);
				}
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
			getIndexList() {
				// 获取台灣指数
				this.$server
					.post("/parameter/zhishu", {
						type: "twd"
					})
					.then((res) => {
						this.loading1 = false;
						if (res && res.data) {
							this.indexList = res.data;
							this.indexList.forEach((item) => {
								this.getChart(item.kline, item.symbol.replace('^', ''), Number(item.gain) > 0 ?
									1 : -1)
							})
						}
					});

				// 获取美股指数
				this.getUsIndexList();
			},
			// 获取美股指数列表
			getUsIndexList() {
				this.$server
					.post("/parameter/zhishu", {
						type: "usd"
					})
					.then((res) => {
						if (res && res.data) {
							this.usIndexList = res.data;
							this.usIndexList.forEach((item) => {
								this.getChart(item.kline, item.symbol.replace('^', ''), Number(item.gain) > 0 ?
									1 : -1)
							})
						}
					});
			},
			getChart(items, symbol, downup) {
				if (!items) {
					return
				}
				let arr = []
				items.forEach(item => {
					let kLineModel = {
						open: item.c === null ? 0 : item.c / 1,
						close: item.c === null ? 0 : item.c / 1,
						high: item.c === null ? 0 : item.c / 1,
						low: item.c === null ? 0 : item.c / 1,
						volume: item.s === null ? 0 : item.s * 10,
						timestamp: item.t * 1
					};
					if (arr.length <= 30) {
						arr.push(kLineModel)
					}
				})
				this.$nextTick(() => {
					this.setChartOption(arr, symbol, downup)
				})
			},
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			async getUserInfo() {
				// this.$refs.loading.open(); //开启加载
				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
				}
			},
			getNews() {
				this.$server
					.post("/common/newss", {
						exchange: "tw",
						lang: "cn",
					})
					.then((res) => {
						this.loading2 = false;
						let arr = [];
						// this.articleList = [...this.articleList, ...arr];
						res.data.result.forEach((item, i) => {
							if (i < 20) {
								arr.push(item);
							}
						});

						this.articleList = arr;
					});
			},
		},
	};
</script>

<style scoped lang="less">
	::-webkit-scrollbar {
		display: none;
	}

	.page {
		min-height: 100vh;
		padding: 0rem 0 0.7rem;

		.topHead {
			// position: fixed;
			// width: 100%;
			height: 2.8rem;
			background: url('../../assets/v1/hbg.png') no-repeat top center/100%;
      .top-tab{
				grid-column-gap: 0.12rem;
				.item{
					font-size: 0.16rem;
					color: #efecec;
					&.active{
						font-weight: bold;
						font-size: 0.17rem;
						color: #ffffff;
					}
				}
			}
			.name {
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}

			.code {
				margin-left: 0.1rem;
				margin-top: 0.05rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #B0C0E4;
			}

			.xx {
				margin-left: 0.16rem;
			}
		}
	}

	.zx {
		// position: fixed;
		// top: 2.6rem;
		// left: 0;
		position: relative;
		z-index: 2;

		.zx-cot {
			// margin-top: -1.8rem;
			background-color: rgba(5, 8, 13, 0.2);

			.zx-list {
				overflow-x: scroll;

				.zx-item {
					padding: 0.1rem;
					margin-right: 0.03rem;
					min-width: 2.3rem;

					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.1rem;
						color: #B0C0E4;
					}

					.price {
						margin: 0 0.1rem;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.1rem;
						color: #FFFFFF;
					}

					.per {
						div {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.1rem;
						}

						.icon {
							margin-right: 0.05rem;
						}

						.mr10 {
							margin-right: 0.1rem;
						}
					}
				}
			}
		}
	}

	.titles {
		padding: 0.08rem;

		.tit {
			padding-left: 0.12rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.16rem;
			color: #182133;
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				width: 0.04rem;
				height: 0.23rem;
				background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
			}
		}
	}

	.index {
		overflow-x: scroll;
		padding: 0 0.15rem;

		.nums {
			padding: 0.1rem 0;
			width: 580%;
			.nums-item {
				width: 50%;
				margin-right: 0.1rem;
				padding: 0.15rem 0.1rem;
				background: #FFFFFF;
				border-radius: 0.13rem;

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #0C061C;
				}

				.t {
					font-family: OPPOSans, OPPOSans;
					font-weight: normal;
					font-size: 0.18rem;
					color: #0C061C;
					margin: 0.1rem 0;
				}

				.t1 {
					font-family: OPPOSans, OPPOSans;
					font-weight: normal;
					font-size: 0.15rem;
				}

				.t2 {
					margin-top: 0.1rem;

					.c1 {
						width: 0.06rem;
						height: 0.06rem;
						border-radius: 50%;
						background: #8e8e8e;
						margin-right: 0.05rem;
					}

					font-family: PingFang TC, PingFang TC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #8e8e8e;
				}
			}
		}
	}

	.hbg {
		padding: 0.15rem;

		.title {
			color: #fff;
		}

		.tips {
			margin-top: 0.3rem;

			.info {
				.t {
					font-size: 0.16rem;
					color: #ffffff;
				}

				.t1 {
					font-size: 0.14rem;
					color: #ffffff;
					margin-top: 0.12rem;
				}
			}

			.dl-btn {
				width: 0.9rem;
				height: 0.28rem;
				border-radius: 0.3rem 0.3rem 0.3rem 0.3rem;
				border: 0.01rem solid rgba(255, 255, 255, 0.9);
				// opacity: 0.8;
				line-height: 0.28rem;
				text-align: center;
				color: #fff;
				font-size: 0.12rem;
			}
		}
	}

	.title {
		position: relative;
		padding: 0.2rem 0.08rem 0;
		z-index: 9;

		.t01 {
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.19rem;
			color: #FFFFFF;
			padding-bottom: 0.05rem;
		}

		.t02 {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #fff;
			padding: 0.05rem 0 0;

			span {
				color: #DCA25C;
			}
		}
	}

	.news02 {
		position: relative;
		z-index: 10;
		margin: 0.08rem;
		overflow-x: scroll;

		.news-list {
			margin-right: 0.08rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.02rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;
			min-width: 3.4rem;

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #333333;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.time {
				margin-top: 0.2rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #5D6C8D;
			}
		}
	}

	.news {
		padding: 0 0.1rem;

		.news-list {
			width: 100%;
			border-radius: 0.06rem;

			.news-item {
				position: relative;
				// width: 100%;
				height: 1.8rem;
				overflow: hidden;

				.t {
					font-weight: 600;
					color: #fff;
					// text-overflow: ellipsis;
					// white-space: nowrap;
					// overflow: hidden;
					// width: 90%;
				}

				.time {
					font-size: 0.12rem;
					color: #fff;
					margin-top: 0.1rem;
				}

				.txt {
					position: absolute;
					padding: 0 0.1rem;
					top: 0.15rem;
				}

				img {
					width: 100%;
					height: 100%;
					border-radius: 0.06rem;
				}
			}
		}

		.news-list02 {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0 0.1rem;

			.news-item02 {
				padding: 0.05rem 0;

				img {
					width: 0.72rem;
					height: 0.48rem;
				}

				.txt {
					margin-left: 0.1rem;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #182133;
				}

				.time {
					margin-top: 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #5D6C8D;
				}
			}
		}
	}

	.func {
		position: relative;
		z-index: 5;
		margin: 0.08rem;
		margin-top: -0.5rem;
		background: #fff;
		border-radius: .1rem;
		padding: .06rem 0.12rem;

		.tab {
			flex-wrap: wrap;

			.tab-item {
				width: 32%;
				align-self: flex-start;
				text-align: center;
				padding: 0.1rem;
				height: 0.73rem;
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0, 0, 0, 0.16);
				border-radius: 0.07rem;
				margin: 0.06rem 0;

				.icon {
					margin: 0 auto 0.08rem;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #000000;
				}
			}
		}
	}

	.rm-list {
		margin: 0.08rem;

		.nav {
			margin: 0.15rem 0;

			.navItem {
				margin-right: 0.1rem;
				padding: 0 0.2rem;
				height: 0.26rem;
				background: #D8E3F6;
				border-radius: 0.13rem;
			}

			.active {
				background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
				color: #fff;
			}
		}

		.rmList {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0 0.1rem;

			.rm-item {
				border-bottom: 0.01rem solid #F6F8FE;
				padding: 0.1rem 0;

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #182133;
				}

				.code {
					margin-top: 0.05rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #69717D;
				}

				.price {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.17rem;
				}

				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
				}
			}
		}

	}

	.tz-list {
		margin: 0.15rem 0 0.2rem;

		.tz-item {
			margin-right: 0.1rem;
			flex-direction: column;
			padding: 0.2rem;
			align-items: flex-start;

			.t {
				font-size: 0.12rem;
				color: #ffffff;
			}

			.t1 {
				font-weight: 600;
				font-size: 0.16rem;
				color: #78e02a;
				margin-top: 0.05rem;
			}
		}
	}

	.hot-list {
		overflow: hidden;
		overflow-x: scroll;
		margin: 0.15rem 0 0.2rem;

		.inner {
			width: 600%; //20

			.hot-item {
				width: 0.93rem;
				background: linear-gradient(180deg, #434851 0%, #2c343b 100%);
				border-radius: 0.08rem;
				margin-right: 0.1rem;
				padding: 0.1rem;
				text-align: center;

				&:nth-child(2n) {
					background: linear-gradient(180deg, #a4aab4 0%, #657586 100%);
				}

				.logo {
					img {
						width: 0.32rem;
						height: 0.32rem;
						border-radius: 0.04rem;
					}
				}

				.name {
					font-size: 0.12rem;
					color: #fff;
					width: 100%;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
				}

				.code {
					font-size: 0.1rem;
					color: #c4c4c4;
					margin: 0.05rem 0;
				}

				.price {
					color: #fff;
					font-size: 0.1rem;
				}

				.per {
					margin-top: 0.05rem;

					span {
						font-size: 0.1rem;
						color: #fff;
					}
				}
			}
		}
	}

	.index-list {
		padding: 0rem 0 0.2rem;

		.index-item {
			width: 33%;

			&.center {
				border-left: 0.01rem solid #dadada;
				border-right: 0.01rem solid #dadada;
				padding: 0 0.1rem;
				margin: 0 0.1rem;
			}
		}

		.name {
			font-size: 0.12rem;
			color: #000000;
		}

		.price {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
			margin: 0.05rem 0;
		}

		.t {
			font-size: 0.1rem;
		}
	}

	.cot {
		padding: 0.2rem 0.1rem;

		.mtb20 {
			margin: 0.2rem 0 0;
		}

		.item {
			.title {
				font-size: 0.16rem;
			}

			.titles {
				margin-bottom: 0.05rem;

				div {
					font-size: 0.12rem;
					color: #535353;
				}
			}

			.list {
				.list-item {
					padding: 0.1rem 0;

					.logo {
						margin-right: 0.1rem;

						img {
							width: 0.32rem;
							height: 0.32rem;
							border-radius: 0.04rem;
						}
					}

					.name {
						font-size: 0.12rem;
						color: #000000;
					}

					.code {
						font-size: 0.1rem;
						color: #c4c4c4;
						margin-left: 0.05rem;
					}

					.per {
						span {
							font-size: 0.1rem;
							margin-right: 0.1rem;
						}
					}

					.price {
						&.center {
							margin: 0 0.1rem;
						}

						div {
							font-size: 0.1rem;
							color: #0c0c0c;
							background: #f3f3f3;
							border-radius: 0.02rem;
							padding: 0.05rem 0;
						}
					}
				}
			}

			.more {
				padding: 0.1rem;

				div {
					font-size: 0.12rem;
					color: #7f7f7f;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}
		}
	}

	.red {
		color: #ba3b3a;
	}

	.green {
		color: #68a03d;
	}

	.red-bg {
		background-color: #af1d1c !important;
		color: #fff !important;
	}

	.green-bg {
		background-color: #4f911d !important;
		color: #fff !important;
	}

	.ipo {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 1.66rem;
		margin: .15rem .08rem .22rem .08rem;
		padding: .18rem .19rem;
		background: #FFFFFF;
		box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0, 0, 0, 0.16);
		border-radius: 0.07rem;

		.items {
			.num {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.36rem;
				color: #DCA25C;
			}

			.fh {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.16rem;
				color: #DCA25C;
				margin-top: .21rem;
			}

			.text {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #333333;
			}
		}

		.texta {
			margin-top: .18rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.09rem;
			color: #5D6C8D;
		}
	}

	.zqbg {
		padding: .2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		position: relative;

		.title {
			font-size: .18rem;
			color: #333;
			font-weight: bold;
			text-align: center;
		}

		.item,
		.item1 {
			margin-top: .1rem;
			font-size: .14rem;
			color: #999;
		}

		.item1 {
			color: #333;
		}
	}

	.wd-100 {
		width: 100%;
	}

	.btns {
		margin-top: .3rem;
		font-size: .16rem;
		color: #FF2415;
		font-weight: bold;
		position: relative;
		z-index: 99999;
	}
</style>