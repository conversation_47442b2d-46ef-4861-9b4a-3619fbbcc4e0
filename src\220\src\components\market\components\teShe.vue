<template>
	<div class="cot">
		<van-skeleton title :row="20" :loading="loading">
			<div class="list">
				<div class="flex flex-b titles">
					<div class="flex-1">{{ $t("newt").t57 }}</div>
					<div class="flex-1 t-r">{{ $t("newt").t58 }}</div>
					<div class="flex-1 t-r">{{ $t("newt").t59 }}</div>
				</div>

				<div class="lists">
					<div class="list-item flex flex-b" v-for="(item, idx) in list" :key="idx"
						@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item)">
						<div class="flex col">
							<!-- <div class="img">
								<img :src="item.logo" />
							</div> -->
							<div>
								<div class="name"> {{ item.name }} </div>
								<div class="code"> {{ item.symbol }} </div>
							</div>
						</div>
						<div class="t-r col">
							<div class="price red" :class="{ green: Number(item.gainValue) < 0, }">
								{{ $formatMoney(item.price,2) || 0 }}
							</div>

							<!-- <div class="t red" :class="{ green: Number(item.gainValue) < 0, }" >
							  {{ item.gainValue > 0 ? "+" : "" }}{{ item.gainValue || 0 }}
							</div> -->
						</div>

						<div class="col flex flex-e">
							<div class="icon" :class="Number(item.gainValue) >0?'up1':'down1'" style="margin-right: 0.05rem;"></div>
							<div class="per red" :class="{ 'green': Number(item.gainValue) < 0, }">
                {{stockType=='try'?'%':''}}{{ item.gainValue > 0 ? "+" : "" }}{{ item.gain.toFixed(2) }}{{stockType=='try'?'':'%'}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-skeleton>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "teShe",
		components: {},
		data() {
			return {
				loading: true,
				type: "returns_top",
				typeList: [{
						name: this.$t("newt").t60,
						type: "returns_top",
					},
					{
						name: this.$t("newt").t61,
						type: "returns_bottom",
					},
					{
						name: this.$t("newt").t62,
						type: "new_high_price",
					},
					{
						name: this.$t("newt").t63,
						type: "new_low_price",
					},
					// {
					//   name: '上限',
					//   type: 'upper_limit_price'
					// },
					// {
					//   name: '下限',
					//   type: 'lower_limit_price'
					// },
					// {
					//   name: '突破上限',
					//   type: 'out_of_upper_limit_price'
					// },
					// {
					//   name: '突破下限',
					//   type: 'out_of_lower_limit_price'
					// }
				],
				list: [],
			};
		},
    props: {
      stockType: {
        type: String,
        default: "",
      },
    },
		created() {},
		mounted() {
			this.getInfo();
		},
		onLoad() {},
		methods: {
			changeType(type) {
				this.type = type;
				this.getInfo();
			},
			getInfo() {
				this.$refs.loading.open(); //开启加载
				this.$server.post("/parameter/top", {
					type: this.stockType
				}).then((res) => {
					this.$emit("upData");

					this.$refs.loading && this.$refs.loading.close();
					this.loading = false;
					if (res&&res.data) {
						this.list = res.data;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.cot{
		margin: 0 0.12rem;
		background: #FFFFFF;
		border-radius: 0.13rem;
	}
	
	.red-bg {
		background-color: #df4645;
	}

	.green-bg {
		background-color: #3561e5;
	}

	.list {
		margin-top:.08rem;
		.titles {
			padding: 0 .2rem;
			border-bottom: 0.01rem solid #f5f5f5;
			font-size: .12rem;
			color: #8B8D91;
			height:.41rem;
		}
		
		.lists {
			.list-item {
				padding: .1rem .12rem;
				border-bottom: 0.01rem solid #ebebeb;
				font-size: 0.12rem;
				
				&:last-child {
					border-bottom: 0;
				}
				
				.col{
					width:33%;
				}
		
				.img {
					margin-right: .12rem;
					width: .28rem;
					height: .28rem;
					img{
						width: .28rem;
						height: .28rem;
						border-radius: .02rem;
					}
				}
		
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.15rem;
					color: #0C061C;
				}
		
				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #999999;
					margin-top: 0.02rem;
				}
		
				.price {
					font-family: OPPOSans, OPPOSans;
					font-weight: 600;
					font-size: 0.14rem;
				}
				.per{
					font-family: OPPOSans, OPPOSans;
					font-weight: normal;
					font-size: 0.14rem;
				}
			}
		}
		
	}
</style>