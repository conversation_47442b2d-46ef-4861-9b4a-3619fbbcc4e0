<template>
  <!-- 概括 -->
  <div>
    <!-- 投资者趋势 -->
    <investor-trends :symbol="id"></investor-trends>

    <div class="about">
      <div class="flex flex-b">
        <div class="t">{{ ko_name }}</div>
        <!-- <div class="t1" @click="showMore = false">{{ $t('查看更多') }}</div> -->
      </div>
      <!-- :class="{ show: showMore }" -->
      <div class="txt ">
        {{ description }}
        <!-- <div class="t1" @click="showMore = true">{{ $t('收起') }}</div> -->
      </div>
    </div>

    <div class="info flex flex-b">
      <div class="flex-1">
        <div class="">
          <div class="t">
            {{ $t("newt").a43 }}{{ info.marketcap_rank }}{{ $t("newt").a44 }}
          </div>
          <div class="">{{ $t("newt").a42 }}</div>
        </div>
        <div class="">
          <div class="t num-font">
            {{ $formatMoney(info.sharesout) }}{{ $t("newt").a46 }}
          </div>
          <div class="">{{ $t("newt").a45 }}</div>
        </div>
        <div class="">
          <div class="t">{{ info.sector }}</div>
          <div class="">{{ $t("newt").a47 }}</div>
        </div>
        <div class="">
          <div class="red t num-font">{{ $formatMoney(info.year_high) }}</div>
          <div class="">{{ $t("newt").a48 }}</div>
        </div>
      </div>
      <div class="flex-1">
        <div class="">
          <div class="t num-font">
            {{ $formatMoney(info.marketcap) }}{{ $t("newt").t50 }}
          </div>
          <div class="">{{ $t("newt").t78 }}</div>
        </div>
        <div class="">
          <div class="t">{{ info.foreigners_pie }}%</div>
          <div class="">{{ $t("newt").a49 }}</div>
        </div>
        <div class="">
          <div class="t">{{ info.industry }}</div>
          <div class="">{{ $t("newt").a50 }}</div>
        </div>
        <div class="">
          <div class="blue t num-font">{{ $formatMoney(info.year_low) }}</div>
          <div class="">{{ $t("newt").a51 }}</div>
        </div>
      </div>
    </div>

    <!-- 投资者 -->
    <investor-info :symbol="id"></investor-info>

    <div class="xs">
      <div class="tb flex flex-b">
        <div class="flex-1">
          <div class="t">{{ $t("newt").a52 }}</div>
          <div class="t1">{{ $t("newt").a53 }} {{ years[3] }}</div>
        </div>

        <div class="flex flex-e  tabs">
          <div class="" :class="{ active: type == 0 }" @click="changeType(0)">
            {{ $t("newt").a54 }}
          </div>
          <div class="" :class="{ active: type == 1 }" @click="changeType(1)">
            {{ $t("newt").a55 }}
          </div>
        </div>
      </div>

      <div class="nums flex flex-b">
        <div class="item flex flex-b">
          <div class="flex-1">
            <div class="t1">{{ $t("newt").a56 }}</div>
            <div class="t-c">
              <div class="t2" :class="{ die: firsetData[29] < 0 }">
                {{ firsetData[29] < 0 ? "" : "+"
                }}{{ $formatMoney(firsetData[29]) }}{{ $t("newt").t50 }}
              </div>
              <span class="t3" :class="{ die: firsetData[42] < 0 }">
                {{ firsetData[42] < 0 ? "" : "+" }}{{ firsetData[42] }}%
              </span>
            </div>
          </div>
          <div class="per">
            <!-- <u-circle-progress
              border-width="16"
              width="140"
              :percent="showPercent(firsetData[42])"
              inactive-color="#F4F5F7"
              :active-color="firsetData[42] < 0 ? '#30E0A1' : '#E73E59'"
              bg-color="transparent"
            >
              <div class="u-progress-info">
                {{ firsetData[42] < 0 ? "" : "+" }}{{ firsetData[42] }}%
              </div>
            </u-circle-progress> -->
          </div>
        </div>
        <div class="item flex flex-b center">
          <div class="flex-1">
            <div class="t1">{{ $t("newt").a57 }}</div>
            <div class="t-c">
              <div class="t2" :class="{ die: firsetData[32] < 0 }">
                {{ firsetData[32] < 0 ? "" : "+"
                }}{{ $formatMoney(firsetData[32]) }}{{ $t("newt").t50 }}
              </div>
              <span class="t3" :class="{ die: firsetData[81] < 0 }">
                {{ firsetData[81] < 0 ? "" : "+" }}{{ firsetData[81] }}%
              </span>
            </div>
          </div>

          <div class="per">
            <!-- <u-circle-progress
              border-width="16"
              width="140"
              :percent="showPercent(firsetData[81])"
              inactive-color="#F4F5F7"
              :active-color="firsetData[81] < 0 ? '#30E0A1' : '#E73E59'"
              bg-color="transparent"
            >
              <div class="u-progress-info">
                {{ firsetData[81] < 0 ? "" : "+" }}{{ firsetData[81] }}%
              </div>
            </u-circle-progress> -->
          </div>
        </div>
        <div class="item flex flex-b">
          <div class=" flex-1">
            <div class="t1">{{ $t("newt").a58 }}</div>
            <div class="t-c">
              <div class="t2" :class="{ die: firsetData[36] < 0 }">
                {{ firsetData[36] < 0 ? "" : "+"
                }}{{ $formatMoney(firsetData[36]) }}{{ $t("newt").t50 }}
              </div>
              <span class="t3" :class="{ die: firsetData[83] < 0 }">
                {{ firsetData[83] < 0 ? "" : "+" }}{{ firsetData[83] }}%
              </span>
            </div>
          </div>

          <div class="per">
            <!-- <u-circle-progress
              border-width="16"
              width="140"
              :percent="showPercent(firsetData[83])"
              inactive-color="#F4F5F7"
              :active-color="firsetData[83] < 0 ? '#30E0A1' : '#E73E59'"
              bg-color="transparent"
            >
              <div class="u-progress-info">
                {{ firsetData[83] < 0 ? "" : "+" }}{{ firsetData[83] }}%
              </div>
            </u-circle-progress> -->
          </div>
        </div>
      </div>

      <!-- 图表 -->
      <div class="chart-box">
        <div class="flex flex-b">
          <div class="flex-1"></div>
          <div class="flex flex-b tbs">
            <div
              class=""
              :class="{ active: types == 0 }"
              @click="changeTypes(0)"
            >
              {{ $t("newt").a59 }}
            </div>
            <div
              class=""
              :class="{ active: types == 1 }"
              @click="changeTypes(1)"
            >
              {{ $t("newt").a60 }}
            </div>
            <div
              class=""
              :class="{ active: types == 2 }"
              @click="changeTypes(2)"
            >
              {{ $t("newt").a61 }}
            </div>
          </div>
        </div>

        <!-- 圖表 -->
        <div class="chart" id="main"></div>
      </div>

      <!-- 卖空状态 -->
      <buy-status :symbol="id"></buy-status>
    </div>
    <loading ref="loading" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import investorInfo from "./gkItems/investorInfo.vue";
import buyStatus from "./gkItems/buyStatus.vue";
import investorTrends from "./gkItems/investorTrends.vue";
export default {
  name: "gkInfo",
  props: {
    symbol: {
      type: String,
      default: "",
    },
  },
  components: {
    investorInfo,
    buyStatus,
    investorTrends,
  },
  data() {
    return {
      ko_name: "",
      showMore: true,
      info: {},
      description: "",
      type: 0,
      types: 0,
      id: "",
      firsetData: {},
      numData: {},
      years: [],
      myChart: null,
    };
  },
  computed: {
    showPercent() {
      return (str) => {
        if (!str || str === null || str == undefined) return 0;
        let isNum = str.indexOf("-");
        if (isNum > -1) {
          let num1 = Number(str.replace("-", ""));
          num1 = num1 > 100 ? 100 : num1;
          return num1;
        } else {
          let num2 = Number(str);
          num2 = num2 > 100 ? 100 : num2;
          return num2;
        }
      };
    },
  },
  created() {},
  mounted() {
    this.getInfo();
  },
  methods: {
    // 切换季度,年度显示
    changeType(type) {
      this.type = type;
      this.getNums();
      this.changeTypes(0);
    },
    // 切换图表显示
    changeTypes(type) {
      this.types = type;
      let arr = [];
      switch (type) {
        case 0:
          arr = this.numData.arr;
          break;
        case 1:
          arr = this.numData.arr1;
          break;
        case 2:
          arr = this.numData.arr2;
          break;
        default:
          break;
      }

      let newOption = {
        series: [
          {
            data: [...arr],
          },
        ],
      };
      // console.log('myChart', this.myChart);
      // 切换更新数据
      this.myChart.setOption(newOption);
    },
    getInfo() {
      this.$refs.loading.open(); //开启加载

      // 获取概括
      this.$server.post("/transaction/gkxx", { symbol: this.symbol }).then((res) => {
        if (res.status == 1) {
          this.info = res.data;
        }
      });
      // 获取公司简介
      this.$server
        .post("/transaction/symbol_details", { symbol: this.symbol })
        .then((res) => {
          this.$refs.loading.close();

          if (res.status == 1) {
            this.id = res.data[this.symbol].id;
            this.description = res.data[this.symbol].description;
            this.ko_name = res.data[this.symbol].ko_name;
            // 要上面获取的id
            this.getNums();
          }
        });
    },
    getNums() {
      let type = "";
      type = this.type == 0 ? "q" : "y";
      this.$server
        .post("/transaction/xiaoshouer", { id: this.id, type })
        .then((res) => {
          if (res.status == 1) {
            this.years = [];
            this.numData = {};

            // 有某些数据 res.data.data[0].fields[36] 没有这一项，默认显示为0
            if (!this.type) {
              // 显示最近的销售
              this.firsetData = {
                "29": res.data.data[0].fields[29]
                  ? (res.data.data[0].fields[29].value / 100000000).toFixed(0)
                  : 0,
                "32": res.data.data[0].fields[32]
                  ? (res.data.data[0].fields[32].value / 100000000).toFixed(0)
                  : 0,
                "36": res.data.data[0].fields[36]
                  ? (res.data.data[0].fields[36].value / 100000000).toFixed(0)
                  : 0,
                "42": res.data.data[0].fields[42]
                  ? res.data.data[0].fields[42].value.toFixed(2)
                  : 0,
                "81": res.data.data[0].fields[81]
                  ? res.data.data[0].fields[81].value.toFixed(2)
                  : 0,
                "83": res.data.data[0].fields[83]
                  ? res.data.data[0].fields[83].value.toFixed(2)
                  : 0,
              };
            } else {
              // 是年销售就是下面这些
              // 43 82 84
              this.firsetData = {
                "29": res.data.data[0].fields[29]
                  ? (res.data.data[0].fields[29].value / 100000000).toFixed(0)
                  : 0,
                "32": res.data.data[0].fields[32]
                  ? (res.data.data[0].fields[32].value / 100000000).toFixed(0)
                  : 0,
                "36": res.data.data[0].fields[36]
                  ? (res.data.data[0].fields[36].value / 100000000).toFixed(0)
                  : 0,
                "42": res.data.data[0].fields[43]
                  ? res.data.data[0].fields[43].value.toFixed(2)
                  : 0,
                "81": res.data.data[0].fields[82]
                  ? res.data.data[0].fields[82].value.toFixed(2)
                  : 0,
                "83": res.data.data[0].fields[84]
                  ? res.data.data[0].fields[84].value.toFixed(2)
                  : 0,
              };
            }

            let arr = [];
            let arr1 = [];
            let arr2 = [];
            res.data.data.forEach((item) => {
              arr.push(
                item.fields[29]
                  ? (item.fields[29].value / 100000000).toFixed(0)
                  : 0
              );
              arr1.push(
                item.fields[32]
                  ? (item.fields[32].value / 100000000).toFixed(0)
                  : 0
              );
              arr2.push(
                item.fields[36]
                  ? (item.fields[36].value / 100000000).toFixed(0)
                  : 0
              );
              this.years.push(item.report_year_month);
            });

            this.years = this.years.reverse();
            this.numData = {
              arr: arr.reverse(),
              arr1: arr1.reverse(),
              arr2: arr2.reverse(),
            };
            console.log(" this.numData", this.numData);
            this.initData();
          }
        });
    },
    initData() {
      if (this.myChart !== null) {
        this.myChart.dispose(); //重新初始化，先销毁之前的
      }

      let chartDom = document.getElementById("main");
      this.myChart = echarts.init(chartDom);
      let option;

      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        xAxis: {
          type: "category",
          data: [...this.years],
          // 坐标轴显示内容
          axisLabel: {
            interval: 0,
            rotate: 20,
            color: "#666",
            fontSize: "12",
          },
          // 坐标轴线
          axisLine: {
            show: true,
            lineStyle: {
              color: "#E0E0E0",
            },
          },
          // 坐标轴刻度
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          // min: 'dataMin', //取最小值为最小刻度
          // max: 'dataMax', //取最大值为最大刻度
          axisLine: {
            show: false, //是否显示
            lineStyle: {
              color: "#E0E0E0",
            },
          },
          type: "value",
          splitLine: {
            lineStyle: {
              color: "#E0E0E0",
            },
          },
          axisLabel: {
            formatter: (value) => {
              return `${this.$formatMoney(value) || 0} ${this.$t("亿")}`;
            },
            color: "#666666",
            fontSize: "12",
          },
        },
        grid: {
          // 圖標距離邊界距離（位置）
          left: "5%",
          right: "5%",
          bottom: "2%",
          top: "8%",
          containLabel: true,
        },
        series: [
          {
            data: [...this.numData.arr],
            type: "line",
            lineStyle: {
              color: "#6970AF",
            },
            symbol: "circle", //将小圆点改成实心 不写symbol默认空心
            symbolSize: 6, //小圆点的大小
            itemStyle: {
              color: "#6970AF", //小圆点和线的颜色
            },
          },
        ],
      };
      option && this.myChart.setOption(option);
    },
  },
};
</script>

<style scoped lang="less">
.chart {
  width: 100%;
  height: 3rem;
}

.xs {
  .tb {
    padding: 0.1rem;
    border-bottom: 0.01rem solid #f5f5f5;
    .t {
      font-weight: bold;
      color: #191919;
    }
    .t1 {
      font-size: 0.12rem;
      color: #808080;
    }
    .tabs {
      background: #ededed;
      border-radius: 0.02rem;
      &.noBorder {
        border-top: 0;
      }
      div {
        flex: 1;
        padding: 0.05rem 0.1rem;
        font-size: 0.12rem;
        color: #3b3b3b;
        text-align: center;
        border-radius: 0.02rem;
      }
      .active {
        color: #ffffff;
        background: #6970af;
      }
    }
  }

  .nums {
    padding: 0.1rem;

    .t {
      font-size: 0.12rem;
      color: #7f7f7f;
    }
    .u-progress-info {
      font-size: 20px;
      color: #252525;
      text-align: center;
      font-weight: bold;
    }

    .item {
      width: 32%;
      background: #ffffff;
      box-shadow: 0rem 0.02rem 0.04rem 0rem rgba(0, 0, 0, 0.15),
        0rem -0.01rem 0.01rem 0rem rgba(0, 0, 0, 0.1);
      border-radius: 0.02rem;
      padding: 0.15rem 0.05rem;
      .t1 {
        text-align: center;
      }
      .t2 {
        font-weight: 600;
        font-size: 0.16rem;
        color: #212121;
        padding: 0.1rem 0;
      }
      .t3 {
        background: #f8dfd9;
        border-radius: 0.3rem;
        padding: 0.05rem 0.1rem;
        font-weight: 600;
        font-size: 0.12rem;
        color: #bb1a23;

        &.die {
          color: #4f8672;
          background: #f0fffa;
        }
      }
    }
  }

  .chart-box {
    padding: 0.1rem;
    .tbs {
      background: #ededed;
      border-radius: 0.02rem;
      div {
        font-size: 0.12rem;
        color: #3b3b3b;
        padding: 0.05rem 0.1rem;
      }
      .active {
        background: #6970af;
        color: #fff;
      }
    }
  }
}

.about {
  padding: 0 0.1rem;
  .t {
    font-weight: 600;
    margin-bottom: 0.05rem;
  }
  .txt {
    font-size: 0.12rem;
    color: #9b9ba3;
    line-height: 0.24rem;
  }
}

.info {
  padding: 0 0.1rem 0.1rem;
  border-bottom: 0.01rem solid #f5f5f5;
  line-height: 0.2rem;
  div {
    font-size: 0.12rem;
    color: #808080;
  }
  .t {
    font-weight: 600;
    font-size: 0.12rem;
    color: #000;
    margin-top: 0.1rem;
  }
  .red {
    color: #c04649;
  }
  .blue {
    color: #4f8672;
  }
}
</style>
