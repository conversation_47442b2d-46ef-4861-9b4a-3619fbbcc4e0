<template>
  <!-- 指数详情 -->
  <div class="page ">
    <top-back :title="$t('new').t2"></top-back>
    <!-- <div
      class="iszx icon animate__animated animate__fadeIn"
      :class="is_zixuan ? 'ysc' : 'wsc'"
      @click="addSelect(details)"
    ></div> -->

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <div class="top-data" v-if="details">
        <div class="flex flex-b tp">
          <div>
            <div class="name">{{ details.ko_name }}</div>
            <div class="code">{{ details.code }}</div>
          </div>
          <div>
            <div class="flex flex-e">
              <div class="price " :class="details.gain >= 0 ? 'red' : 'green'">
                {{ $formatMoney(details.close, 2) }}
              </div>
              <div
                class="icon animate__animated animate__fadeIn"
                :class="details.gain >= 0 ? 'up1' : 'down1'"
              ></div>
            </div>
            <div class="t t-r" :class="details.gain >= 0 ? 'red' : 'green'">
              {{ Number(details.gain) > 0 ? "+" : ""
              }}{{ Number(details.gain).toFixed(2) }} (
              {{ Number(details.gain) > 0 ? "+" : ""
              }}{{ Number(details.gainValue).toFixed(2) }}%)
            </div>
          </div>
        </div>

        <div class="list flex flex-b">
          <div class="flex  flex-b item ">
            <div class="t1">{{ $t("sharesDetails").txt1 }}</div>
            <div class="t2 red-txt">
              {{
                isNaN(Number(details.open).toFixed(2))
                  ? "0"
                  : $formatMoney(Number(details.open).toFixed(2))
              }}
            </div>
          </div>

          <div class="flex flex-b item">
            <div class="t1">{{ $t("sharesDetails").txt2 }}</div>
            <div class="t2 green-txt">
              {{
                isNaN(Number(details.prev_close).toFixed(2))
                  ? "0"
                  : $formatMoney(Number(details.prev_close).toFixed(2))
              }}
            </div>
          </div>

          <div class="flex flex-b item">
            <div class="t1">{{ $t("sharesDetails").txt3 }}</div>
            <div class="t2">
              {{ $formatMoney(Number(details.high).toFixed(2)) }}
            </div>
          </div>

          <div class="flex flex-b item">
            <div class="t1">{{ $t("sharesDetails").txt4 }}</div>
            <div class="t2">
              {{ $formatMoney(Number(details.low).toFixed(2)) }}
            </div>
          </div>

          <div class="flex flex-b item">
            <div class="t1">{{ $t("sharesDetails").txt5 }}</div>
            <div class="t2">
              {{
                isNaN(parseFloat(details.prev_volume / 10000).toFixed(0))
                  ? "-"
                  : $formatMoney(
                      parseFloat(details.prev_volume / 10000).toFixed(0)
                    )
              }}{{ $t("sharesDetails").txt25 }}
            </div>
          </div>

          <div class="flex flex-b item">
            <div class="t1">{{ $t("newt").t33 }}</div>
            <div class="t2">
              {{
                $formatMoney(
                  (Number(details.prev_volume_valued) / 10000).toFixed(0)
                )
              }}{{ $t("sharesDetails").txt25 }}
            </div>
          </div>
        </div>
      </div>

      <!-- 切换显示 -->
      <!-- <div class="nav-box flex flex-b">
        <div
          class="nav-item"
          v-for="(item, index) in navList"
          :key="index"
          :class="{ active: currmentIndex == item.type }"
          @click="changeNav(item.type)"
        >
          {{ item.name }}
        </div>
      </div> -->

      <template v-if="currmentIndex == 0">
        <!-- 显示指数k线详情和首页是同一个组件，只有分时-->
        <k-line :currentItem="details" :currentId="details.stock_id"></k-line>

        <div class="buy animate__animated animate__fadeIn">
          <div class="tab flex flex-b">
            <div
              class="tab-item"
              :class="{ active: isLimit }"
              @click="setLimit(true)"
            >
              {{ $t("sharesDetails").txt7 }}
            </div>
            <div
              class="tab-item"
              :class="{ active: !isLimit }"
              @click="setLimit(false)"
            >
              {{ $t("sharesDetails").txt8 }}
            </div>
          </div>

          <!-- 限价 -->
          <div class="input-box" v-if="isLimit">
            <div class="ipt flex flex-b">
              <div class="flex-1">
                {{ $t("sharesDetails").txt11 }}
              </div>
              <div class="flex-1">
                <van-stepper v-model="buyPrice" />
              </div>
            </div>
            <div class="ipt flex flex-b">
              <div class="flex-1">
                {{ $t("sharesDetails").txt12 }}
              </div>
              <div class="flex-1">
                <van-stepper v-model="buyAmount" />
              </div>
            </div>

            <!-- <div class="ipt flex flex-b">
          <div class="flex-1">
            {{ $t("sharesDetails").txt13 }}
          </div>
          <div class="flex-2">
            <van-stepper v-model="buyGang" />
          </div>
        </div> -->
          </div>

          <!-- 市价 -->
          <div class="input-box" v-if="!isLimit">
            <!-- <div class="ipt flex flex-b">
          <div class="flex-1">
            {{ $t("sharesDetails").txt11 }}
          </div>
          <div class="flex-2">
            <van-stepper v-model="buyPrice" />
          </div>
        </div> -->
            <div class="ipt flex flex-b">
              <div class="flex-1">
                {{ $t("sharesDetails").txt12 }}
              </div>
              <div class="flex-1">
                <van-stepper v-model="buyAmount" />
              </div>
            </div>

            <!-- <div class="ipt flex flex-b">
          <div class="flex-1">
            {{ $t("sharesDetails").txt13 }}
          </div>
          <div class="flex-2">
            <van-stepper v-model="buyGang" />
          </div>
        </div> -->
          </div>

          <!-- 买多、空 -->
          <div class="btns flex flex-b">
            <div
              class="bt icon buy1"
              :class="{ active: buyType == 1 }"
              @click="changeBuyType(1)"
            >
              {{ $t("sharesDetails").txt9 }}
            </div>
            <div
              class="bt bt1 icon buy2"
              :class="{ active: buyType == 2 }"
              @click="changeBuyType(2)"
            >
              {{ $t("sharesDetails").txt10 }}
            </div>
          </div>

          <div class="b-btn" @click="show = true">
            {{ $t("sharesDetails").btn }}
          </div>
        </div>
      </template>
    </van-pull-refresh>

    <van-popup
      v-model="show"
      position="center"
      :round="true"
      closeable
      :style="{ width: '90%' }"
    >
      <div class="popup">
        <div class="title">
          <div class="name">{{ $t(details.ko_name) }}</div>

          <div class="code">{{ $t(details.code) }}</div>
        </div>

        <div class="item flex flex-b">
          <div class="t">{{ $t("new").a59 }}</div>
          <div class="t1">
            {{ isLimit ? $formatMoney(buyPrice) : $t("sharesDetails").txt17 }}
          </div>
        </div>

        <div class="item flex flex-b">
          <div class="t">{{ $t("new").a60 }}</div>
          <div class="t1">{{ $formatMoney(buyGu) }}</div>
        </div>

        <div class="item flex flex-b">
          <div class="t">{{ $t("new").a61 }}</div>
          <div class="t1">{{ $formatMoney(buySz) }}</div>
        </div>

        <div class="item flex flex-b">
          <div class="t">{{ $t("new").a62 }}</div>
          <div class="t1">{{ $formatMoney(buyFuwu) }}</div>
        </div>

        <div class="item flex flex-b">
          <div class="t">{{ $t("new").a63 }}</div>
          <div class="t1">{{ $formatMoney(buyTotal) }}</div>
        </div>

        <div class="item flex flex-b">
          <div class="t">{{ $t("new").a64 }}</div>
          <div class="t1">{{ $formatMoney(userInfo.krw) }}</div>
        </div>

        <div class="item" @click="buyStock">
          <div class="b-btn ">{{ $t("sharesDetails").btn }}</div>
        </div>
      </div>
    </van-popup>
    <loading ref="loading" />
  </div>
</template>

<script>
import kLine from "../components/kLine";

export default {
  name: "",
  props: {},
  data() {
    return {
      currmentIndex: 0,
      isLoading: false,
      userInfo: {},
      show: false,
      buyPrice: "",
      buyAmount: "",
      buyGang: "",
      buyType: 1,
      isLimit: false,
      details: {},
      detailTime: null,
      symbol: "",
      is_zixuan: false,
      cfg: {},
      navList: [
        {
          name: this.$t("menu").href2,
          type: 0,
        },
        {
          name: this.$t("newt").t42,
          type: 1,
        },
        {
          name: this.$t("newt").t43,
          type: 2,
        },
      ],
    };
  },
  components: { kLine },
  created() {
    this.symbol = this.$route.query.symbol;
    this.details = this.$storage.get("stockDetail"); //这里没有详情接口，用的是列表数据
    // this.requestDetail(true);
    this.getConfig();
    this.getUserInfo();
    // this.detailTime = setInterval(() => {
    //   this.requestDetail();
    // }, 5000);
  },
  beforeDestroy() {
    // !!this.detailTime && clearInterval(this.detailTime);
  },
  computed: {
    // 股数
    buyGu() {
      if (this.cfg.buytype == 1) {
        // 买入类型(1手)
        return parseInt(this.buyAmount) * 100;
      }
      return 0;
    },
    // 市值
    buySz() {
      let scale = this.cfg.gtype == 1 ? this.buyGang : 1;
      return this.buyBj * scale;
    },
    // 本金
    buyBj() {
      //按手
      if (this.cfg.buytype == 1) {
        return this.buyGu * this.buyPrice;
      }
      //按万
      return Number(this.buyAmount) * this.buyPrice;
    },
    // 服务费
    buyFuwu() {
      let val = this.buySz * Number(this.cfg.buycharge);

      if (val < this.cfg.minshouxu) {
        return Number(this.cfg.minshouxu);
      }
      return val;
    },
    // 合计
    buyTotal() {
      return this.buyBj + this.buyFuwu;
    },
  },
  methods: {
    changeNav(type) {
      this.currmentIndex = type;
    },
    // 下拉刷新
    onRefresh() {
      // this.requestDetail(true);
      this.getConfig();
      this.getUserInfo();
    },
    setLimit(type) {
      this.isLimit = type;
      if (type == 2) this.buyPrice = this.details.price; //初始价格
    },
    changeBuyType(type) {
      this.buyType = type;
    },
    getUserInfo() {
      this.$server.post("/user/getUserinfo", {}).then((res) => {
        if (res.status == 1) {
          this.userInfo = res.data;
        }
      });
    },
    // 获取配置
    async getConfig() {
      const res = await this.$server.post("/common/config", { type: 'twd' });
      let val = {};
      res.data.forEach((vo) => {
        val[vo.name] = vo.value;
      });

      let arr = val.ganggang.split("/");
      this.buyGang = parseInt(arr[0]); //杠杆倍数给默认值

      this.buyPrice = this.details.close; //价格给默认值

      this.cfg = val;
    },
    requestDetail(isInit) {
      this.$server
        .post("/transaction/stockdetails", { symbol: this.symbol })
        .then((res) => {
          this.isLoading = false; //下拉刷新状态

          if (typeof res[0] == Object) {
            let obj = res[0];
            const arr = [];

            // Find the maximum property value
            const maxValue = Math.max(...Object.keys(obj));

            // Iterate from 0 to the maximum value
            for (let i = 0; i <= maxValue; i++) {
              arr.push(obj[i] || null);
            }
            res[0] = arr;
          }
          let vo = res[0] || {};
          if (res[0]) {
            // if (vo.symbol == "SETI") {
            //   vo.symbol = "SET";
            // }
            this.details = vo;
          } else {
            let stock = this.$storage.get("stockDetail");
            if (!stock) {
              return;
            }
            this.details = stock;
            vo = stock;
          }

          if (isInit) {
            this.buyPrice = this.details.price; //初始价格
            this.getMine();
          }
        });
    },
    buyStock() {
      this.$refs.loading.open(); //开启加载
      //普通购买
      this.$server
        .post("/transaction/buy_stock", {
          // symbol: this.details.symbol,
          symbol: this.symbol,
          zhang: this.buyAmount,
          ganggan: this.buyGang,
          type: this.isLimit ? 2 : 1,
          buyzd: this.buyType, //1 up ，2down
          buy_price: this.buyPrice,
          is_type: 0,
        })
        .then((res) => {
          this.$refs.loading.close(); //关闭加载

          if (res.status == 1) {
            this.show = false;
            this.$toast(this.$t(res.msg));
            setTimeout(() => {
              this.$toPage("/trade/index"); //跳转持仓
            }, 1000);
          }
        });
    },

    // 没有加自选，弃用!
    getMine() {
      this.$server.post("/transaction/Optionallist", { offset: 0 }).then((res) => {
        if (res.status == 1) {
          // 判断当前是否在自选列表里面
          let arr = res.data.filter(
            (item) => item.symbol == this.details.symbol
          );
          if (arr.length) this.is_zixuan = true;
        }
      });
    },
    addSelect(obj) {
      this.$refs.loading.open(); //加载

      if (!this.is_zixuan) {
        this.$server
          .post("/transaction/addOptional", { symbol: obj.symbol })
          .then((res) => {
            this.$refs.loading.close();

            if (res.status == 1) {
              this.is_zixuan = true;
            }
          });
      } else {
        this.$server
          .post("/transaction/removeOptional", { symbol: obj.symbol })
          .then((res) => {
            this.$refs.loading.close();

            if (res.status == 1) {
              this.is_zixuan = false;
            }
          });
      }
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .chart {
  height: 4.5rem;
}
::v-deep .van-stepper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 0.36rem;

  .van-stepper__input {
    flex: 1;
    background: transparent;
    height: 100%;
    margin: 0;
    border-top: 0.01rem solid #c0c0c0;
    border-bottom: 0.01rem solid #c0c0c0;
  }
}

::v-deep .van-stepper__minus,
::v-deep .van-stepper__plus {
  background: transparent;
  width: 0.36rem;
  height: 0.36rem;
  border: 0.01rem solid #c0c0c0;
}

.page {
  padding: 0.5rem 0rem 0.2rem;
}
.popup {
  padding: 0.2rem 0.1rem;
  .title {
    margin-bottom: 0.1rem;
    text-align: center;
    .name {
      font-weight: bold;
    }
    .code {
      font-size: 0.12rem;
      font-weight: 500;
      color: #797979;
    }
  }
  .item {
    padding: 0.1rem 0;
    .t {
      font-size: 0.12rem;
      color: #9b9ba3;
    }
    .t1 {
      font-weight: 500;
    }
  }
  .b-btn {
    margin: 0.1rem 0 0;
  }
}

.buy {
  .tab {
    padding: 0.1rem;
    .tab-item {
      padding: 0.1rem;
      background: #ededed;
      position: relative;
      font-size: 0.12rem;
      color: #424242;
      border-radius: 0.06rem;
      width: 46%;
      text-align: center;
      &.active {
        background: #6970af;
        color: #ffffff;
      }
    }
    border-bottom: 0.01rem solid #ededed;
  }

  .ipt {
    margin: 0.15rem 0;
  }

  .input-box {
    border-radius: 0.04rem;
    border: 0.01rem solid #c0c0c0;
    padding: 0 0.1rem;
    margin: 0 0.1rem;
  }

  .btns {
    padding: 0.1rem;
    .bt {
      width: 46%;
      font-size: 0.12rem;
      color: #ffffff;
      text-align: center;
      opacity: 0.2;
      border-radius: 0.04rem;
      background: #4f8672;
      padding: 0.1rem;
      &.bt1 {
        margin: 0 0 0 0.05rem;
        background: #c5585e;
      }
      &.active {
        opacity: 1;
      }
    }
  }

  .b-btn {
    margin: 0.1rem;
  }
}

.iszx {
  position: fixed;
  top: 0.12rem;
  right: 0.1rem;
  z-index: 999;
}

.top-data {
  padding: 0.1rem 0;
  border-bottom: 0.01rem solid #f5f5f5;

  .tp {
    padding: 0 0.1rem 0.1rem;
    border-bottom: 0.01rem solid #f5f5f5;
    .name {
      font-weight: 600;
      // font-size: 0.12rem;
    }
    .code {
      font-weight: 500;
      font-size: 0.12rem;
      color: #797979;
    }
    .price {
      font-weight: 600;
      font-size: 0.18rem;
    }
    .t {
      font-weight: 600;
      font-size: 0.12rem;
    }
  }

  .list {
    flex-wrap: wrap;
    padding: 0.1rem 0.1rem 0;
    .item {
      width: 48%;
      line-height: 0.22rem;

      .t1 {
        font-size: 0.12rem;
        color: #9b9ba3;
      }
      .t2 {
        font-size: 0.12rem;
      }
    }
  }

  .red {
    color: #c04649;
  }
  .green {
    color: #4f8672;
  }
}

.nav-box {
  padding: 0 0.1rem;
  // background: #ffffff;
  // box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);
  border-bottom: 0.01rem solid #f5f5f5;

  .nav-item {
    padding: 0.1rem 0;
    flex: 1;
    font-size: 0.12rem;
    color: #a1a1a1;
    text-align: center;
    position: relative;
    &::after {
      content: "";
      width: 50%;
      height: 0.02rem;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background: transparent;
    }
    &.active {
      color: #6970af;
      &::after {
        background: #6970af;
      }
    }
  }
}
</style>
