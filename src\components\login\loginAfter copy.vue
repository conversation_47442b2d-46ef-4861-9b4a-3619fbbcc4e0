<template>
	<div class="page flex flex-b">
		<div class="cot t-c">
			<img class="img animate__animated animate__fadeIn" :src="$cfg.logo" alt="" />
			<div class="txt">{{ $cfg.title }}</div>
		</div>
		<div class="progress">
			<van-progress :percentage="percentage" :show-pivot="false" color="#dba25c" track-color="#FFFFFF" stroke-width="6" />
			<div class="t">加載中... {{ percentage }}%</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "start",
		props: {},
		data() {
			return {
				percentage: 25,
			};
		},
		components: {},
		mounted() {
			this.setInt();
		},
		methods: {
			setInt() {
				setTimeout(() => {
					this.percentage = 50;
					setTimeout(() => {
						this.percentage = 75;
						setTimeout(() => {
							this.percentage = 100;
							this.startTime();
						}, 500);
					}, 500);
				}, 500);
			},

			startTime() {
				setTimeout(() => {
					this.$toPage("/home/<USER>");
				}, 500);
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		width: 100vw;
		min-height: 100vh;
		background: #17243A;
		padding: 1rem 0.2rem;
		flex-direction: column;

		.img {
			width: 1rem;
			height: 1rem;
			object-fit: contain;
		}

		.txt {
			font-weight: 600;
			font-size: 0.26rem;
			color: #ffffff;
			margin-top: 0.1rem;
		}

		.progress {
			width: 100%;

			.t {
				color: #ffffff;
				margin-top: 0.1rem;
				text-align: center;
			}
		}
	}
</style>