@charset "utf-8";
html, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img,input, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center, dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	vertical-align: baseline;
	background: transparent;
  font-family: Arial, sans-serif;
}

// @font-face {
  // font-family: 'PingFang-TC-Medium';
  // src: url('./PingFang-TC-Medium.otf');
// }

body {
    min-width: 100vw;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    // font-family: 'PingFang-TC-Medium'!important;
    color: #000000;
    background-color: #F3F2F2;
	font-size: 0.14rem;
  font-family: Arial, sans-serif;
}

* {
    margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
    box-sizing: border-box;
    //font-size: 0.14rem;
}

.van-dialog__header {
    color: #000;
}


// 这个会影响下拉刷新的组件的显示
.van-pull-refresh{
    overflow: unset!important;
}

.flex {
    display: flex;
    align-items: center;
	&.flex-a {
	  justify-content: space-around;
	}
    &.flex-b {
      justify-content: space-between;
    }
    &.flex-c {
      justify-content: center;
    }
    &.flex-e {
      justify-content: flex-end;
    }
}
.flex-column-item{
	display: flex;
	flex-direction: column;
	align-items: center;
}

.flex-wrap{flex-wrap: wrap;}

.flex-1 {
    flex: 1;
}
.flex-2 {
    flex: 2;
}
.flex-3 {
    flex: 3;
}
.t-center,.t-c , .text-center{
    text-align: center;
}
.t-right,.t-r, .text-right {
    text-align: right;
}
.no-data {
    text-align: center;
    margin: 1rem 0;
}

.top-pad {
    padding-top: 0.6rem;
}

.red {
	color: #549D7E !important;
}

.green {
	color: #CB0000 !important;
}

.b-btn {
    border-radius: 0.08rem;
    background: #4E9BE7;
    text-align: center;
    font-size: 0.14rem;font-weight: 500;
    color: #ffffff;
    padding: 0.12rem 0;
    margin-top: 0.4rem;
}



.ellipsis{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.icon {
    &.back {
      width: 0.2rem;
      height: 0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
	&.my{
		width: 0.22rem;
		height: 0.22rem;
		background: url("../home/<USER>") no-repeat center/100%;
	}
    &.ty {
      width: 0.2rem;
      height: 0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.wty {
      width: 0.2rem;
      height: 0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.jzz{
      width: 0.56rem;
      height:0.56rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: 0 auto;
    }
    &.logon{
      width: 1.87rem;
      height:1.71rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: 0 auto;
    }
    &.a {
      width: 0.21rem;
      height: 0.21rem;
      background: url("../menu/menu1.png") no-repeat center/100%;
    }
    &.ac {
      width: 0.21rem;
      height: 0.21rem;
      background: url("../menu/menu1A.png") no-repeat center/100%;
    }
    &.a1 {
      width: 0.21rem;
      height: 0.21rem;
      background: url("../menu/menu2.png") no-repeat center/100%;
    }
    &.ac1 {
      width: 0.21rem;
      height: 0.21rem;
      background: url("../menu/menu2A.png") no-repeat center/100%;
    }
    &.a2 {
      width: 0.8rem;
      height: 0.8rem;
      background: url("../menu/menu3.png") no-repeat center/100%;
    }
    &.ac2 {
      width: 0.8rem;
      height: 0.8rem;
      background: url("../menu/menu3.png") no-repeat center/100%;
    }
    &.a3 {
      width: 0.2rem;
      height: 0.21rem;
      background: url("../menu/menu4.png") no-repeat center/100%;
    }
    &.ac3 {
      width: 0.2rem;
      height: 0.21rem;
      background: url("../menu/menu4A.png") no-repeat center/100%;
    }
    &.a4 {
      width: 0.22rem;
      height: 0.16rem;
      background: url("../menu/menu5.png") no-repeat center/100%;
    }
    &.ac4 {
      width: 0.22rem;
      height: 0.16rem;
      background: url("../menu/menu5A.png") no-repeat center/100%;
    }
    &.set {
      width: 0.22rem;
      height: 0.22rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.jt1 {
      width: 0.14rem;
      height: 0.14rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.m1 {
      width:0.66rem;
      height: 0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.m2 {
      width:0.66rem;
      height: 0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.m3 {
      width:0.66rem;
      height: 0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.m4 {
      width:0.66rem;
      height: 0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.pass{
      width:2.01rem;
      height: 1.06rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: .3rem auto;
    }
    &.msg{
      width:0.21rem;
      height: 0.21rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
	&.msg2{
	  width:0.24rem;
	  height: 0.24rem;
	  background: url("../home/<USER>") no-repeat center/100%;
	}
    &.sou{
      width:0.24rem;
      height: 0.24rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.cz{
      width:0.24rem;
      height: 0.24rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.tx{
      width:0.24rem;
      height: 0.24rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.sbg{
      width:100%;
      height:1.35rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.addj{
      width:0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.bjbtn{
      width:0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.zf{
      width:0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.df{
      width:0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }


    &.jz1{
      width: 2.74rem;
      height: 2.42rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: 0 auto;
    }
    &.dl{
      width: 1.76rem;
      height: 1.76rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: 0 auto;
    }
    &.zy{
      width: 0.18rem;
      height:0.18rem;
      background: url("../login/eye.png") no-repeat center/100%;

    }
    &.by{
      width: 0.17rem;
      height:0.17rem;
      background: url("../v2/eyeClose.png") no-repeat center/100%;
    }
	&.close{
	  width: 0.28rem;
	  height:0.28rem;
	  background: url("../v2/close.png") no-repeat center/100%;
	}
    &.dj {
      width: 0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.zc {
      width: 2.36rem;
      height: 1.68rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: 0 auto;

    }
    &.jz2{
      width: 2.86rem;
      height:2rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin: 0 auto;
    }

    &.user {
      width: 0.36rem;
      height: 0.36rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.tz {
      width: 0.2rem;
      height: 0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.jt2 {
      width: 0.22rem;
      height: 0.22rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.nocheck{
      width: 0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.checked{
      width: 0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.rzwc {
      width:3.35rem;
      height: 2.11rem;
      background: url("../mine/shiminImg1.png") no-repeat center/100%;
    }
    &.sf{
      width: 0.2rem;
      height:0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.bankbg{
      width: 100%;
      height:1.7rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.zjbg{
      width: 100%;
      height:1.24rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.ss{
      width: 0.22rem;
      height:0.22rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.ss1{
      width: 0.24rem;
      height:0.24rem;
      background: url("../v2/sou.png") no-repeat center/100%;
    }
    &.mk1{
      width: 0.66rem;
      height:0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.mk2{
      width: 0.66rem;
      height:0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.mk3{
      width: 0.66rem;
      height:0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.mk4{
      width: 0.66rem;
      height:0.66rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.gdp{
      width: 0.9rem;
      height:0.56rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.hg{
      width: 0.16rem;
      height:0.16rem;
      background: url("../home/<USER>") no-repeat center/100%;
      margin-right: 0.05rem;
    }
    &.mg{
      width: 0.16rem;
      height:0.16rem;
      background: url("../home/<USER>") no-repeat center/100%;

      margin-right: 0.05rem;
    }

    &.u1{
      width: 0.18rem;
      height:0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.d1{
      width: 0.18rem;
      height:0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.jzx{
      width: 0.24rem;
      height:0.24rem;
      background: url("../menu/menu2.png") no-repeat center/100%;
    }
    &.czx{
      width: 0.24rem;
      height:0.24rem;
      background: url("../menu/menu2A.png") no-repeat center/100%;
    }
    &.kset{
      width:0.22rem;
      height: 0.21rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }

    &.add{
      width: 0.12rem;
      height:0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.copy {
      width: 0.15rem;
      height: 0.17rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.down {
      width: 0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.up {
      width: 0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.wsc {
      width: 0.16rem;
      height: 0.16rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }

    &.jt {
		margin-left: 0.05rem;
      width: 0.06rem;
      height: 0.11rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.xl{
      width: 0.1rem;
      height: 0.1rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.up1{
      width: 0.1rem;
      height: 0.08rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.down1{
      width: 0.1rem;
      height: 0.08rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.sou2{
      width: 0.2rem;
      height: 0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.tm{
		margin-right: 0.05rem;
      width: 0.14rem;
      height: 0.14rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
	&.suo{
	margin-right: 0.05rem;
	  width: 0.14rem;
	  height: 0.14rem;
	  background: url("../home/<USER>") no-repeat center/100%;
	}
    &.s1{
      width: 0.1rem;
      height: 0.1rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.x1{
      width: 0.1rem;
      height: 0.1rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.wxz {
      width: 0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.xz {
      width: 0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.sczx {
      width: 0.2rem;
      height: 0.2rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.ysc {
      width: 0.16rem;
      height: 0.16rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }

    &.gd1{
      width: 1.5rem;
      height: 0.98rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.gd2{
      width: 1.5rem;
      height: 0.98rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.dd1{
      width: 0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.zz1{
      width: 0.12rem;
      height: 0.12rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.rn{
      width:1.2rem;
      height: 1.4rem;
      background: url("../market/img4.png") no-repeat center/100%;
      margin: .3rem auto;
    }
    &.rns{
      width:0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.sou2{
      width: 0.18rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.usd {
      width: 0.34rem;
      height: 0.34rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.qh {
      width: 0.39rem;
      height: 0.39rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.thb {
      width: 0.34rem;
      height: 0.34rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.del {
      width: 0.19rem;
      height: 0.19rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.back1 {
      width: 0.1rem;
      height: 0.18rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.sou1 {
      width: 0.16rem;
      height: 0.17rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.rz {
      width: 0.12rem;
      height: 0.14rem;
      background: url("../home/<USER>") no-repeat center/100%;
    }
    &.logo {
      width:1.35rem;
      height: 0.35rem;
      // background: url("../home/<USER>") no-repeat center/100%;
      img{
        width: 100%;
        height: 100%;
      }
    }

    &.success {
      width: 0.97rem;
      height: 0.97rem;
      background: url("../login/sc.png") no-repeat center/100%;
    }
	&.zh {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/zh.png") no-repeat center/100%;
	}
	&.mm {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/mm.png") no-repeat center/100%;
	}
	&.yqm {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/yqm.png") no-repeat center/100%;
	}
	&.mine {
	  width: 0.36rem;
	  height: 0.36rem;
	  background: url("../v2/mine.png") no-repeat center/100%;
	}
	&.yhk {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/yhk.png") no-repeat center/100%;
	}
	&.jg {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/jg.png") no-repeat center/100%;
	}
	&.sfz {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/sfz.png") no-repeat center/100%;
	}
	&.ys {
	  width: 0.2rem;
	  height: 0.2rem;
	  background: url("../v2/ys.png") no-repeat center/100%;
	}
}
