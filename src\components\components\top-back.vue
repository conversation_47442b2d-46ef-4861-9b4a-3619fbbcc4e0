<template>
	<div class="">
		<div class="header ">
			<div class="flex flex-b reg" v-show="isReg">
				<div class="icon rjt animate__animated animate__fadeIn" @click="goBack"></div>
				<div class="t flex-1">{{ title }}</div>
				<div></div>
			</div>
			<div class="flex flex-b otg" v-show="!isReg">
				<div class="icon rjt animate__animated animate__fadeIn" :class="{ jt1: isList }" @click="goBack"></div>
				<div class="flex-1 t" :class="{ white: isList }">
					{{ title }}
				</div>
				<div></div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "topBack",
		props: {
			title: {
				type: String,
				default: "",
			},
			isReg: {
				default: "",
			},
			isList: {
				default: false,
			},
		},
		data() {
			return {};
		},
		components: {},
		created() {},
		computed: {},
		methods: {
			goBack() {
				this.$router.go(-1);
			},
		},
	};
</script>

<style scoped lang="less">
	.header {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0.15rem;
		background: #fff;
		.t {
			padding-left: 0.1rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.17rem;
			color: #000;
		}
		.white {
			color: #000;
		}
	}
</style>