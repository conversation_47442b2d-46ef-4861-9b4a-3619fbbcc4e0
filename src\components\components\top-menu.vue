<template>
  <div class="top-header">
    <div class="flex flex-b">
      <!-- 股票列表显示返回 -->
      <div
        class="icon back animate__animated animate__fadeIn"
        v-if="isList"
        @click="$toPage('/home/<USER>')"
      ></div>
      <div
        v-else
        class="icon zk animate__animated animate__fadeIn"
        @click="$toPage('/information/index')"
      ></div>

      <div
        v-if="!title"
        class="search flex flex-1"
        @click="$toPage('/favorite/search')"
      >
        <div class="icon sou animate__animated animate__fadeIn"></div>
        <div>{{ $t("search").txt2 }}</div>
      </div>

      <div class="tt" v-else>{{ title }}</div>

      <div
        class="icon tz animate__animated animate__fadeIn"
        @click="$toPage('/information/userInfo')"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "topMenu",
  props: {
    title: {
      type: String,
      default: "",
    },
    isList: {
      default: false,
    },
  },
  data() {
    return {};
  },
  components: {},
  methods: {},
  created() {},
  computed: {},
};
</script>

<style scoped lang="less">
.top-header {
  padding: 0.1rem 0.1rem;
  width: 100%;
  height: 0.5rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #fff;
  .tt {
    font-weight: 500;
    font-size: 0.14rem;
    color: #000000;
  }
  .search {
    border-radius: 0.3rem;
    background: #f3f3f3;
    height: 0.3rem;
    padding: 0 0.1rem;
    margin: 0 0.1rem;
    div {
      font-size: 0.12rem;
      color: #8f8f8f;
    }
  }
}
</style>
