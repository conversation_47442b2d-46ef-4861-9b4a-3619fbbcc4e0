<template>
	<div class="page ">
		<!-- <top-menu :title="$t('home').tab1"></top-menu> -->
		<top-back title="熱門資訊"></top-back>
		<!-- <div class="flex flex-b top-fixed">
			<div class="icon m7 animate__animated animate__fadeIn" @click="getConfig()"></div>
			<div>熱門資訊</div>
			<div class="icon tz animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')"></div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<!-- <van-list v-model="loading" :finished="finished" :finished-text="$t('new').a51" :loading-text="$t('new').a"
				@load="onLoad"> -->
				<no-data v-if="!articleList.length"></no-data>
				<div class="news-list" v-if="articleList.length">
					<div class="news-item" v-for="(item, index) in articleList" :key="index"
						@click="toNewsDetail(item)">
						<div class="flex flex-b">
							<img v-if="item.img" :src="item.img" alt="" />

							<div class="flex-1">
								<div class="t">{{ item.title }}</div>
								<div class="time">
									{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created*1000) }}
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- </van-list> -->
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newsList",
		props: {},
		data() {
			return {
				isLoading: false,
				loading: false,
				finished: false,
				articleList: [],
				page: 0,
			};
		},
		components: {},
		created() {},
		mounted() {
			this.getNews();
		},
		computed: {},
		methods: {
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "twd",
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			// 下拉刷新
			onRefresh() {
				this.page = 1;
				this.getNews();
			},
			onLoad() {
				// this.page += 1;
				// this.getNews();
			},
			getNews() {
				this.$refs.loading.open();

				this.$server
					.post("/common/newss", {
						exchange: "tw",
						lang: "cn",
					})
					.then((res) => {
						this.$refs.loading.close();
						this.isLoading = false; //下拉刷新状态
						this.loading = false;
						let arr = res.data.result;

						// this.articleList = [...this.articleList, ...arr];
						this.articleList = arr;
						if (arr.length == 0) {
							this.finished = true; //结束列表加载
						}
					});
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
		},
	};
</script>

<style scoped lang="less">
	.top-fixed {
		padding: 0.15rem;
		width: 100%;
		height: 0.5rem;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		background-color: #fff;
	}

	.page {
		min-height: 100vh;
		padding: 0.5rem 0.15rem 0.6rem;
	}

	.news-list {
		.news-item {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #ededed;

			&:last-child {
				border-bottom: 0;
			}

			.t {
				// font-weight: 600;
				color: #000000;
			}

			.time {
				font-size: 0.12rem;
				color: #6f6f6f;
				margin-top: 0.1rem;
			}

			img {
				width: 0.8rem;
				height: 0.5rem;
				border-radius: 0.04rem;
				margin-right: 0.1rem;
			}
		}
	}
</style>