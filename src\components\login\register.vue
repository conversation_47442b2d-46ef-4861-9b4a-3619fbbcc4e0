<template>
	<div class="page flex flex-b">
		<!-- <top-back :title="$t('new').b19" :isReg="true"></top-back> -->
		<div class="form">
			<div class="title">
				<!-- <img class="img animate__animated animate__fadeIn" :src="$cfg.logo" alt="" /> -->
				<div class="txt">{{ $cfg.title }}</div>
				<div class="t">授權註冊</div>
			</div>
			<div class="cot">
				<!-- <div class="t">帳戶</div> -->
				<div class="inner flex flex-b">
					<div class="icon zh"></div>
					<input type="text" placeholder="請輸入帳號" maxlength="11" v-model="form.account" />
				</div>
				<!-- <div class="t">密碼</div> -->
				<div class="inner flex flex-b">
					<div class="icon mm"></div>
					<input v-if="show" type="text" placeholder="請輸入密碼" v-model="form.pwd" />
					<input v-else type="password" placeholder="請輸入密碼" v-model="form.pwd" />
					<div class="icon" :class="show ? 'by' : 'zy1'" @click="show = !show"></div>
				</div>
				<!-- <div class="t">重複密碼</div> -->
				<div class="inner flex flex-b">
					<div class="icon mm"></div>
					<input v-if="!show1" type="password" placeholder="請再次輸入密碼" v-model="form.pwd1" />
					<input v-else type="text" placeholder="請再次輸入密碼" v-model="form.pwd1" />
					<div class="icon" :class="show1 ? 'by' : 'zy1'" @click="show1 = !show1"></div>
				</div>
				<!-- <div class="t">邀請碼</div> -->
				<!-- <div class="inner flex flex-b">
					<div class="icon yqm"></div>
					<input type="text" placeholder="請輸入擔保方邀請碼" v-model="form.inviter" />
				</div> -->
				<div class="flex tips">
					<van-checkbox checked-color="#078bde" v-model="checked" shape="square" icon-size="14px" @change="save">本人已清楚了解同意
						<span @click="$toPage('/information/userPrivacy')">{{ $cfg.title }} 隱私政策</span>
					</van-checkbox>
				</div>
				<div class="b-btn animate__animated animate__fadeIn" @click="register">立即授權註冊</div>
				<div class="b-btn02 animate__animated animate__fadeIn" @click="$toPage('/login/login')">已授權，立即登入</div>
			</div>
			<loading ref="loading" />
		</div>
	</div>
</template>
<script>
	export default {
		name: "login",
		data() {
			return {
				checked: false,
				show: false,
				show1: false,
				cfg: {},
				form: {
					account: "",
					inviter: "",
					pwd: "",
					pwd1: "",
				},
				isRead: "",
			};
		},
		components: {},
		mounted() {
			console.log()
			let params = window.location.href
			let code = params.split('=')[1]
			if(code){
				this.form.inviter = code
			}
			// this.getConfig();
		},
		methods: {
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "twd"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/login/logo.png");
				}
				this.cfg = val;
			},
			save(e) {
				// console.log("e", e);
				this.isRead = e;
			},
			isValidString(str) {
      const regex = /^[A-Za-z]\d{9}$/; // 正则表达式：以字母开头，后面跟9位数字
      return regex.test(str);
    },
      containsChinese(str) {
        const regex = /[\u4e00-\u9fff]/;
        return regex.test(str);
      },
			async register() {
				
				if (!this.form.account) {
					this.$toast("請輸入帳號");
					return;
				}
        if(this.containsChinese(this.form.account)){
          this.$toast("帳號必須不含中文");
          return;
        }
				// if (!this.isValidString(this.form.account)) {
				// 	this.$toast("請輸入正確的身分證");
				// 	return;
				// }
				if (!this.form.pwd) {
					this.$toast("請輸入密碼");
					return;
				}
				if (!this.form.pwd1) {
					this.$toast("請再次輸入密碼");
					return;
				}
				if (this.form.pwd1 !== this.form.pwd) {
					this.$toast("兩次輸入密碼不一致");
					return;
				}
				// if (!this.form.inviter) {
				// 	this.$toast("請輸入邀請碼");
				// 	return;
				// }

				if (!this.isRead) {
					this.$toast("請閱讀並勾選隱私協議");
					return;
				}

				this.$refs.loading.open(); //开启加载
				const res = await this.$server.post("/user/register", {
					...this.form,
					type: "twd",
				});
				this.$refs.loading.close();
				if (res.status == 1) {
					this.$toast(this.$t(res.msg));

					localStorage.setItem("AC", this.form.account);
					localStorage.setItem("PS", this.form.pwd);

					setTimeout(() => {
						// this.$toPage("/login/reLogin");
						this.$toPage("/login/login");
					}, 1000);
				}
			},
		},
	};
</script>
<style lang="less" scoped="scoped">
	::v-deep .van-checkbox__label {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.12rem;
		color: #999999 !important;
	}

	.page {
		width: 100vw;
		min-height: 100vh;
		padding: 1rem 0 0;
		background: url("../../assets/123.png");
		background-size: contain;
		background-position: center top;
		background-repeat: no-repeat;
		background-color: #fff;
		flex-direction: column;
		width: 100vw;
		min-height: 100vh;
		background: url('../../assets/v1/newstart.png');
		background-size: 100%;
		.logo {
			width: 1rem;
			height: 1rem;
			margin: 0 auto 1rem;
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			.txt {
				font-weight: 600;
				font-size: 0.3rem;
				color: #333;
				margin-top: 0.1rem;
			}
		}

		.form {
			width: 100%;
			padding: 0 0.2rem;
			.title {
				.img {
					width: 0.5rem;
					height: 0.5rem;
				}
			
				.txt {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.35rem;
					color: #fff;
				}
			
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #fff;
					margin-bottom: 0.1rem;
				}
			}

			.cot {
				margin: 0.4rem 0 0;
				background: #FFFFFF;
				border-radius: 0.14rem;
				padding: 0.15rem;
				.t {
					color: #222222;
					margin-right: 0.05rem;
					font-size: 0.16rem;
				}
			}

			.inner {
				margin-bottom: 0.16rem;
				padding: 0.14rem 0;
				padding-right: .1rem;
				border-bottom: 0.01rem solid #D3D3D3;
				input {
					background-color: transparent;
					flex: 1;
					color: #333;
					padding: 0 0.1rem;
					font-size: 0.13rem;
			
					&::placeholder {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}
				}
			}
			.tips {
				margin: 0.3rem 0 0.2rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #078bde;
				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #078bde;
				}
			}
		}
	}
	::v-deep .van-checkbox__label{
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.12rem;
		color: #A2B4D3;
	}
	::v-deep .van-checkbox__icon--checked{
		.van-icon{
			background-color: transparent;
			border-color: #999;
		}
	}
</style>