<template>
	<div class="page ">
		<top-back title="登入密碼"></top-back>

		<div class="cot">
			<!-- <div class="tt">設置登入密碼</div> -->
			<div class="bg">
				<div class="item">
					<!-- <div class="t">舊密碼</div> -->
					<div class="flex flex-b ipt">
						<input class="flex-1" v-model="form.old_pass" :type="show ? 'text' : 'password'"
							placeholder="請輸入舊密碼" />
						<div class="icon animate__animated animate__fadeIn" :class="show ? 'mby' : 'mzy'"
							@click="show = !show"></div>
					</div>
				</div>
				<div class="item">
					<!-- <div class="t">新密碼</div> -->
					<div class="flex flex-b ipt ">
						<input v-model="form.new_pass" :type="show1 ? 'text' : 'password'" placeholder="請輸入新密碼" />

						<div class="icon animate__animated animate__fadeIn" :class="show1 ? 'mby' : 'mzy'"
							@click="show1 = !show1"></div>
					</div>
				</div>
				<div class="item">
					<!-- <div class="t">新密碼</div> -->
					<div class="flex flex-b ipt last">
						<input v-model="form.new_pass2" :type="show2 ? 'text' : 'password'" placeholder="請再次輸入新密碼" />
						<div class="icon animate__animated animate__fadeIn" :class="show2 ? 'mby' : 'mzy'"
							@click="show2 = !show2"></div>
					</div>
				</div>
				<div class="b-btn animate__animated animate__fadeIn" @click="editPassword">確認</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "loginPass",
		props: {},
		data() {
			return {
				show: false,
				show1: false,
				show2: false,
				form: {
					old_pass: "",
					new_pass: "",
					new_pass2: "",
				},
			};
		},
		components: {},
		methods: {
			editPassword() {
				let that = this;
				if (!this.form.old_pass) {
					this.$toast("請輸入舊密碼");

					return false;
				}
				if (!this.form.new_pass && this.form.new_pass.length < 6) {
					this.$toast("請輸入新密碼");

					return false;
				}
				if (this.form.new_pass2 !== this.form.new_pass) {
					this.$toast("請再次輸入新密碼");
					return false;
				}
				this.$refs.loading.open(); //开启加载
				this.$server
					.post("/user/changePassword", {
						type: "twd",
						old_pass: this.form.old_pass,
						new_pass: this.form.new_pass,
						new_passs: this.form.new_pass2,
					})
					.then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							setTimeout(() => {
								this.loginOut();
							}, 1500);
						}
					});
			},
			// 退出
			loginOut() {
				localStorage.removeItem("tokend");
				localStorage.removeItem("account");

				setTimeout(() => {
					this.$toPage("/login/login");
				}, 1500);
			},
		},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.7rem 0.15rem 0;
		min-height: 100vh;
		background: #f7f7f7;

		.cot {
			.bg {
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
				border-radius: 0.07rem;
				padding: 0.1rem 0.15rem;
			}
			.tt {
				font-size: 0.16rem;
				margin-bottom: 0.15rem;
			}
			.item {
				// margin-bottom: 0.2rem;
				.t {
					font-weight: 600;
					color: #0e1028;
					margin-bottom: 0.1rem;
				}
				.ipt {
					border-bottom: 0.01rem solid #cecece;
					height: 0.42rem;
					margin-bottom: 0.2rem;
					&.last {
						margin-bottom: 0;
					}
				}

				input {
					margin-right: 0.1rem;
					&::placeholder {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #182133;
					}
				}
			}
		}
	}
</style>