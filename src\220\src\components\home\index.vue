<template>
	<div class="page homeIndex">
		<div class="top">
			<div class="header flex flex-b">
				<div class="icon mine" @click="$toPage('/information/index')"></div>
				<div class="search flex flex-1" @click="$toPage('/favorite/search')">
					<div class="icon ss"></div>
					<div>{{$t('search').txt2}}</div>
				</div>
				<!-- <div>{{$t('menu').href1}}</div> -->
				<div class="icon msg" @click="$toPage('/information/userInfo')"></div>
				<div class="icon kset" style="margin-left: 0.1rem;" @click="$toPage('/information/setting')"></div>
			</div>
      <div class="nav-box flex">
        <div class="nav-item" v-for="(item, index) in navList" :key="index"
             :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
          {{ item.name }}<span></span>
        </div>
      </div>
			<div class="banner">
				<van-swipe :touchable="true" :stop-propagation="false">
					<van-swipe-item>
						<!-- <img src="../../assets/home/<USER>" /> -->
						<div class="tab flex flex-b text-center">
							<div class="item" v-for="(item,idx) in tabList" :key="idx" @click="$toDetail(item.url) "
								v-if="idx<4">
								<div>
									<img :src="item.icon" />
								</div>
								{{item.name}}
							</div>
						</div>
					</van-swipe-item>
					<van-swipe-item>
						<!-- <img src="../../assets/home/<USER>" /> -->
						<div class="tab flex flex-b text-center">
							<div class="item" v-for="(item,idx) in tabList" :key="idx" @click="$toDetail(item.url) "
								v-if="idx>3">
								<div>
									<img :src="item.icon" />
								</div>
								{{item.name}}
							</div>
						</div>
					</van-swipe-item>
				</van-swipe>
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="5" :loading="loading">

				<div class="topStock">
					<div class="itemBox flex">
						<div class="item" :class="{'red':Number(item.gain)>=0,'green':Number(item.gain)<0}"
							v-for="(item,idx) in indexList" :key="idx"
							@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item)" v-if="idx<3">
							<div class="name">{{item.symbol}}</div>
							<div class="price">{{$formatMoney(item.price)}}</div>
							<div class="per flex flex-b" :class="item.gain>0?'red':'green'">
								<div>{{$formatMoney(item.gainValue)}}</div>
								<div> {{stockType=='try'?'%':''}}{{$formatMoney(item.gain)}}{{stockType=='try'?'':'%'}}</div>
							</div>
							<img src="../../assets/v2/redLine.png" style="width: 0.98rem;height: 0.36rem;" alt=""
								v-if="item.gain>0" />
							<img src="../../assets/v2/greenLine.png" style="width: 0.98rem;height: 0.36rem;" alt=""
								v-else />
						</div>
					</div>
				</div>
				<!-- 股票列表 -->
				<div class="stockList">
					<div class="title flex flex-b">
						<div class="t">
							{{ $t('热门股票') }}
						</div>
						<div class="flex t1" @click="$toPage('/market/index')">
							{{ $t("更多") }}
							<div class="icon jt"></div>
						</div>
					</div>
					<div class="stockTitle flex flex-b">
						<div>{{$t('market').txt1}}</div>
						<div>{{$t('home').txt3}}/{{$t('涨跌幅')}}</div>
					</div>
					<div class="item flex flex-b" v-for="(item,idx) in topList" v-if="idx<6"
						@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item)">
						<div class="name flex">
							<div class="img"><img :src="item.logo" /></div>
							<div>
								<div>{{item.name}}</div>
								<span>{{item.symbol}}</span>
							</div>
						</div>
						<div class="price text-right">
							<div>{{$formatMoney(item.price,2)}}</div>
							<div class="flex">
								<div class="icon" :class="item.gain>=0?'up1':'down1'"></div>
								<span :class="item.gain>=0?'red':'green'">{{stockType=='try'?'%':''}}{{item.gain}}{{stockType=='try'?'':'%'}}</span>
							</div>
						</div>
					</div>
				</div>
				<!-- 指数列表 -->
				<div class="index">
					<div class="nums flex flex-b">
						<div class="nums-item" :class="{ center: i == 1 }" v-for="(item, i) in indexList" :key="i"
							@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item) ">
							<div class="flex">
								<div class="icon" :class="item.icon"></div>
								<div class="name"> {{ item.symbol }} </div>
							</div>
							<div class="flex flex-b">
								<div class="t"> {{ $formatMoney(item.price, 2) }} </div>
								<img src="../../assets/v2/greenLine.png" alt="" v-if="item.gain < 0" style="width: 0.57rem;height: 0.18rem;"/>
								<img src="../../assets/v2/redLine.png" alt="" v-else style="width: 0.57rem;height: 0.18rem;" />
							</div>

							<div class="t1 red flex flex-e" :class="{ 'green': item.gain < 0 }">
								<div class="icon animate__animated animate__fadeIn" :class="item.gain < 0 ? 'down1' : 'up1'" style="margin-right: 0.05rem;"></div>
								{{ item.gain <= 0 ? "" : "+" }}{{ $formatMoney(item.gainValue, 2) }}
                {{stockType=='try'?'%':''}}{{ item.gain <= 0 ? "" : "+" }}{{ item.gain }}{{stockType=='try'?'':'%'}}
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
			<van-skeleton title :row="20" :loading="loading1">
				<!-- 新闻列表 -->
				<div class="news">
					<div class="title flex flex-b">
						<div class="t">
							{{ $t('news').title }}
						</div>
						<div class="flex t1" @click="$toPage('/home/<USER>')">
							{{ $t("更多") }}
							<div class="icon jt"></div>
						</div>
					</div>

					<div class="news-list">
						<no-data v-if="!newsList.length"></no-data>
						<div class="news-item flex flex-b" v-for="(item, index) in newsList" :key="index"	v-show="index < 20" @click="clickNewsDetail(item)" v-if="index<3">
							<div class="img">
								<img :src="item.img" alt="" />
							</div>
							<div class="pad flex-1">
								<div class="tt">{{ item.title }}</div>
								<div class="time">
									{{ $formatDate( "DD-MM-YYYY hh:mm", new Date(item.created*1000) ) }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
		<tab-bar :current="0"></tab-bar>
	</div>
</template>

<script>
	import teShe from "./teShe.vue";
	import industryStatus from "./industryStatus.vue";

	export default {
		name: "home",
		props: {},
		data() {
			return {
        navList: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
				flagUp: true,
				flagUserPop: false,
				indexList: [],
				loading: true,
				loading1: true,
				kfUrl: "",
				isLoading: false,
				hotList: [],
				chooseList: [],
				newsList: [],
				tabList: [],
				totalAssets: 0,
				topList: []
			};
		},
		components: {
			teShe,
			industryStatus,
		},
		created() {},
		mounted() {
      this.menu()
			this.getTop();
			this.getNews();
			this.getIndexList();
		},
		methods: {
      menu(){
        this.tabList = [
          {
            name: this.$t("日内交易"),
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t("new").t10, //抢筹
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t('market').fast4, // 盘前
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t('market').fast5, // 盘前
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t('market').fast6, // 大宗
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t("新股申购"), // ipo
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t("market").fast7, // 跟单
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
          {
            name: this.$t("new").b6,
            icon: require("../../assets/home/<USER>"),
            url: "/home/<USER>" + this.stockType,
          },
        ]
      },
      changeStock(type){
        this.stockType = type
        this.menu()
        this.getTop();
        this.getNews();
        this.getIndexList();
      },
			// 下拉刷新
			onRefresh() {
        this.menu()
				this.getTop();
				this.getNews();
				this.getIndexList();
			},
			getTop() { //排行
				this.$server.post("/parameter/top", {
					type: this.stockType
				}).then((res) => {
					if (res && res.data) {
						this.topList = res.data;
						//this.clickSort();
					}
				});
			},
			async getIndexList() { // 指数

				const res = await this.$server.post("/parameter/zhishu", {
					type: this.stockType
				});

				let arr = [];
				if (res && res.data) {
					arr = res.data;
				}

				this.loading = false;
				this.isLoading = false;

				this.indexList = arr;
			},
			getNews() {
        let per = {}
        if(this.stockType == 'try'){
          per = {
            exchange: "tr",
            lang: "tr"
          }
        }
        if(this.stockType == 'usd'){
          per = {
            exchange: "us",
            lang: "en"
          }
        }
				this.$server.post("/common/newss", per).then((res) => {
					this.loading1 = false;
					if (res && res.data) {
						this.newsList = res.data.result;
					}
				});
			},
			clickNewsDetail(item) {
				window.localStorage.setItem('newsDetail', JSON.stringify(item));
				this.$toPage('/home/<USER>');
			},
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.kfUrl = val.kefu;
			},
		},
	};
</script>

<style scoped lang="less">
	::-webkit-scrollbar {
		display: none;
	}

	::v-deep .van-swipe__indicator {
		width: 0.08rem;
		height: 0.08rem;
		border-radius: 50%;
		background: #a5a5a5;
	}

	::v-deep .van-swipe__indicator--active {
		background: #E10414;
	}

  .nav-box {
    width: 100%;
    height: .4rem;
    top: .5rem;
    padding: 0.15rem;
    .nav-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.14rem;
      color: #111111;
      text-align: center;
      margin-right: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span{
        width: .5rem;
        height: .03rem;
        background: transparent;
        margin-top: .05rem;
        display: block;
      }
      &.active {
        font-weight: 600;
        font-size: 0.14rem;
        color: #E10414;
        position: relative;
        span{
          background: #E10414;
        }
      }
    }
  }

	.homeIndex {
		padding: .5rem 0 1rem;
		.top {
			position: relative;
			z-index: 888;

			.header {
				width: 100vw;
				height: .5rem;
				background: #F3F2F2;
				position: fixed;
				top: 0;
				left: 0;
				z-index: 999;
				padding: 0 0.12rem;
				font-weight: 500;
				font-size: .18rem;
				color: #000000;
			}

			.search {
				height: 0.36rem;
				background: #FFFFFF;
				border-radius: 0.1rem;
				border: 0.01rem solid #EAEAE9;
				padding: 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #9B98A5;
			}

			.banner {
				padding: .1rem 0;

				.van-swipe-item {
					height: 1.55rem;
				}

				img {
					width: 100%;
				}
			}

			.tab {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #0C061C;
				align-items: flex-start;
				flex-wrap: wrap;

				img {
					width: 0.69rem;
				}

				.item {
					width: 24%;
				}
			}
		}

		.myTotal {
			padding: 0 0.15rem;

			.title {
				font-weight: 500;
				font-size: .18rem;
			}

			.t {
				font-weight: 500;
				font-size: .12rem;
				margin: .04rem 0 .1rem 0;
			}

			.num {
				font-weight: 600;
				font-size: .24rem;
				color: #4D8BE5;
			}
		}

		.topStock {
			margin: 0 .12rem 0;
			padding: 0.12rem 0;
			//min-height: 2.09rem;
			background: #FFFFFF;
			border-radius: 0.13rem;

			.title {
				font-weight: 600;
				font-size: .14rem;
				margin-bottom: .1rem;

				img {
					width: .24rem;
				}
			}

			.itemBox {

				// overflow: scroll;
				.item {
					padding: 0 .12rem;
					width: 33%;
					font-weight: 500;
					font-size: .11rem;
					border-right: 0.01rem solid #E4EAF1;

					&:last-child {
						border-right: none;
					}
				}

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #666666;
				}

				.price {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
					font-weight: 600;
					font-size: 0.18rem;
					color: #0C061C;
					padding: .1rem 0;
				}

				.per {
					margin-bottom: 0.1rem;
					font-family: OPPOSans, OPPOSans;
					font-weight: normal;
					font-size: 0.13rem;
				}

				.info {
					color: #A2A7AA;
					font-weight: 400;

					span {
						color: #24272C;
						font-weight: 500;
					}
				}
			}
		}

		.stockList {
			margin: .16rem .12rem 0;
			background: #FFFFFF;
			border-radius: 0.13rem;
			padding: 0.12rem;

			.title {
				padding-bottom: 0.1rem;
				border-bottom: 0.01rem solid #E4EAF1;

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #0C061C;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #E10414;
				}
			}

			.stockTitle {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
				padding: .1rem 0;
			}

			.item {
				margin-bottom: .1rem;
				padding: .1rem 0;
				border-bottom: 0.01rem solid #E4EAF1;

				.img {
					margin-right: .1rem;

					img {
						width: 0.34rem;
						height: 0.34rem;
						border-radius: 50%;
					}
				}

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #0C061C;

					span {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #999999;
					}
				}

				.price {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
					font-weight: 600;
					font-size: 0.14rem;
					color: #0C061C;

					span {
						margin-top: 0.02rem;
						margin-left: 0.05rem;
						font-family: OPPOSans, OPPOSans;
						font-weight: normal;
						font-size: 0.12rem;
					}
				}
			}
		}

		.index {
			overflow-x: scroll;
			padding: 0 0.15rem;

			.nums {
				padding: 0.1rem 0;
				width: 580%;
				.nums-item {
					width: 50%;
					margin-right: 0.1rem;
					padding: 0.15rem 0.1rem;
					background: #FFFFFF;
					border-radius: 0.13rem;

					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.15rem;
						color: #0C061C;
					}

					.t {
						font-family: OPPOSans, OPPOSans;
						font-weight: normal;
						font-size: 0.18rem;
						color: #0C061C;
						margin: 0.1rem 0;
					}

					.t1 {
						font-family: OPPOSans, OPPOSans;
						font-weight: normal;
						font-size: 0.15rem;
					}

					.t2 {
						margin-top: 0.1rem;

						.c1 {
							width: 0.06rem;
							height: 0.06rem;
							border-radius: 50%;
							background: #8e8e8e;
							margin-right: 0.05rem;
						}

						font-family: PingFang TC,
						PingFang TC;
						font-weight: 500;
						font-size: 0.12rem;
						color: #8e8e8e;
					}
				}
			}
		}

		.large {
			.title {
				border-bottom: .01rem solid #EEEEEE;
				height: .48rem;
				padding: 0 .16rem;
				font-weight: 600;
				font-size: 0.16rem;

			}

			.itemBox {
				height: 2.09rem;
				background: #2B2E3E;
				border-radius: .08rem;
				margin: .1rem .16rem;
				position: relative;

				.item {
					padding: .24rem;
					color: #fff;

					.txt1 {
						font-weight: 600;
						font-size: .16rem;
					}

					.txt2 {
						font-weight: 500;
						font-size: .14rem;
						margin-top: .06rem;
					}

					.txt3 {
						font-weight: 500;
						font-size: .14rem;
						color: #B6E992;
						margin: .1rem 0 .44rem 0;
					}

					.txt4 {
						font-weight: 500;
						font-size: .14rem;
						color: #99C281;
						height: .36rem;
						border-radius: .04rem;
						border: .01rem solid #99C281;
						display: inline-flex;
						align-items: center;
						padding: 0 .3rem;
					}
				}

				.img {
					position: absolute;
					bottom: .8rem;
					right: 1rem;

					img {
						width: .36rem;
						position: absolute;
						top: 0;
						left: 0;
					}

					.img1 {
						top: .13rem;
					}

					.img2 {
						left: .22rem;
					}

					.img3 {
						top: .25rem;
						left: .5rem;
					}
				}
			}
		}

		.news {
			//padding: 0.15rem;
			.title {
				padding: 0.1rem .12rem;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.18rem;
					color: #0C061C;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #E10414;
				}
			}

			.news-list {
				margin: 0.1rem 0.12rem 0;
				background: #FFFFFF;
				border-radius: 0.13rem;
				.news-item {
					padding: .12rem;
					.img {
						flex: none;
						margin-right: .12rem;

						img {
							width: 1.15rem;
							height: 0.76rem;
							border-radius: .06rem;
							// object-fit:fill;
						}
					}

					.tt {
						font-weight: 600;
						font-size: 0.16rem;
						color: #0C061C;
						-webkit-line-clamp: 2;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						overflow: hidden;
					}

					.time {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
						margin-top: .1rem;
					}
				}
			}
		}
	}
</style>
