<template>
	<div class="page">
		<top-back title="競拍"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<div class="flex flex-b t-c top">
					<div class="flex-1" @click="$toPage('/home/<USER>')">
						<div class="icon zj"></div>
						<div class="">申購記錄</div>
					</div>
					<div class="flex-1" @click="$toPage('/home/<USER>')">
						<div class="icon zj"></div>
						<div class="">中籤記錄</div>
					</div>
				</div>

				<div class="tb flex flex-b">
					<div class="tb-item" v-for="(item, index) in navList" :key="index"
						:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
						{{ item.name }}
					</div>
				</div>

				<van-skeleton title :row="26" :loading="loading">
					<no-data v-if="!xinList.length"></no-data>
					<div class="item" v-for="(item, index) in xinList" :key="index" @click="toDetail(item)">
						<div class="item-top flex flex-b">
							<div class=" flex-1">
								<div class="name">{{ item.name || "-" }}</div>
								<div class="code">{{ item.symbol || "-" }}</div>
							</div>
							<div class="flex-1 flex flex-e">
								<div class="st-btn" :class="{ isEnd: item.isKsg == 2 }">
									{{ btnArr[item.isKsg] }}
								</div>
							</div>
						</div>

						<div class="item-middle">
							<div class="item-list">
								承銷價
								<span>{{ $formatMoney(item.bprice) }}</span>
							</div>
							<div class="item-list">
								市價
								<span>{{ $formatMoney(item.price) }}</span>
							</div>
							<div class="item-list">
								差價
								<span>
									{{ $formatMoney(item.price - item.bprice) || "0" }}</span>
							</div>
							<div class="item-list">
								總申購張
								<span> {{ $formatMoney(item.num, 0) || "-" }}</span>
							</div>
							<div class="item-list">
								截止日
								<span> {{ $formatDate("YYYY-MM-DD", item.end * 1000) }}</span>
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newStock",
		data() {
			return {
				loading: true,
				isLoading: false,
				btnArr: ["詳情", "立即申購", "申購結束"],
				xinList: [
					// {
					// 	name:'name',
					// 	symbol:'symbol',
					// 	isKsg: 0,
					// 	bprice:10,
					// 	price:10,
					// 	num:12,
					// 	end:11111111,
					// },
				],
				allList: [],
				navList: [{
						name: "待申購",
						type: 0,
					},
					{
						name: "申購中",
						type: 1,
					},
					{
						name: "申購結束",
						type: 2,
					},
				],
				currmentIndex: 1,
			};
		},
		computed: {},
		mounted() {
			this.getXingu();
		},
		methods: {
			onRefresh() {
				this.getXingu();
			},
			changeNav(type) {
				// this.$refs.loading.open();
				this.currmentIndex = type;
				this.xinList = this.allList.filter((item) => item.isKsg == type);
				this.xinList.reverse();
				// setTimeout(() => {
				//   this.$refs.loading.close();
				// }, 1000);
			},
			getXingu() {
				this.$server
					.post("/trade/placinglist", {
						type: "twd",
						buy_type: 1
					})
					.then((res) => {
						this.loading = false;
						this.isLoading = false;
						let now = new Date().getTime();
						let arr = [];
						res.data.forEach((item) => {
							// item.start = new Date().getTime() / 1000 + 50000;
							// item.end = new Date().getTime() / 1000 + 60000;

							// 可申购
							if (item.start * 1000 <= now && now <= item.end * 1000) {
								item.time = Math.floor(
									(item.end * 1000 - now) / 1000 / 60 / 60 / 24
								);
								item.isKsg = 1; //是否可申购
							} else if (now < item.start * 1000) {
								item.time = Math.floor(
									(item.start * 1000 - now) / 1000 / 60 / 60 / 24
								);
								// 待申购
								item.isKsg = 0;
							} else if (item.end * 1000 < now) {
								// 结束
								item.isKsg = 2;
							}
							arr.push(item);
						});
						this.allList = [...new Set(arr)];
						this.changeNav(1);
					});
			},
			toDetail(item) {
				this.$storage.save("itemTemp", item);
				this.$toPage(`/home/<USER>
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0 0.15rem;
		min-height: 100vh;
	}

	.tb {
		background: #FFFFFF;
		margin: 0.2rem 0 0;

		.tb-item {
			flex: 1;
			text-align: center;
			padding: 0.1rem 0;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #405476;

			&.active {
				font-weight: 600;
				color: #DCA25C;
				position: relative;
				&::after{
					position: absolute;
					content: '';
					bottom: 0;
					left: 0;
					width: 100%;
					height: 0.02rem;
					background-color: #DCA25C;
				}
			}
		}
	}

	.cot {
		.top {
			.icon {
				width: 0.3rem;
				height: 0.3rem;
				margin: 0 auto 0.05rem;
			}

			div {
				color: #000000;
				font-size: 0.14rem;
			}
		}

		.item {
			padding: 0.15rem;
			border-bottom: 0.01rem solid #EAEDF8;
			.item-top {
				.name {
					font-size: 0.14rem;
					font-weight: 600;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #69717D;
				}

				.st-btn {
					background: #FFFFFF;
					box-shadow: 0rem 0rem 0.08rem 0rem rgba(0,0,0,0.25);
					border-radius: 0.13rem;
					padding: 0.05rem 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.13rem;
					color: #DCA25C;
					text-align: center;

					&.isEnd {
						color: #fff;
						background-color: #999;
					}
				}
			}

			.item-middle {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				margin: 0.15rem 0 0;

				.item-list {
					width: 46%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 0.12rem;
					font-weight: 400;
					color: #999999;
					line-height: 0.24rem;

					span {
						font-size: 0.14rem;
						font-weight: bold;
						color: #000000;
					}
				}
			}
		}
	}
</style>