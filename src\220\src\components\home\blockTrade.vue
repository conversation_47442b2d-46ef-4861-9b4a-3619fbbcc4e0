<template>
	<div class="page ">
		<top-back :title="title"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot">
					<div class="nav-box flex">
						<div class="nav-item" v-for="(item, index) in navList" :key="index"
							:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
							{{ item.name }}
						</div>
					</div>

					<template v-if="currmentIndex == 0">
						<div class="title flex flex-b">
							<div>{{$t('股票名称')}}</div>
							<div>{{ $t("dividend").txt2 }}</div>
							<div>{{ $t("dividend").txt13 }}</div>
						</div>
						<div class="list-item flex flex-b" v-for="(item, index) in chooseList" :key="index"
							@click="stockDetails(item)">
							<div class="flex-1 name">
								<div>{{ item.name }}</div>
								<span>{{ item.symbol }}</span>
							</div>
							<div class="price flex-1 text-center">{{ $formatMoney(item.price) }}</div>
							<div class="price flex-1 text-right">{{ $formatMoney(item.stock_num) }}</div>

							<!-- <div class="b-btn flex flex-c">
								{{ $t("dividend").btn }}
							</div> -->
						</div>
						<no-data v-if="!chooseList.length"></no-data>
					</template>
					<template v-else>
						<div class="title flex flex-b">
							<div class="txt">{{$t('股票名称')}}</div>
							<div class="txt text-center">{{ $t("dividend").txt5 }}</div>
							<div class="txt text-center">{{ $t("dividend").txt6 }}</div>
							<div class="txt text-center">{{ $t("dividend").txt7 }}</div>
							<!-- <div class="txt text-right">{{ $t("dividend").txt8 }}</div> -->
						</div>
						<div class="list-item items flex flex-b" v-for="(item, index) in myList" :key="index">
							<div class="inner-item">
								<div>{{ item.stock_name }}</div>
								<span>{{ item.stock_code }}</span>
							</div>
							<div class="inner-item text-center">{{ $formatMoney(item.buy_price) }}</div>
							<div class="inner-item text-center">{{ $formatMoney(item.zhang) }}</div>
							<div class="inner-item text-center">{{ $formatMoney(item.cj_num) }}</div>
						</div>
						<no-data v-if="!myList.length"></no-data>
					</template>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<van-popup v-model="show" position="center" :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-title">
					<img src="../../assets/market/positionImg02.png" style="width: 1.26rem;height: 1.28rem;" alt="" />
					<!-- <div class="txt">
						<div>{{ stockObj.name}}</div>
						<span>{{stockObj.code}}</span>
					</div> -->
				</div>
				<div class="pop-price t-c">
					<div class="t1">{{ $t("申购价格") }}</div>
					<div class="t">{{ $formatMoney(stockObj.price) || 0 }}</div>
				</div>
				<div class="pad">
					<div class="ipt">
						<!-- <div class="tt">{{ $t("new").b46 }}</div> -->
						<div class="pop-num">
							<input v-model="buyObj.handle" type="number" :placeholder="$t('dividend').txt12" @input="TypeInput($event)" />
						</div>
						<div class="txt">
							{{ $t("dividend").txt14 }}
							<span>{{ $formatMoney(countMoney) || 0 }}</span>
						</div>
					</div>
					<div class="pop-num" v-if="stockObj.password">
						<div>{{ $t("new").b47 }}</div>
						<input :placeholder="$t('new').t1" type="password" v-model="password" />
					</div>
					<div class="flex flex-b btips ">
						<div class="flex">
							<div class="t1">{{ $t("newt").t13 }}</div>
							<div class="t2">
								{{ stockType=='try'?$formatMoney(userInfo.try):$formatMoney(userInfo.usd) || 0 }}
							</div>
						</div>
						<div class="flex">
							<div class="t1">{{ $t("dividend").txt13 }}</div>
							<div class="t2">{{ $formatMoney(stockObj.stock_num) || 0 }}</div>
						</div>
					</div>
					<div @click="buyFn" class="big_btn">{{ $t("dividend").btn }}</div>
				</div>
				<div class="flex flex-c">
					<div class="icon close" @click="show = false"></div>
				</div>
			</div>
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "blockTrade",
		data() {
			return {
				loading: true,
				isLoading: false,
				navList: [{
						name: this.$t("dividend").tab1,
						type: 0,
					},
					{
						name: this.$t("dividend").tab2,
						type: 1,
					},
				],
				currmentIndex: 0,
				chooseList: [
					// {
					//   name: "名称",
					//   code: "007",
					//   price: 1000,
					//   stock_num:1000
					// },
				],
				myList: [
					// {
					//   stock_name: "stock_name",
					//   stock_code: "stock_code",
					//   buy_price: 1000,
					//   zhang: 1000,
					//   cj_num: 1000,
					//   status: 1,
					// },
				],
				show: false,
				stockObj: {},
				buyObj: {
					handle: null,
				},
				password: "",
				userInfo: {},
				currentItem: {},
				type: 0,
				title: '',
        stockType: 'try'
			};
		},
		computed: {
			countMoney() {
				return this.stockObj.price * this.buyObj.handle;
			},
		},
		created() {
			this.type = this.$route.query.type;
      this.stockType = this.$route.query.stockType;
			if (this.type == 0) {
				this.title = this.$t('market').fast6;
			} else if (this.type == 1) {
				this.title = this.$t('market').fast4;
			} else if (this.type == 2) {
				this.title = this.$t('market').fast5;
			}
			this.initData();
			this.getNew();
		},
		mounted() {},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.initData();
				this.getNew();
			},
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: this.$stockType,
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			TypeInput(e) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.buyObj.handle = e.target.value.replace(inputType, "");
				});
			},
			getNew() {
				this.$server.post("/trade/nbhllist", {
					type: this.stockType,
					dz_type: this.type,
				}).then((res) => {
					this.isLoading = false;
					this.loading = false;
					this.$refs.loading.close(); //关闭加载

					if (res.status == 1) {
						this.chooseList = res.data;
					}
				});
			},
			getMine() {
				this.$server.post("/trade/ustockslist", {
						type: this.stockType,
						dz_type: this.type,
						is_qc: 2,
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						if (res.status == 1) {
							this.myList = res.data;
						}
					});
			},
			stockDetails(stock) {
				this.show = true;
				this.currentItem = stock;
				this.stockObj = stock;
				//   this.$server
				//     .post("/trade/nbhldetails", {
				//       symbol: stock.code,
				//       type: "krw",
				//     })
				//     .then((res) => {
				//       this.stockObj = res.data;
				//     });
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.$refs.loading.open(); //开启加载

				if (index) this.getMine();
				else this.getNew();
			},
			buyFn() {
				if (!this.buyObj.handle) {
					this.$toast(this.$t("dividend").txt12);
					return;
				}

				let parmes = {
					symbol: this.stockObj.symbol,
					zhang: this.buyObj.handle,
					//password: this.password,
					type: this.stockType,
					is_qc: 2,
					dz_type: this.type,
					id: this.stockObj.id, //大宗增加传递参数，列表id
				};

				if (this.stockObj.password) {
					if (!this.password) {
						this.$toast(this.$t("new").t);
						return;
					}

					parmes.password = this.password;
				}

				this.$refs.loading.open(); //开启加载

				this.$server.post("/trade/buy_stock", parmes).then((res) => {
					this.$refs.loading.close();

					this.show = false;

					if (res.msg.indexOf('当前股票大宗交易最少买入') > -1) {
						this.$toast(res.msg.replace('当前股票大宗交易最少买入', this.$t('当前股票大宗交易最少买入')).replace('股', this.$t(
							'股')));
						return false;
					}
					if (res.msg.indexOf('当前大宗一天限购') > -1) {
						this.$toast(res.msg.replace('当前大宗一天限购', this.$t('当前大宗一天限购')).replace('次', this.$t('次')));
						return false;
					}

					if (res.msg) {
						this.$toast(this.$t(res.msg));
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.9rem 0 0.1rem;
		min-height: 100vh;
	}

	.nav-box {
		width: 100%;
		height: .4rem;
		position: fixed;
		top: .5rem;
		left: 0;
		z-index: 888;
		padding: 0.15rem;
		.nav-item {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #111111;
			text-align: center;
			margin-right: 0.3rem;
			&.active {
				font-weight: 600;
				font-size: 0.17rem;
				color: #E10414;
				position: relative;
				// &:after {
				// 	content: '';
				// 	display: block;
				// 	width: .1rem;
				// 	height: .03rem;
				// 	background: #0068FF;
				// 	border-radius: .02rem;
				// 	position: absolute;
				// 	bottom: -.03rem;
				// 	left: 43%;
				// }
			}
		}
	}

	.titles {
		padding: 0.1rem 0.1rem 0;
		border-bottom: 0.01rem solid #f5f5f5;
		div {
			font-size: 0.12rem;
			color: #464646;
		}
	}

	.van-popup {
		background-color: transparent;
	}
	::v-deep .van-popup{
		overflow-y: visible;
	}
	.pop {
		position: relative;
		background-color: #fff;
		border-radius: 0.13rem;
		.close{
			position: absolute;
			left: 50%;
			bottom: -0.5rem;
			transform: translate(-50%);
		}
		.btips {
			padding: 0.1rem 0;
			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #999999;
			}

			.t2 {
				font-weight: 400;
				font-size: 0.14rem;
				color: #e10414;
				margin-left: 0.05rem;
			}
		}

		.pop-title {
			border-radius: 0.13rem;
			background: linear-gradient(rgba(225, 4, 20, 0.3) 0%, rgba(225, 4, 20, 0.01) 100%);
			font-size: 0.16rem;
			text-align: center;
			img {
				margin-top: -0.5rem;
				width: 1.23rem;
				height: 1.28rem;
			}
			.close {
				position: absolute;
				top: .16rem;
				right: .15rem;
			}
			.txt {
				padding-top: .1rem;
				span {
					margin-top: 0.06rem;
					display: inline-block;
					color: #8C8C8C;
					font-size: .12rem;
				}
			}
		}

		.pop-price {
			padding: 0.1rem 0;

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.37rem;
				color: #E10414;
				margin-top: 0.1rem;
			}

			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.18rem;
				color: #333333;
			}
		}

		.pad {
			padding: 0.1rem 0.15rem;
		}
		
		.ipt {
			.tt {
				font-weight: 600;
				font-size: 0.14rem;
				color: #0e1028;
			}
			input {
				width: 100%;
				background: #F5F7FA;
				//   border-radius: 0.04rem;
				height: 0.4rem;
				line-height: 0.4rem;
				&::placeholder {
					font-size: 0.12rem;
					color: #8a8a8a;
				}
			}

			.txt {
				font-size: 0.12rem;
				color: #9a9fa5;
				span {
					font-size: 0.12rem;
					color: #c5585e;
				}
			}

		}

		.pop-num {
			margin-top: 0.05rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				height: 0.44rem;
				background: #F5F5F5;
				border-radius: 0.08rem;
				line-height: 0.44rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}
		}



		.b-btn {
			margin: 0.2rem 0 0;
		}
	}

	.cot {
		background: #FFFFFF;
		border-radius: 0.13rem;
		margin: 0 .12rem;
		overflow: scroll;

		.title {
			padding: .12rem;

			div {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}

			.txt {
				width: 20%;
			}
		}

		.list-item {
			padding: 0 .12rem 0.15rem;
			//margin-bottom: 0.15rem;
			.name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #333333;
				width: 33%;

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
				}
			}

			.price {
				font-weight: bold;
				font-family: PingFangSC, PingFang SC;
				font-size: 0.14rem;
				color: #333333;
				width: 33%;
			}

			.inner-item {
				font-weight: 600;
				font-size: 0.14rem;
				color: #24272C;
				width: 20%;
				word-break: break-all;
				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
				}
			}

			.b-btn {
				height: 0.36rem;
				text-align: center;
				margin: .1rem 0.1rem 0;
			}
		}
	}
</style>