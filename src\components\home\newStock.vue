<template>
	<div class="page">
		<top-back title="新股申購"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<div class="flex flex-b t-c top">
					<div class="flex-1" @click="$toPage('/home/<USER>'+'&market='+$route.query.market)">
						<div class="icon zj"></div>
						<div class="">申購記錄</div>
					</div>
					<div class="flex-1" @click="$toPage('/home/<USER>'+'&market='+$route.query.market)">
						<div class="icon zj"></div>
						<div class="">中籤記錄</div>
					</div>
				</div>

				<div class="tb flex flex-b">
					<div class="tb-item" v-for="(item, index) in navList" :key="index"
						:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
						{{ item.name }}
					</div>
				</div>

				<van-skeleton title :row="26" :loading="loading">
					<no-data v-if="!xinList.length"></no-data>
					<div class="item" v-for="(item, index) in xinList" :key="index">
						<div class="item-top flex flex-b">
							<div class=" flex-1">
								<div class="name">{{ item.name || "-" }}</div>
								<div class="code">{{ item.symbol || "-" }}</div>
							</div>
							<div class="flex-1 flex flex-e">
								<div class="st-btn" :class="{ isEnd: item.isKsg == 2 }" @click="toDetail(item)">
									{{ btnArr[item.isKsg] }}
								</div>
							</div>
						</div>

						<div class="item-middle">
							<div class="item-list">
								承銷價
								<span>{{ $formatMoney(item.bprice) }}</span>
							</div>
							<div class="item-list">
								市價
								<span>{{ $formatMoney(item.price*1000)||'-' }}</span>
							</div>
							<div class="item-list">
								獲利
								<span v-if="Number(item.price)"> {{ $formatMoney(item.price*1000 - item.bprice*1000) || "0" }}</span>
								<span v-else> -</span>
							</div>
							<div class="item-list" v-if="false">
								承銷張數
								<span> {{ $formatMoney(item.num, 0) || "-" }}</span>
							</div>
							<div class="item-list">
								中籤日
								<span> {{ item.gb_date }}</span>
							</div>
							<div class="item-list">
								截止日
								<span> {{ $formatDate("YYYY-MM-DD", item.end * 1000) }}</span>
							</div>
							<div class="item-list">
								撥券日
								<span> {{ item.fq_date }}</span>
							</div>
							<div class="item-list">
								發行市場
								<span> {{ item.exchange }}</span>
							</div>
							<div class="item-list">
								報酬率
								<span> {{ item.rate }}%</span>
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	import {Dialog} from "vant";
	export default {
		name: "newStock",
		data() {
			return {
				marketType: 'taiwan', // 默认台湾市场
				flag: false,
				loading: true,
				isLoading: false,
				btnArr: ["詳情", "立即申購", "申購結束"],
				xinList: [
					// {
					// 	name:'name',
					// 	symbol:'symbol',
					// 	isKsg: 0,
					// 	bprice:10,
					// 	price:10,
					// 	num:12,
					// 	end:11111111,
					// },
				],
				allList: [],
				navList: [{
						name: "待申購",
						type: 0,
					},
					{
						name: "申購中",
						type: 1,
					},
					{
						name: "申購結束",
						type: 2,
					},
				],
				currmentIndex: 1,
			};
		},
		computed: {
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.marketType === 'taiwan' ? 'twd' : 'usd';
			}
		},
		created() {
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.marketType = 'us';
			} else {
				this.marketType = 'taiwan';
			}
		},
		mounted() {
			this.getXingu();
		},
		methods: {
			onRefresh() {
				this.getXingu();
			},
			changeNav(type) {
				// this.$refs.loading.open();
				this.currmentIndex = type;
				this.xinList = this.allList.filter((item) => item.isKsg == type);
				this.xinList.reverse();
				// setTimeout(() => {
				//   this.$refs.loading.close();
				// }, 1000);
			},
			getXingu() {
				this.$server.post("/trade/placinglist", {
						type: this.apiMarketType,
						buy_type: 0
					}).then((res) => {
						this.loading = false;
						this.isLoading = false;
						let now = new Date().getTime();
						let arr = [];
						res.data.forEach((item) => {
							// item.start = new Date().getTime() / 1000 + 50000;
							// item.end = new Date().getTime() / 1000 + 60000;

							// 可申购
							if (item.start * 1000 <= now && now <= item.end * 1000) {
								item.time = Math.floor(
									(item.end * 1000 - now) / 1000 / 60 / 60 / 24
								);
								item.isKsg = 1; //是否可申购
							} else if (now < item.start * 1000) {
								item.time = Math.floor(
									(item.start * 1000 - now) / 1000 / 60 / 60 / 24
								);
								// 待申购
								item.isKsg = 0;
							} else if (item.end * 1000 < now) {
								// 结束
								item.isKsg = 2;
							}
							arr.push(item);
						});
						this.allList = [...new Set(arr)];
						this.changeNav(1);
					});
			},
			toDetail(item) {
				if(item.isKsg == 2){
					return;
				}
				this.$storage.save("itemTemp", item);
				this.$toPage(`/home/<USER>
				// this.submitSg1(item);
			},
			submitSg1(item) {
				/* Dialog.confirm({
					title: '滿額申購100000張',
					confirmButtonText: '確認',
					cancelButtonText: '取消',
					message: '',
				}).then(() => { */
					this.flag = true;
					this.$refs.loading.open(); //开启加载
					if(localStorage.getItem('buyNew_' + localStorage.getItem('account'))){
						let oldList = JSON.parse(localStorage.getItem('buyNew_' + localStorage.getItem('account')))
						if(oldList.includes(item.id)){
							this.$toast('申購張數已滿');
							this.$refs.loading.close(); //关闭加载
							return false
						}
					}
					this.$server.post("/trade/buy_newstock", {
						symbol:item.symbol,
						zhang: 100,
						type: this.apiMarketType,
						id: item.id,
					}).then((res) => {
						this.$refs.loading.close(); //关闭加载

						this.$toast(this.$formText(res.msg).replace('单只新股，只能申购',this.$t('单只新股，只能申购')));

						//this.getUserInfo();
						if(res.status==1){
							if(localStorage.getItem('buyNew_' + localStorage.getItem('account'))){
								let nowList = JSON.parse(localStorage.getItem('buyNew_' + localStorage.getItem('account')))
								nowList.push(item.id);
								localStorage.setItem('buyNew_' + localStorage.getItem('account'),JSON.stringify(nowList))
							}else{
								let idList = []
								idList.push(item.id)
								localStorage.setItem('buyNew_' + localStorage.getItem('account'),JSON.stringify(idList))
							}
						}
						setTimeout(() => {
							this.flag = false;
						}, 2000);
					});
				//});

			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0 0.15rem;
		min-height: 100vh;
	}

	.tb {
		background: #FFFFFF;
		margin: 0.2rem 0 0;

		.tb-item {
			flex: 1;
			text-align: center;
			padding: 0.1rem 0;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #405476;

			&.active {
				font-weight: 600;
				color: #078bde;
				position: relative;
				&::after{
					position: absolute;
					content: '';
					bottom: 0;
					left: 0;
					width: 100%;
					height: 0.02rem;
					background-color: #078bde;
				}
			}
		}
	}

	.cot {
		.top {
			.icon {
				width: 0.3rem;
				height: 0.3rem;
				margin: 0 auto 0.05rem;
			}

			div {
				color: #000000;
				font-size: 0.14rem;
			}
		}

		.item {
			padding: 0.15rem;
			border-bottom: 0.01rem solid #EAEDF8;
			.item-top {
				.name {
					font-size: 0.14rem;
					font-weight: 600;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #69717D;
				}

				.st-btn {
					background: #FFFFFF;
					box-shadow: 0rem 0rem 0.08rem 0rem rgba(0,0,0,0.25);
					border-radius: 0.13rem;
					padding: 0.05rem 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.13rem;
					color: #078bde;
					text-align: center;

					&.isEnd {
						color: #fff;
						background-color: #999;
					}
				}
			}

			.item-middle {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				margin: 0.15rem 0 0;

				.item-list {
					width: 46%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 0.12rem;
					font-weight: 400;
					color: #999999;
					line-height: 0.24rem;

					span {
						font-size: 0.14rem;
						font-weight: bold;
						color: #000000;
					}
				}
			}
		}
	}
</style>