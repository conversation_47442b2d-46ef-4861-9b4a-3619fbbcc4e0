<template>
	<!-- 新股申购 -->
	<div class="page ">
		<top-back :title="$t('new').b4"></top-back>

		<!--    <div class="nav-box">
			  <div class="nav-item" v-for="(item, idx) in navList" :key="idx" :class="{ active: currmentIndex === item.type }" @click="changeNav(item.type)">
				{{ item.name }}
				<span></span>
			  </div>
		</div> -->

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="list">
					<div class="item" v-for="(item, i) in ksgList" :key="i" @click="toDetail(item)">
							<div class="top flex flex-b ">
								<div class="flex-1">
									<div class="name flex">
										<div>{{ item.name || "-" }}</div>
										<div class="per flex">
											<div class="t2 ">{{ $t("newt").a4 }}</div>
											<div class="t1" :class="{'red':item.price - item.bprice>=0,'green':item.price - item.bprice<0}">{{ ((item.price - item.bprice) / item.bprice) * 100 == -100 ? "-" : ( ((item.price - item.bprice) / item.bprice) * 100 ).toFixed(2) }}%</div>
										</div>
									</div>
									<span>{{ item.symbol || item.code }}</span>
								</div>
								<div class="flex flex-e animate__animated animate__fadeIn">
									<div class="st-btn flex" v-if="item.isKsg"> {{ $t("申购") }} </div>
									<div class="st-btn end flex" v-else>{{ $t("new").a74 }}</div>
								</div>
							</div>

							<div class="item-middle flex flex-b flex-wrap text-center">
								<div class="item-list flex flex-b">
									<div class="t2">{{ $t("newt").a7 }}</div>
									<div class="t3">{{ $formatMoney(parseFloat(item.price),2) || "0" }}
									</div>
								</div>
								<div class="item-list flex flex-b">
									<div class="t2 ">{{ $t("newt").a8 }}</div>
									<div class="t3 red">
										{{ $formatMoney(item.price - item.bprice) }}
									</div>
								</div>
								<div class="item-list flex flex-b">
									<div class="t2 ">{{ $t("newt").a9 }}</div>
									<div class="t3">
										{{ $formatMoney(item.num) || "0" }}
										<!-- {{ $t("home").txt9 }} -->
									</div>
								</div>
								<div class="item-list flex flex-b">
									<div class="t2 ">{{ $t("newt").a10 }}</div>
									<div class="t3 ">{{ $formatMoney(parseFloat(item.bprice),2) || "0" }}
									</div>
								</div>
								<!--  <div class="item-list">
									<div class="t3">{{ item.markets || item.nums || '0' }}{{ $t('home').txt9 }}</div>
									 <div class="t2">{{ $t('home').txt7 }}</div>
								</div> -->
							</div>

						<div class="time flex">
							<div class="icon tm"></div>
							{{ $t("截止时间") }}:
							{{ $formatDate("DD-MM-YYYY hh:mm:ss", item.end * 1000) }}
						</div>
						<div class="b-btn flex flex-c" v-if="false">{{$t('申购')}}</div>
					</div>
				</div>

				<no-data v-if="isShow"></no-data>
			</van-skeleton>
		</van-pull-refresh>

		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newStock",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: 0,
				navList: [
					{ name: this.$t("新股申购"), type: 0 },
					{ name: this.$t("新股配售"), type: 1 },
				],
				ksgList: [
					// {
					// 	name:'name',
					// 	code:'code',
					// 	bprice:100,
					// 	price:111,
					// 	isKsg:1,
					// 	price:100,
					// 	num:111,
					// 	end:123333
					// },
				],
				userInfo: {},
				quantity: "",
        stockType: 'try'
			};
		},
		mounted() {
      this.stockType = this.$route.query.stockType;
			this.getList();
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getList();
			},
			changeNav(type) {
				this.currmentIndex = type;
				this.getList();
			},
			// 获取列表
			getList() {
				this.$server
					.post("/trade/placinglist", {
						type: this.stockType,
						buy_type: 0
					})
					.then((res) => {
						this.isLoading = false; //下拉刷新状态
						this.loading = false;
						if (res.status == 1) {
							let now = new Date().getTime();
							let arr = [];
							res.data.forEach((item) => {
								// 可申购
								if (item.start * 1000 < now && now < item.end * 1000) {
									item.time = Math.floor(
										(item.end * 1000 - now) / 1000 / 60 / 60 / 24
									);
									item.isKsg = true; //是否可申购
								} else if (now < item.start * 1000) {
									item.time = Math.floor(
										(item.start * 1000 - now) / 1000 / 60 / 60 / 24
									);
									// 待申购
									item.isKsg = false;
								}

								arr.push(item);
							});
							this.ksgList = [...new Set(arr)];

							if (!this.ksgList.length) {
								this.isShow = true;
							}
						}
					});
			},
			toDetail(item) {
				this.$storage.save("itemTemp", item);
				this.$toPage(`/home/<USER>
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.12rem 0.1rem;
		min-height: 100vh;
	}

	.list {
		background: #FFFFFF;
		border-radius: 0.13rem;
		.item {
			padding:.12rem;
			border-bottom: 0.01rem solid #f2f2f2;
			.top{
				.name{
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.16rem;
					color: #333333;
					.t2{
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}
					.t1{
						margin-left: 0.03rem;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #e10414;
					}
				}
				span{
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
				}
				.per {
					font-weight: 400;
					font-size: .11rem;
					color: #E97F88;
					height: .2rem;
					background: #FFFFFF;
					border-radius: 0.02rem;
					border: 0.01rem solid #C1C1C1;
					margin-left: .1rem;
					padding:0 .06rem;
				}
				.st-btn{
					height: 0.24rem;
					background: #FFE8E9;
					border-radius: 0.12rem;
					border: 0.01rem solid #E10414;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #E10414;
					padding:0 .16rem;
					&.end{
						background: #F7F8FE;
						color:#999;
					}
				}
			}
			.tt {
				font-weight: 600;
				// font-size: 0.16rem;
				font-size: 0.14rem;
				color: #000000;
			}

			.tt1 {
				font-size: 0.12rem;
				color: #8C8C8C;
				margin-top: 0.05rem;
			}

			.item-middle {
				padding: 0.1rem;margin-top:.2rem;
				background: linear-gradient( 90deg, #FFF0F1 0%, #FFFFFF 100%);
				border-radius: 0.08rem;
				.item-list {
					line-height: 0.3rem;
					width: 46%;
					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}

					.t3 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.14rem;
						color: #333333;
					}
				}
			}
		}

		.time {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #999999;
			padding: 0.1rem 0 0;
			//   &.tm {
			//     border-top: 0.01rem solid #ebebeb;
			//   }
		}

		.b-btn{
			margin: 0 .28rem .14rem;
			height:.36rem;
		}




	}

	.nav-box {
		position: fixed;
		width: 100%;
		left: 0;
		top: 100px;
		background-color: #fff;
		z-index: 999;
		padding: 0 20px;
		border-bottom: 2px solid #dedede;

		.nav-item {
			width: calc(100% / 2);
			font-size: 32px;
			font-weight: 500;
			color: #666666;
			text-align: center;
			position: relative;
			padding: 20px 0;

			&::after {
				content: "";
				width: 50%;
				height: 4px;
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
			}
		}

		.active {
			color: #3f5ad8;
			font-weight: 600;

			&::after {
				background: #3f5ad8;
			}
		}
	}
</style>
