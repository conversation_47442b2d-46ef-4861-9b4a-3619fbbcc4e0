<template>
	<div class="page ">
		<top-back title="關於我們"></top-back>
		<div class="list">
			<!-- <div class="item" v-html="info"></div> -->
			<div class="item">
				<table>
					<tr>
						<td>統一編號（統編）</td>
						<td>27407220</td>
					</tr>
					<tr>
						<td>公司名稱</td>
						<td>百佳圓投資股份有限公司</td>
					</tr>
					<tr>
						<td>代表人姓名</td>
						<td>陳其泰</td>
					</tr>
					<tr>
						<td>公司所在地</td>
						<td>臺南市東區裕忠路258號</td>
					</tr>
					<tr>
						<td>公司狀況</td>
						<td>核准設立</td>
					</tr>
					<tr>
						<td>資本總額(元)</td>
						<td>870,000,000</td>
					</tr>
					<tr>
						<td>實收資本額(元)</td>
						<td>870,000,000</td>
					</tr>
					<tr>
						<td>排名</td>
						<td>#3,172</td>
					</tr>
					<tr>
						<td>登記機關</td>
						<td>經濟部商業發展署</td>
					</tr>
					<tr>
						<td>每股金額(元)</td>
						<td>10</td>
					</tr>
					<tr>
						<td>已發行股份總數(股)	</td>
						<td>87,000,000</td>
					</tr>
					<tr>
						<td>核准設立日期</td>
						<td>093年07月26日</td>
					</tr>
					<tr>
						<td>最後核准變更日期</td>
						<td>113年01月05日</td>
					</tr>
					<tr>
						<td>複數表決權特別股</td>
						<td>無</td>
					</tr>
					<tr>
						<td>對於特定事項具否決權特別股</td>
						<td>無</td>
					</tr>
					<tr>
						<td>特別股股東被選為 董事、監察人之禁止或 限制或當選一定 名額之權利</td>
						<td>無</td>
					</tr>
					<tr>
						<td>類型</td>
						<td>股份有限公司</td>
					</tr>
					<tr>
						<td>類型</td>
						<td>公司基本資料</td>
					</tr>
				</table>
				
				<br />
				所營事業資料<br /><br />
				H701010住宅及大樓開發租售業<br /><br />
H703090不動產買賣業<br /><br />
H703100不動產租賃業<br /><br />
E801010室內裝潢業<br /><br />
J701040休閒活動場館業<br /><br />
J801030競技及休閒運動場館業<br /><br />
JB01010會議及展覽服務業<br /><br />
ZZ99999除許可業務外，得經營法令非禁止或限制之業務<br /><br />
H201010一般投資業<br /><br />
				聲明	本頁內所載之資料，所載資料之完整性、即時性和正確性仍應以資料來源單位為準。<br />
			</div>
		</div>

		<!-- <no-data v-if="!this.info"></no-data> -->
	</div>
</template>

<script>
	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			getInfo() {
				this.$server
					.post("/common/wenben", {
						name: "关于我们",
						type: "twd",
					})
					.then((res) => {
						this.info = res.data;
					});
			},
		},
		created() {
			//this.getInfo();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0.1rem 0;
		min-height: 100vh;
	}

	.list {
		.item {
			line-height: 0.24rem;
			margin: 0.2rem 0;
		}
		table{
			border:.01rem solid #ccc;
			width:100%;
			td{
				border-bottom:.01rem solid #ccc;
			}
			tr:last-child{
				td{border-bottom: 0;}
			}
		}
	}
</style>