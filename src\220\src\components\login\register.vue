<template>
	<div class="page">
		<top-back :isLogin="true" :title="$t('new').b19"></top-back>

		<div class="logo flex flex-c">
			<img src="../../assets/v2/res.png" style="width: 2.79rem;height: 1.85rem;" alt="" />
			<!-- <img :src="$cfg.logo" />
			<div>{{$cfg.title}}</div> -->
		</div>
		<div class="topTitle">
			<span class="txt">{{$t('login').txt1}} {{$cfg.title}}</span>
			<div>{{$t('login').txt2}}</div>
		</div>

		<div class="form">
			<div class="input">
				<div class="t" v-if="false">{{ $t("login").phone }}</div>
				<div class="inner flex flex-b">
					<div class="icon zh"></div>
					<input type="number" :placeholder="$t('login').phoneTip" maxlength="11" v-model="form.account" />
				</div>
			</div>

			<div class="input">
				<div class="t" v-if="false">{{ $t("login").password }}</div>
				<div class="inner flex flex-b">
					<div class="icon mm"></div>
					<input v-if="show" type="text" :placeholder="$t('login').passwordTip1" v-model="form.pwd" />
					<input v-else type="password" :placeholder="$t('login').passwordTip1" v-model="form.pwd" />
					<div class="icon" :class="show ? 'zy' : 'by'" @click="show = !show"></div>
				</div>
			</div>

			<div class="input">
				<div class="t" v-if="false">{{ $t("login").password2 }}</div>
				<div class="inner flex flex-b">
					<div class="icon mm"></div>
					<input v-if="show1" type="password" :placeholder="$t('login').passwordTip2" v-model="form.pwd1" />
					<input v-else type="text" :placeholder="$t('login').passwordTip2" v-model="form.pwd1" />
					<div class="icon" :class="show1 ? 'zy' : 'by'" @click="show1 = !show1"></div>
				</div>
			</div>
			<div class="input">
				<div class="t" v-if="false">{{ $t("login").code }}</div>
				<div class="inner flex flex-b">
					<div class="icon yqm"></div>
					<input type="text" :placeholder="$t('login').codeTip" v-model="form.inviter" />
				</div>
			</div>
			<div class="big_btn animate__animated animate__fadeIn" @click="register">
				{{ $t("new").b19 }}
			</div>
			<div class="tip flex flex-c">
				<div class="checkBox" @click="flagCheck=!flagCheck">
					<img src="../../assets/login/selectOn.png" v-if="flagCheck" />
					<img src="../../assets/login/select.png" v-else />
				</div>
				<div class="t" @click="flagCheck=!flagCheck">
          {{ $t("login").txt4 }}
          <span class="t1" @click="$toPage('/information/privacy')">《{{ $t("login").txt6 }}》</span>
        </div>
<!--				<div class="t1" @click="$toPage('/information/aboutInfo')">《{{ $t("login").txt5 }}》</div>-->
<!--				<div class="t">{{ $t("login").txt7 }}</div>-->
			</div>
			<div class="tip flex flex-c" style="margin-top:.3rem;">
				<!-- <span @click="clickKefu()">{{ $t("new").b14 }}</span> -->
				<div class="flex" @click="$toPage('/login/login')">
					<div class="t">{{ $t("new").b21 }}</div>
					<span class="t1">{{ $t("new").b9 }}</span>
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>
<script>
	import {
		mapMutations
	} from "vuex";
	export default {
		name: "login",
		data() {
			return {
				flagCheck: true,
				show: false,
				show1: false,
				cfg: {},
				form: {
					account: "",
					inviter: "",
					pwd: "",
					pwd1: "",
				},
			};
		},
		components: {},
		mounted() {

		},
		methods: {
			...mapMutations(["saveToken", "saveAccount"]),
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/logo.png");
				}
				this.cfg = val;
			},

			async register() {
				if (!this.form.account) {
					this.$toast(this.$t("login").phoneTip);
					return;
				}
				if (!this.form.pwd) {
					this.$toast(this.$t("login").passwordTip1);
					return;
				}
				if (!this.form.pwd1) {
					this.$toast(this.$t("login").passwordTip2);
					return;
				}
				if (this.form.pwd1 !== this.form.pwd) {
					this.$toast(this.$t("login").passwordTip3);
					return;
				}
				if (!this.form.inviter) {
					this.$toast(this.$t("login").codeTip);
					return;
				}

				if (!this.flagCheck) {
					this.$toast(this.$t("请阅读并同意用户协议"));
					return;
				}

				this.$refs.loading.open(); //开启加载
				const res = await this.$server.post("/user/register", {
					...this.form
				});
				this.$refs.loading.close();
				if (res.status == 1) {
					this.$toast(this.$t(res.msg));

					localStorage.setItem("AC", this.form.account);
					localStorage.setItem("PS", this.form.pwd);

					setTimeout(() => {
						//this.$toPage("/login/login");
						this.goLogin();
					}, 1000);
				}
			},
			clickKefu() {
				this.$toast(this.$t('login').txt3);
			},
			async goLogin() {
				let data = {
					account: this.form.account,
					password: this.form.pwd,
				};
				const res = await this.$server.post("/user/login", data);

				if (res.status == 1) {
					this.$toast(this.$t(res.msg));
					// 登录时首次，赋值token
					this.$server.defaults.headers.token = res.data.token;
					this.$server.defaults.headers.account = res.data.account;

					this.saveToken(res.data.token);
					this.saveAccount(res.data.account);
					setTimeout(() => {
						this.$toPage("/home/<USER>");
					}, 1000);
				} else {
					this.$toast(this.$t(res.msg));
				}
			}
		},
	};
</script>
<style lang="less" scoped="scoped">
	.page {
		width: 100vw;
		min-height: 100vh;
		padding: .2rem 0 0.5rem;
		background: #FFFFFF;
		.topTitle {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #999999;
			padding-left: .2rem;
			padding-bottom: .2rem;

			span {
				display: block;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.22rem;
				color: #2A2A2A;
				margin-bottom: .1rem;
			}
		}

		.logo {
			padding: .5rem .2rem 0;
			font-weight: 800;
			font-size: .2rem;
			color: #fff;

			// img {
			// 	max-height: .8rem;
			// 	max-width: .8rem;
			// 	margin-bottom: .1rem;
			// }
		}

		.form {
			background-color: #fff;
			border-radius: .5rem .5rem 0 0;
			padding: .1rem .25rem;
			.title {
				padding: 0.2rem 0;

				.t {
					font-weight: 600;
					font-size: 0.22rem;
					color: #000000;
				}

				.t1 {
					font-size: 0.14rem;
					color: #000000;
					margin-top: 0.05rem;
				}
			}

			.input {
				margin-bottom: .1rem;
				position: relative;
				.t {
					font-weight: 400;
					color: #888889;
					font-size: .14rem;
				}
			}

			.inner {
				height: .48rem;
				border-bottom: 0.01rem solid #E4EAF1;
				.icon{
					margin-right: 0.1rem;
				}
				img {
					width: .17rem;
					margin-right: .12rem;
				}
				.clear {
					margin-left: .12rem;
					font-size: .2rem;
					color: #999;
					line-height: .1rem;

					img {
						width: .18rem;
					}
				}
			}

			input {
				background: none;
				flex: 1;
				font-size: 0.15rem;
				color: #333;

				&::placeholder {
					color: #999;
				}
			}

			.tips {
				font-size: 0.13rem;
				color: #999;

				.txt {
					color: #000;
				}
			}

			.tip {
				// flex-wrap: wrap;
				//white-space: nowrap;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #000020;

				img {
					width: .16rem;
					margin-right: .1rem;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000020;
          white-space: nowrap;
				}

				.t1 {
					color: #e10414;
					margin: 0 0.05rem;
				}

				.t2 {
					color: #545454;
					margin-top: 0.05rem;
				}
			}

			.btn {
				background: #111111;
				border-radius: 0.08rem;
				margin: 0.5rem auto;
				padding: 0.15rem 0;
				font-size: 0.14rem;
				color: #ffffff;
				text-align: center;
			}
		}
	}
</style>
