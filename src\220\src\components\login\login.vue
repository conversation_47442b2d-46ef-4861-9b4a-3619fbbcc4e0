<template>
	<div class="page ">
		<top-back  :isLogin="true" :title="$t('new').b9"></top-back>
		<div class="logo flex flex-c">
			<img src="../../assets/v2/logi.png" style="width: 2.82rem;height: 1.9rem;" alt="" />
			<!-- <img :src="$cfg.logo" />
			<div>{{$cfg.title}}</div> -->
		</div>
		<div class="topTitle">
			<span class="txt">{{$t('new').b10}}</span>
			<div>{{$t('new').b11}}</div>
		</div>
		<div class="form">
			<div class="input">
				<div class="t" v-if="false"> {{ $t("new").b12 }} </div>
				<div class="inner flex flex-b">
					<!-- <img src="../../assets/login/loginIco1.png" /> -->
					<div class="icon zh"></div>
					<input :placeholder="$t('login').phoneTip" v-model="phone" maxlength="11" type="number" />
					<div class="clear" @click="phone=''">
						<img src="../../assets/login/close.png" />
					</div>
				</div>
			</div>
			<div class="input">
				<div class="t" v-if="false"> {{ $t("new").b13 }} </div>
				<div class="inner flex flex-b">
					<div class="icon mm"></div>
					<!-- <img src="../../assets/login/loginIco2.png" /> -->
					<input v-if="show" :placeholder="$t('login').passwordTip1" v-model="password" type="text" />
					<input v-else :placeholder="$t('login').passwordTip1" v-model="password" type="password" />
					<div class="icon" :class="show ? 'zy' : 'by'" @click="show = !show"></div>
					<div class="clear" @click="password=''">
						<img src="../../assets/login/close.png" />
					</div>
				</div>
			</div>

			<div class="tips flex flex-b">
				<div class="flex flex-b">
					<!-- <van-switch active-color="#e10414" inactive-color="#908C8C" v-model="checked" @change="save" :disabled="!phone" /> -->
					<van-checkbox v-model="checked" shape="square" checked-color="#E10414" icon-size="18px"></van-checkbox>
					<div class="mr5">{{ $t("new").b15 }}</div>
				</div>
				<div @click="clickKefu()">{{$t('newt').t2}}</div>
			</div>

			<div class="big_btn animate__animated animate__fadeIn" @click="login">
				{{ $t("login").title1 }}
			</div>
			<div class="tip flex">
				<div class="checkBox" @click="flagCheck=!flagCheck">
					<img src="../../assets/login/selectOn.png" v-if="flagCheck" />
					<img src="../../assets/login/select.png" v-else />
				</div>
				<div class="t" @click="flagCheck=!flagCheck">
          {{ $t("login").txt4 }}
          <span class="t1" @click="$toPage('/information/aboutInfo')">《{{ $t("login").txt5 }}》</span>
          <span class="t">{{ $t("login").txt7 }}</span>
          <span class="t1" @click="$toPage('/information/privacy')">《{{ $t("login").txt6 }}》</span>
        </div>
			</div>
			<div class="resText flex flex-c" @click="$toPage('/login/register')">{{$t('new').b18}}<span>{{$t('cashOut').txt5}}</span></div>

			<div class="tip" style="margin-top:.1rem;" v-if="false">
				<div class="t1" @click="clickKefu()">
					{{ $t("new").b14 }}
				</div>
			</div>
		</div>

		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>
<script>
	import {
		mapMutations
	} from "vuex";
	export default {
		name: "login",
		data() {
			return {
				flagCheck: true,
				show: false,
				cfg: {},
				phone: "",
				password: "",
				logo: "",
				checked: false,
			};
		},
		components: {},
		mounted() {
			if (this.$storage.get("account")) {
				this.phone = this.$storage.get("account");
			}
			if (this.$storage.get("PS")) {
				this.password = this.$storage.get("PS");
			}
		},
		destroyed() {},
		methods: {
			...mapMutations(["saveToken", "saveAccount"]),
			save(e) {
				if (e) {
					this.$storage.save("account", this.phone);
					this.$storage.save("PS", this.password);
				} else {
					this.$storage.remove("account");
					this.$storage.remove("PS");
				}
			},
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post('/common/config', {
					type: 'all'
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/logo.png");
				}
				this.cfg = val;
			},
			async login() {
				if (!this.phone) {
					this.$toast(this.$t("login").phoneTip);
					// Toast({
					//   message: this.$t("login").phoneTip,
					//   duration: 2000,
					// });
					return;
				}
				if (!this.password) {
					this.$toast(this.$t("login").passwordTip1);
					return;
				}
				if (!this.flagCheck) {
					this.$toast(this.$t("请阅读并同意用户协议"));
					return;
				}
				let data = {
					account: this.phone,
					password: this.password,
				};

				this.$refs.loading.open(); //开启加载
				const res = await this.$server.post("/user/login", data);
				this.$refs.loading.close(); //关闭加载
				if (res.status == 1) {
					this.$toast(this.$t(res.msg));
					// 登录时首次，赋值token
					this.$server.defaults.headers.token = res.data.token;
					this.$server.defaults.headers.account = res.data.account;

					this.saveToken(res.data.token);
					this.saveAccount(res.data.account);
					window.localStorage.setItem('PS', this.password);
					setTimeout(() => {
						this.$toPage("/login/loginAfter");
						// this.$toPage("/home/<USER>");
					}, 1000);
				} else {
					this.$toast(this.$t(res.msg));
				}
			},
			clickKefu() {
				this.$toast(this.$t('login').txt3);
			}
		},
	};
</script>
<style lang="less" scoped="scoped">
	.page {
		width: 100vw;
		min-height: 100vh;
		padding: .2rem 0 0.5rem;
		background: #FFFFFF;

		.topTitle {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #999999;
			padding-left: .2rem;
			padding-bottom: .2rem;

			span {
				display: block;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.22rem;
				color: #2A2A2A;
				margin-bottom: .1rem;
			}
		}

		.logo {
			padding: .5rem .2rem 0;
			font-weight: 800;
			font-size: .2rem;
			color: #fff;

			// img {
			// 	max-height: .8rem;
			// 	max-width: .8rem;
			// 	margin-bottom: .1rem;
			// }
		}

		.form {
			background-color: #fff;
			border-radius: .5rem .5rem 0 0;
			padding: .1rem .25rem;
			.title {
				padding: 0.2rem 0;

				.t {
					font-weight: 600;
					font-size: 0.22rem;
					color: #000000;
				}

				.t1 {
					font-size: 0.14rem;
					color: #000000;
					margin-top: 0.05rem;
				}
			}

			.input {
				margin-bottom: .1rem;
				position: relative;
				.t {
					font-weight: 400;
					color: #888889;
					font-size: .14rem;
				}
			}

			.inner {
				height: .48rem;
				border-bottom: 0.01rem solid #E4EAF1;
				.icon{
					margin-right: 0.1rem;
				}
				img {
					width: .17rem;
					margin-right: .12rem;
				}
				.clear {
					margin-left: .12rem;
					font-size: .2rem;
					color: #999;
					line-height: .1rem;

					img {
						width: .18rem;
					}
				}
			}

			input {
				background: none;
				flex: 1;
				font-size: 0.15rem;
				color: #333;

				&::placeholder {
					color: #999;
				}
			}

			.tips {
				font-size: 0.13rem;
				color: #999;
				.mr5{
					margin-left: 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #E10414;
				}
				.txt {
					color: #000;
				}
			}
			.resText{
				padding: 0.3rem 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #000020;
				span{
					color: #e10414;
				}
			}
			.tip {
				// flex-wrap: wrap;
				//white-space: nowrap;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #000020;

				img {
					width: .16rem;
					margin-right: .1rem;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000020;
				}

				.t1 {
					color: #e10414;
					margin: 0 0.05rem;
				}

				.t2 {
					color: #545454;
					margin-top: 0.05rem;
				}
			}

			.btn {
				background: #111111;
				border-radius: 0.08rem;
				margin: 0.5rem auto;
				padding: 0.15rem 0;
				font-size: 0.14rem;
				color: #ffffff;
				text-align: center;
			}
		}
	}
</style>
