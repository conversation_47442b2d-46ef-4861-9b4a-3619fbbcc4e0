<template>
	<!-- 市场 -->
	<div class="page ">
		<!-- <top-back title="行情"></top-back> -->
		<div class="topHead">
			<div class="flex" style="padding: 0 0.16rem;" @click="goBack">
				<div class="icon back animate__animated animate__fadeIn"></div>
				<div class="t flex-1">行情</div>
			</div>
			<van-skeleton title :row="6" :loading="loading1">
				<!-- 指數顯示 -->
				<div class="zx-cot">
					<div class="zx-list flex flex-b">
						<div class="zx-item flex" v-for="(item, i) in indexList" :key="i" v-show="i < 5">
							<div class="name">{{ item.symbolName }}</div>
							<div class="price" :class="Number(item.change) > 0 ? 'red' : 'green'">
								{{ $formatMoney(item.price) }}
							</div>
							<div class="flex flex-b per" :class="Number(item.change) > 0 ? 'red' : 'green'">
								<div class="flex mr10">
									<div class="icon" :class="Number(item.change) > 0 ? 'up' : 'down'"></div>
									{{ $formatMoney(item.change) }}
								</div>

								<div class="flex">
									<div class="icon" :class="Number(item.change) > 0 ? 'up' : 'down'"></div>
									{{ item.changePercent }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</div>
		<!-- 切換市場和自選 -->
		<!-- <div class="nav-box flex">
			<div class="nav-item" v-for="(item, index) in navList" :key="index"
				:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
				{{ item.name }}
			</div>
		</div> -->

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="6" :loading="loading1" v-if="false">
				<!-- 指數顯示 -->
				<div class="zx-cot">
					<div class="zx-list flex flex-b">
						<div class="zx-item" :class="Number(item.change) > 0 ? 'red-bg' : 'green-bg'"
							v-for="(item, i) in indexList" :key="i" v-show="i < 3">
							<div class="name">{{ item.symbolName }}</div>
							<div class="price" :class="Number(item.change) > 0 ? 'red' : 'green'">
								{{ $formatMoney(item.price) }}
							</div>
							<div class="flex flex-b per" :class="Number(item.change) > 0 ? 'red' : 'green'">
								<div class="flex mr10">
									<div class="icon" :class="Number(item.change) > 0 ? 'up' : 'down'"></div>
									{{ $formatMoney(item.change) }}
								</div>

								<div class="flex">
									<div class="icon" :class="Number(item.change) > 0 ? 'up' : 'down'"></div>
									{{ item.changePercent }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
			<div class="bg">
				<van-skeleton title :row="6" :loading="loading2">
					<div class="cy" @click="$toPage('/favorite/categoryList')">
						<div class="flex flex-b tt">
							<div class="t">產業報告</div>
							<div class="icon more"></div>
						</div>
						<div class="cy-list flex flex-b">
							<div class="cy-item" :class="item.change.sort > 0 ? 'red-border' : 'green-border'"
								v-for="(item, i) in categoryList" :key="i" v-show="i < 3">
								<div class="name">{{ item.sectorName || "-" }}</div>
								<!-- <div>{{ item.symbolName }}</div> -->
								<div class="price" :class="item.change.sort > 0 ? 'red' : 'green'">
									{{ $formatMoney(item.price.sort) || "-" }}
								</div>
								<div class="per flex flex-c" :class="item.change.sort > 0 ? 'red' : 'green'">
									<div class="icon" :class="item.change.sort > 0 ? 'up' : 'down'"></div>
									{{ $formatMoney(item.change.sort) }}
									{{ item.changePercent || "-" }}
								</div>
							</div>
						</div>
					</div>
				</van-skeleton>
				<van-skeleton title :row="26" :loading="loading">
					<!-- 列表 -->
					<div class="rm" v-if="currmentIndex == 1">
						<div class="tt flex flex-b" @click="$toPage('/favorite/moreList')">
							<div class="t">熱門股票</div>
							<div class="icon more"></div>
						</div>

						<!-- 市場切換 -->
						<div class="m-tab flex flex-b">
							<div class="m-item flex-1 t-c" v-for="(item, index) in tab"
								:class="{ active: item.type == mType }" :key="index" @click="changeMarket(item.type)">
								{{ item.name }}
							</div>
						</div>

						<div class="bg">
							<!-- 分類切換 -->
							<div class="change flex flex-b">
								<div class="change-item" v-for="(item, index) in mList"
									:class="{ active: item.sectorId == sortIndex }" :key="index"
									@click="changeSort(item.sectorId)">
									{{ item.name }}
								</div>
							</div>
						</div>

						<div class="rm-list">
							<div class="titles flex flex-b">
								<div class="flex-1">名稱</div>
								<div class="flex-1 t-c">價格</div>
								<div class="flex-1 t-c flex flex-c">
									成交量
									<div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div>
								</div>
								<div class="flex-1 t-r flex flex-e">
									漲跌
									<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
								</div>
							</div>

							<no-data v-if="!list.length"></no-data>

							<div class="rm-item flex flex-b" v-for="(item, i) in list" :key="i" @click="
				    $toDetail(`/market/stockDetail?symbol=${item.systexId}`, item)
				  " v-show="i < 10">
								<div class="flex-1">
									<div class="name">{{ item.symbolName }}</div>

									<div class="code">{{ item.systexId }}</div>
								</div>
								<div class="flex-1 t-c price" :class="item.change.sort > 0 ? 'red' : 'green'">
									{{ $formatMoney(item.price.sort) || "-" }}
								</div>
								<div class="flex-1 t-c">
									<!-- {{ $formatMoney(Number(item.volume) / 1000000) }} M -->
									{{ $formatMoney(Number(item.volumeK)) || "-" }} K
								</div>
								<div class="flex-1 t-r">
									<div class="flex flex-e per" :class="Number(item.change.sort) > 0 ? 'red' : 'green'">
										<div class="icon animate__animated animate__fadeIn"
											:class="Number(item.change.sort) > 0 ? 'up' : 'down'"></div>
										{{ $formatMoney(item.change.sort) || "-" }}
									</div>
									<div class="per" :class="Number(item.change.sort) > 0 ? 'red' : 'green'">
										{{ item.changePercent || "-" }}
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 自选列表显示 -->
					<zxList v-if="currmentIndex == 2" />
				</van-skeleton>
			</div>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	import zxList from "./zxList.vue";
	export default {
		name: "favorite",
		props: {},
		components: {
			zxList,
		},
		data() {
			return {
				marketType: 'taiwan', // 默认台湾市场
				mType: "TAI",
				// 台湾市场tab
				taiwanTab: [{
						name: "上市",
						type: "TAI",
					},
					{
						name: "上櫃",
						type: "TWO",
					},
				],
				// 美国市场tab
				usTab: [{
						name: "NASDAQ",
						type: "NASDAQ",
					},
					{
						name: "NYSE",
						type: "NYSE",
					},
				],
				show: true,
				show1: true,
				navList: [{
						name: "台股",
						type: 1
					},
					{
						name: "自選",
						type: 2
					},
				],
				currmentIndex: 1,
				indexList: [],
				loading: true,
				loading1: true,
				loading2: true,
				isLoading: false,
				categoryList: [],
				sort: [{
						name: "漲幅榜",
						id: 0
					},
					{
						name: "跌幅榜",
						id: 1
					},
					{
						name: "成交額",
						id: 2
					},
					// { name: "創高榜", id: 3 },
					// { name: "創低榜", id: 4 },
				],
				sortIndex: 0,
				list: [],
				type: "zhangfb",
				mList: [],
			};
		},
		computed: {
			filterName() {
				return (value) => {
					value = value.replace("股價", "");
					let indx = value.indexOf("指數");
					return value.slice(indx - 2, indx + 2);
				};
			},
			// 根据市场类型返回对应的tab
			tab() {
				return this.marketType === 'taiwan' ? this.taiwanTab : this.usTab;
			},
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.marketType === 'taiwan' ? 'twd' : 'usd';
			}
		},
		created() {
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.marketType = 'us';
				this.mType = 'NASDAQ'; // 美国市场默认NASDAQ
			} else {
				this.marketType = 'taiwan';
				this.mType = 'TAI'; // 台湾市场默认上市
			}
		},
		mounted() {
			this.getIndexList();
			this.getCategory();
			this.getMarkList();
		},
		methods: {
			goBack() {
        console.log(123)
        this.$router.go(-1);
			},
			changeMarket(type) {
				this.$refs.loading.open();
				this.mType = type;
				this.getMarkList();

				this.getCategory();
			},
			changeSort(type) {
				this.$refs.loading.open();
				this.sortIndex = type;
				this.getMarkListStock();
			},
			// 走的排行榜數據 弃用
			getList() {
				this.$server
					.post("/parameter/top", {
						type: "twd",
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.isLoading = false;
						// console.log("res", res);
						this.list = res.data;
					});
			},
			getCategory() {
				this.$server
					.post("/parameter/category", {
						category: this.mType
					})
					.then((res) => {
						let arr = [];
						if (res.status === 1) {
							res.data.forEach((item, index) => {
								if (index < 3) {
									this.$server
										.post("/parameter/stockservices", {
											category: this.mType,
											sectorId: item.sectorId,
											offset: 1,
										})
										.then((ras) => {
											ras.list.forEach((item2, index2) => {
												if (index2 < 1) {
													arr.push(item2);
												}
												this.loading2 = false;
												this.categoryList = arr;
											});
										});
								}
							});
						}
					});
			},

			// 获取市场下的分类
			getMarkList() {
				this.$server
					.post("/parameter/category", {
						category: this.mType
					})
					.then((res) => {
						if (res.status == 1) {
							this.mList = res.data;
							this.sortIndex = this.mList[0].sectorId;
							this.getMarkListStock();
						}
					});
			},

			// 获取分类下的股票
			getMarkListStock() {
				this.$server
					.post("/parameter/stockservices", {
						category: this.mType,
						sectorId: this.sortIndex,
						offset: 1,
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.loading2 = false;
						this.isLoading = false;
						this.list = res.list || [];
						console.log(this.list,2323232)
					});
			},

			changeNav(type) {
				this.currmentIndex = type;
			},
			getIndexList() {
				this.$server
					.post("/parameter/zhishu", {
						type: this.apiMarketType
					})
					.then((res) => {
						this.loading1 = false;
						if (res && res.data) {
							this.indexList = res.data;
						}
					});
			},

			onRefresh() {
				this.getIndexList();
				// this.getCategory();
				this.getMarkList();
			},

			changeList(type) {
				if (type == 0) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else {
					this.show1 = !this.show1;

					// 漲跌排序
					if (this.show1) {
						this.list = this.list.sort(
							(a, b) => Number(b.gainValue) - Number(a.gainValue)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.gainValue) - Number(b.gainValue)
						);
					}
				}
			},
		},
	};
</script>

<style scoped lang="less">
	::-webkit-scrollbar {
		display: none;
	}
	.m-tab {
		border-bottom: 0.01rem solid #dedede;
		.m-item {
			font-size: 0.14rem;
			color: #979797;
			position: relative;
			padding: 0.05rem 0;

			&::after {
				content: "";
				width: 0.2rem;
				height: 0.02rem;
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				background-color: transparent;
			}

			&.active {
				color: #549d7e;

				&::after {
					background-color: #549d7e;
				}
			}
		}
	}
	.rm {
		.tt {
			padding: 0.1rem 0;
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #182133;
			}

			.t1 {
				font-size: 0.12rem;
				color: #549d7e;
			}
		}

		.bg {
			overflow-x: scroll;

			.change {
				padding: 0.15rem 0;
				width: 800%;

				.change-item {
					background: #8e8e8e;
					border-radius: 0.3rem;
					font-size: 0.12rem;
					color: #ffffff;
					padding: 0.05rem 0;
					width: 30%;
					text-align: center;
					margin-right: 0.1rem;

					&.active {
						background: linear-gradient(124deg, #77ba90 0%, #7dbab4 100%);
					}
				}
			}
		}

		.rm-list {
			.titles {
				padding: 0.05rem 0;

				div {
					font-size: 0.12rem;
					color: #787878;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}

			.rm-item {
				padding: 0.1rem 0;
				border-bottom: 0.01rem solid #dedede;

				div {
					font-size: 0.12rem;
				}

				.name {
					font-weight: bold;
					font-family: PingFangSC, PingFang SC;
					font-size: 0.15rem;
					color: #333333;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					line-height: 0.11rem;
					margin-top: 0.05rem;
				}

				.price {
					font-weight: bold;
					font-family: PingFangSC, PingFang SC;
					font-size: 0.15rem;
					color: #333333;
					.icon {
						margin-left: 0.05rem;
					}
				}

				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.15rem;
					.icon {
						margin-right: 0.05rem;
					}
				}
			}
		}
	}
	.cy {
		.tt {
			padding: 0.1rem 0;
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #182133;
			}

			.t1 {
				font-size: 0.12rem;
				color: #549d7e;
			}
		}
		.cy-list {
			padding: 0.1rem 0;
			flex-wrap: wrap;

			.cy-item {
				width: 32%;
				text-align: center;
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
				border-radius: 0.07rem;
				margin-bottom: 0.1rem;
				padding: 0.1rem 0;

				&.red-border {
					border-color: #cf2829;
				}

				&.green-border {
					border-color: #52985d;
				}

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #182133;
				}

				.price {
					font-size: 0.16rem;
					padding: 0.1rem 0;
				}

				.icon {
					margin-right: 0.05rem;
				}

				.per {
					font-size: 0.12rem;
				}
			}
		}
	}
	.zx-cot {
		margin-top: 0.2rem;
		overflow-x: scroll;
		background-color: rgba(5, 8, 13, 0.2);
		.zx-list {
			padding: 0.1rem;
			min-width: 11rem;
			.zx-item {
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.1rem;
					color: #B0C0E4;
				}
				.price {
					margin: 0 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.1rem;
					color: #FFFFFF;
				}
				.per {
					div {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.1rem;
					}
					.icon {
						margin-right: 0.05rem;
					}
					.mr10 {
						margin-right: 0.1rem;
					}
				}
			}
		}
	}
	.top-fixed {
		padding: 0.15rem;
		width: 100%;
		height: 0.5rem;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		background: #f7f7f7;
		color: #181818;

		.zw {
			width: 0.24rem;
		}
	}
	.nav-box {
		// margin: 0 0 0.1rem;
		width: 100%;
		position: fixed;
		top: 0.5rem;
		left: 0;
		z-index: 999;
		background-color: #f7f7f7;
		padding: 0 0.15rem;

		.nav-item {
			padding: 0 0 0.1rem;
			font-size: 0.14rem;
			color: #8e8e8e;
			text-align: center;
			position: relative;
			margin-right: 0.2rem;

			&::after {
				content: "";
				width: 50%;
				height: 0.04rem;
				position: absolute;
				border-radius: 0.3rem;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}

			&.active {
				color: #549d7e;

				&::after {
					background: #549d7e;
				}
			}
		}
	}
	::v-deep .search {
		border-radius: 0.04rem;
	}

	.page {
		padding: 0.5rem 0 0.6rem;
		min-height: 100vh;
		background: #f7f7f7;
		.topHead{
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 2.5rem;
			background: url('../../assets/v1/hbg.png') no-repeat center/100%;
			padding: 0.2rem 0;
			.t {
				padding-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.17rem;
				color: #FFFFFF;
			}
		}
		.bg{
			z-index: 22;
      margin-top: .7rem;
			width: 100%;
			background: #F6F8FE;
			border-radius: 0.15rem 0.15rem 0rem 0rem;
			padding:0.1rem;
			overflow: auto;
		}
	}

	.btn-box {
		padding: 0.2rem 0.1rem;

		.b-btn {
			margin: 0;
		}
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.red {
		color: #ba3b3a;
	}

	.green {
		color: #68a03d;
	}

	.title {
		padding: 0 0.1rem;

		div {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
		}
	}

	.cot {
		.list {
			.titles {
				padding: 0.1rem 0.1rem 0;

				div {
					font-size: 0.12rem;
					color: #535353;
				}
			}

			.list-item {
				padding: 0.1rem;
				border-bottom: 0.01rem solid #f4f4f4;

				.wxz {
					margin-right: 0.05rem;
				}

				.name {
					font-size: 0.12rem;
					color: #000000;
				}

				.code {
					font-size: 0.1rem;
					color: #c4c4c4;
				}

				.price {
					font-size: 0.12rem;
					color: #0c0c0c;
					text-align: center;
				}

				.per {
					.t {
						font-size: 0.12rem;
						color: #0c0c0c;

						&.t1 {
							margin-left: 0.1rem;
						}
					}
				}
			}
		}
	}

	.btns {
		margin: 0.2rem 0.1rem;
		position: relative;

		&.bt {
			.btn {
				width: 100%;
			}

			&::after {
				display: none;
			}
		}

		&::after {
			content: "";
			position: absolute;
			width: 0.02rem;
			height: 50%;
			background-color: #888888;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}

		.btn {
			width: 48%;
			font-weight: 500;
			text-align: center;
			padding: 0.1rem 0;
			font-size: 0.12rem;
			color: #888888;

			.icon {
				margin-right: 0.05rem;
			}
		}
	}
</style>