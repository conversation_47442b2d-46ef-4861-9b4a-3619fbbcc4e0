<template>
	<div class="page ">
		<top-back :title="$t('market').fast7"></top-back>

		<!-- 图 -->
		<!--   <div class="chart"></div> -->

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="25" :loading="loading">
				<no-data v-if="isShow"></no-data>
				<div class="list" v-if="chooseList.length">
					<div class="item flex" style="align-items: flex-start;" v-for="(item, index) in chooseList" :key="index">
						<img class="pic" src="../../assets/market/img2.png" />
						<div class="top flex-1">
							<div class="name">{{ item.name }}</div>
							<div class="info flex flex-b">
								<div class="price text-center">
									<div>{{ $formatMoney(item.capital) }}</div>
									<span>{{ $t("fllow").txt4 }}</span>
								</div>
								<div class="text-center price">
									<div class="flex">
										<div style="color: #e10414;">{{ item.yprofit }}</div>
										<img src="../../assets/v2/upp.png" style="width: 0.11rem;height: 0.15rem;margin-left: 0.05rem;" alt="" />
									</div>
									<span>{{ $t("fllow").txt5 }}</span>
								</div>
								<div class="link">
									<div v-if="item.state == -1" class="s-btn bt flex flex-c">
										{{ $t("fllow").txt6 }}
									</div>
									<div v-else class="s-btn flex flex-c" @click="stockDetails(item)">
										{{ $t("fllow").txt7 }}
									</div>
								</div>
							</div>
							<div class="flex-1 t-c" v-if="false">
								<div class="t">{{ item.dprofit }}</div>
								<div class="t1">{{ $t("每日盈利") }}</div>
							</div>

							<div class="flex-1 t-r" v-if="false">
								<div class="t red">{{ $formatMoney(item.money) }}</div>
								<div class="t1">{{ $t("fllow").txt9 }}</div>
							</div>

							<div class="data flex flex-b" v-if="false">
								<div class="flex-1 t-c">
									<div class="t3" :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
										{{ item.yprofit }}
									</div>
									<div class="t4">{{ $t("fllow").txt5 }}</div>
								</div>
							</div>

							<div class="flex flex-b" v-if="false">
								<div class="t2">{{ $t("newt").t18 }} {{ item.sender }}</div>
							</div>

							<div class="time" v-if="false">
								<div class="flex flex-b">
									<div>{{ $t("fllow").txt13 }}</div>
									<span>{{ item.locktime || "-" }}</span>
								</div>
								<div class="flex flex-b">
									<div>{{ $t("fllow").txt14 }}</div>
									<span>{{ item.end || "-" }}</span>
								</div>
							</div>

							<div class="" v-if="false">
								<div v-if="item.state == -1" class="s-btn bt">
									{{ $t("fllow").txt6 }}
								</div>
								<div v-else class="s-btn" @click="stockDetails(item)">
									{{ $t("fllow").txt7 }}
								</div>
							</div>

						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<van-popup v-model="show" position="center" :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-title">
					<div class="txt">{{ stockObj.name}}</div>
				</div>
				<div class="pop-price t-c">
					<div class="t1">{{ $t("fllow").txt9 }}</div>
					<div class="t">{{ $formatMoney(stockObj.capital) || 0 }}</div>
				</div>
				<div class="pad">
					<div class="ipt">
						<div class="tt">{{ $t("new").b45 }}</div>
						<div class="">
							<input class="" v-model="buyObj.handle" type="number" :placeholder="$t('fllow').txt12" />
						</div>
						<div class="txt">
							{{ $t("fllow").txt9 }}
							<span>{{ $formatMoney(stockObj.money) }}</span>
						</div>
					</div>

					<div @click="buyFn" class="big_btn">{{ $t("fllow").btn }}</div>
				</div>
				<div class="text-right">
					<div class="icon close animate__animated animate__fadeIn" @click="show = false"></div>
				</div>
			</div>
		</van-popup>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "fllow",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: 0,
				stockObj: {},
				chooseList: [
					// {
					// 	name:'name',
					// 	code:'code',
					// 	capital:100,
					// 	yprofit:111,
					// 	state:1,
					// 	price:100,
					// 	num:111,
					// 	end:123333
					// },
				],
				show: false,
				buyObj: {
					handle: "",
				},
        stockType: 'try'
			};
		},
		computed: {},
		mounted() {
      this.stockType = this.$route.query.stockType;
			this.getNew();
		},

		methods: {
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getNew();
			},
			getNew() {
				this.$server.post("/trade/productlist", {
					type: this.stockType,
				}).then((res) => {
					this.isLoading = false;
					this.loading = false;

					res.data.forEach((vo) => {
						let now = Date.now() * 0.001;
						vo.state = now > vo.locktime ? -1 : now > vo.end ? 1 : 0;
						vo.state = 1;
					});
					// this.chooseList = res.data;

					if (!this.chooseList.length) {
						this.isShow = true;
					}
				});
			},
			stockDetails(stock) {
				this.stockObj = stock;
				this.show = true;
			},
			buyFn() {
				if (this.buyObj.handle < Number(this.stockObj.money)) {
					this.$toast(this.$t("new").t14 + this.stockObj.money);
					return;
				}
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/buy_product", {
						id: this.stockObj.id,
						money: this.buyObj.handle,
						type: this.stockType,
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						this.show = false;
						if (res.status == 1) {
							this.$toast(this.$t("new").t15);
						}
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.6rem 0.12rem 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	::v-deep .header {
		background: none;
	}

	.list {
		background: #FFFFFF;
		border-radius: 0.13rem;
		overflow: scroll;
		padding-top: .1rem;

		.item {
			padding: 0.12rem;
			border-bottom: 0.01rem solid #F0F0F0;
			.pic {
				width: 0.6rem;
				height: 0.6rem;
				background: #F3F3F3;
				border-radius: 50%;
				flex: none;
				margin-right: .12rem;
			}

			.top {
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #333333;
				}
				.price {
					font-weight: bold;
					font-size: .14rem;
					color: #24272C;
					span {
						display: inline-block;
						margin-top: 0.05rem;
						font-weight: 400;
						font-size: .11rem;
						color: #6F7274;
					}
				}
				.info {
					margin-top: 0.3rem;
					background: linear-gradient( 90deg, #FFF0F1 0%, #FFFFFF 100%);
					border-radius: 0.08rem;
					padding: 0.12rem;
				}
				.link {
					flex: none;
					.s-btn {
						width: 0.62rem;
						height: 0.24rem;
						min-width: .62rem;
						background: url(../../assets/v2/buyIn.png) no-repeat center/100%;
						color: #fff;
						border-radius: .04rem;
						padding: 0 .1rem;
						&.bt {
							background: #eee;
							color: #999;
							font-weight: 400;
						}
					}
				}
			}

			.data {
				background: linear-gradient(90deg, #EEF7FF 0%, #F9FCFF 23%, #F0F8FF 53%, #F7FBFF 100%);
				border: .01rem solid rgba(78, 155, 231, 0.15);
				border-radius: 0.06rem;
				padding: 0.1rem 0.1rem;
				margin: 0.1rem 0;

				.border {
					border-right: 0.01rem solid #dfdfdf;
				}

				.t3 {
					font-size: 0.14rem;
					color: #000000;
				}

				.t4 {
					font-size: 0.12rem;
					color: #838383;
					margin-top: 0.05rem;
				}
			}
		}



		.time {
			padding: .1rem 0;
			font-size: 0.12rem;
			color: #8C8C8C;
			line-height: 0.24rem;

			span {
				color: #383838;
				font-weight: 500;
			}
		}

	}

	.van-popup {
		background-color: transparent;
	}

	.close{
		position: absolute;
		left: 50%;
		bottom: -0.5rem;
		transform: translate(-50%);
	}
	::v-deep .van-popup{
		overflow-y: visible;
	}
	.pop {
		background-color: #fff;
		border-radius: .13rem;
		position: relative;

		.btips {
			padding: 0.1rem 0;

			.t1 {
				font-size: 0.12rem;
				color: #343434;
			}

			.t2 {
				font-weight: 500;
				font-size: 0.12rem;
				color: #63AB2B;
				margin-left: 0.05rem;
			}
		}

		.pop-title {
			padding: 0.16rem 0;
			border-radius: 0.13rem;
			background: linear-gradient(rgba(225, 4, 20, 0.3) 0%, rgba(225, 4, 20, 0.01) 100%);
			font-size: 0.16rem;
			text-align: center;
			img {
				margin-top: -0.5rem;
				width: 1.23rem;
				height: 1.28rem;
			}
			.close {
				position: absolute;
				top: .16rem;
				right: .15rem;
			}
			.txt {
				padding-top: .1rem;
				span {
					margin-top: 0.06rem;
					display: inline-block;
					color: #8C8C8C;
					font-size: .12rem;
				}
			}
		}

		.pop-price {
			padding: 0.1rem 0;
		
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.37rem;
				color: #E10414;
				margin-top: 0.1rem;
			}
		
			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.18rem;
				color: #333333;
			}
		}

		.pad {
			padding: 0.2rem 0.15rem;
		}

		.ipt {
			.tt {
				font-weight: 600;
				font-size: 0.14rem;
				color: #0e1028;
			}
			input {
				margin: 0.12rem 0;
				width: 100%;
				background: #F5F7FA;
				//   border-radius: 0.04rem;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.12rem;
				&::placeholder {
					font-size: 0.12rem;
					color: #8a8a8a;
				}
			}
		
			.txt {
				font-size: 0.12rem;
				color: #9a9fa5;
				span {
					font-size: 0.12rem;
					color: #c5585e;
				}
			}
		
		}
		.pop-num {
			margin-top: 0.05rem;
		
			input {
				margin: 0.05rem 0;
				width: 100%;
				height: 0.44rem;
				background: #F5F5F5;
				border-radius: 0.08rem;
				line-height: 0.44rem;
				padding: 0 0.1rem;
		
				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}
		}



		.b-btn {
			margin: 0.2rem 0 0;
		}
	}
</style>