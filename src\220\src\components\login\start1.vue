<template>
	<div class="page">
		<div class="top">
			<div class="tit">{{$t('new').newt2}} {{$cfg.title}}</div>
			<!-- <div class="icon logon"></div> -->
			<!-- <div class="title">
				<div>{{$t('logon').txt1}}</div>
				<span>{{$t('logon').txt2}}</span>
			</div> -->
			<van-swipe class="my-swipe" indicator-color="white">
				<van-swipe-item>
					<img src="../../assets/v2/png1.png" style="width: 2.49rem;height: 1.9rem;" alt="" />
				</van-swipe-item>
				<van-swipe-item>
					<img src="../../assets/v2/png2.png" style="width: 2.83rem;height: 2.3rem;" alt="" />
				</van-swipe-item>
				<van-swipe-item>
					<img src="../../assets/v2/png3.png" style="width: 2.96rem;height: 2.21rem;" alt="" />
				</van-swipe-item>
				<van-swipe-item>
					<img src="../../assets/v2/png4.png" style="width: 3.39rem;height: 2.26rem;" alt="" />
				</van-swipe-item>
				<van-swipe-item>
					<img src="../../assets/v2/png5.png" style="width: 3.35rem;height: 2.23rem;" alt="" />
				</van-swipe-item>
			</van-swipe>
		</div>

		<div class="btn flex flex-b">
			<div class="bt flex-1" @click="$toPage('/login/register')">
				{{ $t("new").b19 }}
			</div>
			<div class="bt bt1 flex-1" @click="$toPage('/login/login')">
				{{ $t("login").title1 }}
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "start1",
		props: {},
		data() {
			return {
				cfg: {},
			};
		},
		components: {},
		mounted() {},
		methods: {},
	};
</script>

<style scoped lang="less">
	.my-swipe .van-swipe-item {
	    color: #fff;
	    font-size: 20px;
	    line-height: 150px;
	    text-align: center;
	  }
	  ::v-deep .van-swipe__indicators{
		  align-items: center;
	  }
	  ::v-deep .van-swipe__indicator{
		  width: 0.15rem;
		  height: 0.15rem;
		  background: #FFFFFF;
		  border: 0.02rem solid #CDCDCD;
	  }
	::v-deep .van-swipe__indicator--active{
		width: 0.2rem;
		height: 0.2rem;
		background: #FFFFFF;
		border: 0.04rem solid #E10414;
	}
	.page {
		padding: 0.8rem 0.3rem 0.5rem;
		background-color: #fff;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
	}

	.top {
		width: 100%;
		margin: 0 auto;

		.tit {
			margin-bottom: 0.6rem;
			text-align: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.22rem;
			color: #111111;
		}

		.title {
			margin-top: .65rem;
			font-weight: 500;
			font-size: .18rem;
			color: #042620;

			span {
				margin-top: .17rem;
				font-weight: 400;
				font-size: .14rem;
				color: #666666;
				display: block;
			}
		}
	}

	.btn {
		width: 100%;

		.bt {
			height: 0.4rem;
			line-height: 0.4rem;
			background: #E10414;
			border-radius: 0.08rem;
			text-align: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #FFFFFF;

			&.bt1 {
				background: transparent;
				border-radius: 0.08rem;
				border: 0.01rem solid #E10414;
				color: #E10414;
				margin-left: .16rem;
			}
		}
	}
</style>