<template>
	<div class="page">
		<top-index></top-index>
		
		<div>
			<div class="money">
				<div class="flex flex-b tops ">
					<div class="flex-1">
						<div class="t flex" @click="show = !show">
							{{ $t("mine").txt8 }}
							<!-- <div class="icon animate__animated animate__fadeIn" :class="show ? 'zy' : 'by'"></div> -->
						</div>
						<div class="num">
							{{$currency}}{{ show ? $formatMoney(totalAssets) || 0 : "****" }}
						</div>
					</div>
		
					<!-- 图 -->
					<!-- <div class="animate__animated animate__fadeIn">
						<div class="" id="main"></div>
					</div> -->
				</div>
		
				<div class="nums">
					<div class="item flex flex-b">
						<div class="t2">
							<span class="c1 animate__animated animate__fadeIn"> </span>
							{{ $t("mine").txt1 }}
						</div>
						<div class="t1">
							{{ show ? $formatMoney(totalAssets) || 0 : "****" }}
						</div>
					</div>
					<div class="item flex flex-b">
						<div class="t2">
							<span class="c1 animate__animated animate__fadeIn"> </span>{{ $t("mine").txt2 }}
						</div>
						<div class="t1">
							{{ show ? $formatMoney(userInfo.Germany) || 0 : "****" }}
						</div>
					</div>
					<div class="item flex flex-b">
						<div class="t2">
							<span class="c2 animate__animated animate__fadeIn"> </span>{{ $t("mine").txt3 }}
						</div>
						<div class="t1">
							{{ show ? $formatMoney(totalProfit) || 0 : "****" }}
						</div>
					</div>
					<div class="item flex flex-b">
						<div class="t2">
							<span class="c3 animate__animated animate__fadeIn"> </span>{{ $t("mine").txt4 }}
						</div>
						<div class="t1">
							{{ show ? $formatMoney(freezeAssets) || 0 : "****" }}
						</div>
					</div>
					<!-- <div class="item">
						<div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
						<div class="t2">{{ $t("new").a36 }} (USD)</div>
					</div> -->
				</div>
			</div>
			
			<div class="btn flex flex-b">
				<div class="link1 flex flex-c" @click="$toPage('/information/recharge')">{{ $t("new").b }}</div>
				<div class="link2 flex flex-c" @click="$toPage('/information/cashOut')">{{ $t("new").a23 }}</div>
			</div>
			
			<div class="list">
				<div class="item flex flex-b" v-for="(item,idx) in list" :key="idx" @click="goUrl(item.url)">
					<div class="flex">
						<img :src="item.icon" />
						<div>{{item.name}}</div>
					</div>
					<div class="icon jt1"></div>
				</div>
			</div>
		</div>
		<loading ref="loading" />
		<tab-bar :current="4"></tab-bar>
	</div>
</template>

<script>
	export default {
		name: "information",
		props: {},
		data() {
			return {
				chartData: [],
				show: true,
				loading: true,
				isLoading: false,
				kfUrl: "",
				userInfo: {},
				currentIndex: 0,
				totalProfit: 0,
				totalAssets: 0,
				freezeAssets: 0,
				list:[
					{
						name: this.$t("mine").menu5,
						icon: require('../../assets/mine/walletIco1.png'),
						url: "/information/fundRecord",
					},
				],
			};
		},
		computed: {},
		mounted() {
			// this.getConfig();
			//this.getTotalProfit();
			this.getTotalAssets();
		},
		methods: {
			goUrl(url) {
				if (url == "kefu") {
					this.goKefu();
				} else if (url == "authInfo") {
					if (this.userInfo.is_true == 1) {
						this.$toast(this.$t("new").tt);
					} else {
						this.$toPage("/information/authInfo");
					}
				}else if(url=='exit'){
					this.exit();
				} else {
					this.$toPage(url);
				}
			},
			exit() {
				this.$storage.remove("tokend");
				this.$refs.loading.open();
				setTimeout(() => {
					this.$refs.loading.close();
					this.$toPage("/login/login");
				}, 1000);
			},
			showCzTips() {
				this.$toast(this.$t("czts"));
			},
			// 下拉刷新
			onRefresh() {
				this.isLoading = false;
				// this.getConfig();
				//this.getTotalProfit();
				//this.getTotalAssets();
			},
			goKefu() {
				this.getConfig();
			},
			//盈利資金
			async getTotalProfit() {
				const res = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let fdyk = 0;
				if (res.status == 1) {
					fdyk = Number(res.data.fdyk);
				}
				this.totalProfit = fdyk;
			},
			// 获取总资产
			async getTotalAssets() {
				this.$refs.loading.open(); //开启加载
	
				const res = await this.$server.post("/user/getUserinfo");
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let krw = Number(res.data.Germany || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: this.$stockType,
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}
	
				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: this.$stockType,
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);
	
					subscriptionProfit = total + total1;
				}
	
				// 日内交易的申请冻结 接口报错
				const res3 = await this.$server.post("/trade/urnjylist", {
					type: this.$stockType,
				});
				let dayDelFreeze = 0;
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					res3.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit));
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDelFreeze = total;
				}
	
				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});
	
					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}
	
				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: this.$stockType,
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}
	
				// 日内交易持仓
				const res6 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					buy_type: 1,
				});
				let dayDealFreeze = 0;
				delete res6.data.ccsz;
				delete res6.data.fdyk;
				let dataArr1 = res6.data;
				if (dataArr1.length) {
					let arr = [];
					dataArr1.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDealFreeze = total;
				}
	
				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					followingFreeze +
					dayDelFreeze +
					bigDealFreeze +
					positionFreeze +
					dayDealFreeze;
				// 总资产
				this.totalAssets = krw + this.freezeAssets;
				this.$refs.loading.close();
	
				
			},
	
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				// this.kfUrl = val.kefu;
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			
		},
	};
</script>

<style lang="less">
	.page{
		padding: .5rem 0 0.5rem;
	}
	.money {
	
		.tops {
			// background: linear-gradient(90deg, #469d6f, #184856);
			//   margin-bottom: 0.1rem;
			padding:.16rem .16rem 0;
			border-bottom: .08rem solid #eee;
			.t {
				font-size: 0.14rem;
				color: #000000;
	
				.icon {
					margin-left: 0.05rem;
				}
			}
	
			.num {
				font-weight: 600;
				font-size: 0.24rem;
				color: #4D8BE5;
				margin: 0.1rem 0 0.2rem;
			}
	
			.line {
				background: rgba(255, 255, 255, 0.5);
				border-radius: 0.03rem;
				height: 0.07rem;
				position: relative;
	
				&::after {
					content: "";
					width: 80%;
					height: 100%;
					position: absolute;
					top: 0;
					left: 0;
					z-index: 1;
					background: linear-gradient(45deg, #88d68b, #ffffff);
					border-radius: 0.03rem;
				}
			}
	
			.zc {
				margin-left: 0.3rem;
			}
		}
	
		.nums {
			padding: .16rem;
			.item {
				// padding: 0 0 0.1rem;
				text-align: center;
				line-height: 0.24rem;
	
				.t1 {
					font-weight: 600;
					font-size: 0.14rem;
					color: #000000;
				}
	
				.t2 {
					font-size: 0.12rem;
					color: #656363;
				}
			}
		}
		
	}
	.btn{
		border-bottom: .08rem solid #eee;
		padding:.14rem .36rem;
		font-size: .14rem;
		font-weight: 500;
		.link1{
			min-width: 1.02rem;
			height: .28rem;
			background: #63AB2B;
			border-radius: .3rem;
			color: #FFFFFF;
		}
		.link2{
			min-width: 1.02rem;
			height: .28rem;
			background: #B7EA93;
			border-radius: .3rem;
			color: #63AB2B;
		}
	}
	
	.list{
		padding:.16rem;
		.item{
			font-weight: 400;
			font-size: .14rem;
			img{width:.22rem;margin-right:.1rem;}
		}
	}
	
</style>