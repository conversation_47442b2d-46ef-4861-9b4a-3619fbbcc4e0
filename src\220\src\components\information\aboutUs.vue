<template>
	<div class="page ">
		<top-back :title="$t('关于我们')"></top-back>

		<div class="list">
			<div class="item">
				{{ $t("about").txt2 }}
			</div>
			<div class="item" v-html="info"></div>
			<no-data v-if="!info"></no-data>
		</div>
	</div>
</template>

<script>
	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			initData() {
				this.$server
					.post("/common/wenben", {
						type: this.$stockType,
						name: "关于我们"
					})
					.then((res) => {
						if (res.status == 1) {
							this.info = res.data;
						}
					});
			},
		},
		created() {
			this.initData();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		// background: #fff;
		padding: 0.6rem 0.15rem 0.2rem;
		min-height: 100vh;
	}

	.list {
		background: #ffffff;
		border-radius: 0.08rem;
		padding: 0.15rem;

		.item {
			line-height: 0.2rem;
			// margin-bottom: 0.1rem;
		}
	}
</style>