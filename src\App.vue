<template>
	<div id="app">
		<keep-alive>
			<router-view v-if="$route.meta.keepAlive" />
		</keep-alive>
		<router-view v-if="!$route.meta.keepAlive" />
	</div>
</template>
<script>
	import "./assets/js/com";

	export default {
		data() {
			return {};
		},
		beforeCreate() {
			var _this = this;

			var first = null;

			document.addEventListener("plusready", function() {
				var webview = plus.webview.currentWebview();
				var ver = plus.runtime.version;
				// window.localStorage.setItem("pdver",ver)
				webview.setStyle({
					popGesture: "none"
				});
				plus.key.addEventListener("backbutton", function() {
					webview.canBack(function(e) {
						if (e.canBack) {
							webview.back();
						} else {
							if (!first) {
								first = new Date().getTime();
								setTimeout(function() {
									first = null;
								}, 1000);
							} else {
								if (new Date().getTime() - first < 1000) {
									plus.runtime.quit();
								}
							}
						}
					});
				});
				plus.navigator.setStatusBarBackground("#FFF");
				plus.navigator.setStatusBarStyle("dark");
			});
		},
		destroyed() {},
		methods: {},
	};
</script>
<style lang="less">
	@import "./assets/css/style.less";
	@import "./assets/css/animate.css";

  .van-dialog__confirm, .van-dialog__confirm:active{
    color: #DCA25C !important;
  }

  .van-skeleton__row{
    background-color: transparent !important;
  }
  .van-skeleton__title{
    background-color: transparent !important;
  }
  
  input:-webkit-autofill {
    box-shadow:0 0 0 1000px transparent inset !important; // 颜色可以随意，这里设置的是透明色
  }
  input:-internal-autofill-previewed,
  input:-internal-autofill-selected {
  -webkit-text-fill-color: #333 !important; // 可加可不加
    transition: background-color 5000s ease-in-out 0s !important;
  }
</style>