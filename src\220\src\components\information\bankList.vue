<template>
	<div class="page ">
		<top-back :title="$t('mine').menu3"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<!-- <first-loading ref="firstLoading"></first-loading> -->
				<div class="list">
					<div class="item" v-for="(item, index) in bankList" :key="index">
						<div class="flex flex-b">
							<div class="name">
								<div>{{ item.bank_name }}</div>
								<span>{{ formatNum(item.bank_num) }}</span>
							</div>
							<div class="icon del animate__animated animate__fadeIn" @click="delbakcard(item.id)"></div>
						</div>

					</div>
				</div>

				<div class="padd">
					<div class="big_btn flex flex-c animate__animated animate__fadeIn"
						@click="$toPage('/information/addBankCard')">
						<div>
							{{ $t("bankManagement").btn }}
						</div>

					</div>
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "bankList",
		props: {},
		data() {
			return {
				isLoading: false,
				bankList: [],
				flag: false,
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {
			formatNum() {
				return (value) => {
					let str = value.slice(0, 4);
					return `${str} **** **** ****`;
				};
			},
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		methods: {
			onRefresh() {
				this.initData();
			},
			delbakcard(bankid) {
				if (this.flag) return;
				this.$refs.loading.open(); //开启加载
				this.flag = true;
				this.$server
					.post("/user/delbakcard", {
						bankid
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(this.$t("new").a22);
							setTimeout(() => {
								this.initData();
							}, 1500);
						}
					});
			},
			initData() {
				this.$server
					.post("/user/cardList", {
						size: 200,
						page: 1
					}, (failres) => {
						that.bankList = [];
					})
					.then((res) => {
						// this.$refs.firstLoading.close();
						this.isLoading = false;
						this.flag = false;
						var arr = [];
						for (var i in res.data) {
							var row = res.data[i];
							if (row.bank_name != "TRC" && row.bank_name != "ERC") {
								arr.push(row);
							}
						}
						this.bankList = arr;
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.12rem 0.2rem;
		min-height: 100vh;
		.padd {
			padding: 0.2rem 0;
			.b-btn {
				margin: 0rem .4rem;
				background-color: #fff;
				font-weight: 400;
				font-size: .14rem;
				color: #6F7274;
				padding: 0.2rem 0;

				img {
					width: .24rem;
				}
			}
		}

		.cot {
			.list {
				.item {
					padding: 0.2rem 0.12rem;
					background: #FFFFFF;
					box-shadow: 0rem 0.01rem 0.2rem 0rem rgba(255,255,255,0.35);
					border-radius: 0.07rem;
					overflow: hidden;
					margin-bottom: 0.1rem;
					.name {
						font-family: PingFangTC, PingFangTC;
						font-weight: 400;
						font-size: 0.15rem;
						color: #333333;
						span{
							margin-top: 0.1rem;
							display: inline-block;
							font-family: PingFangTC, PingFangTC;
							font-weight: 600;
							font-size: 0.2rem;
							color: #333333;
						}
					}

					.t {
						font-size: 0.2rem;
						color: #fff;
						font-weight: 600;
						height: .51rem;
						background: rgba(0, 0, 0, 0.43);
					}
				}
			}
		}
	}
</style>