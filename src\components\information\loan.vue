<template>
	<div class="page">
		<div class="bg">
			<div class="header flex flex-b">
				<div class="icon back" @click="goBack"></div>
				<div class="t flex-1">慈善融資</div>
				<div></div>
			</div>
			<!-- <top-back title="借貸專區"></top-back> -->
			<!-- <div class="jl" @click="$toPage('/information/loanRecords')">{{ $t("借款記錄") }}</div> -->
			<div class="xyf flex flex-c" v-if="false">
				<div class="">
					<div class="t">{{ userdata.xyf }}</div>
					<div class="">{{ $t("信用分") }}</div>
				</div>
			</div>
			<div class="flex-column-item list">
				<div class="tt">{{ $t("可融資額度") }}</div>
				<div class="t">{{ $formatMoney(userdata.twdkjed, 0) }}</div>
				<div class="t0">{{ $t("已融資額度") }}{{ $formatMoney(userdata.twdyjed, 0) }}</div>
				<div class="flex flex-b cont">
					<div class="flex">
						<div class="icon xz"></div>
						隨時可還款
					</div>
					<div class="flex">
						<div class="icon xz"></div>
						最快秒到帳
					</div>
				</div>
				<div class="btns">
					<div class="b-btn flex-1" @click="btn1">{{ $t("融資") }}</div>
					<div class="b-btn bt flex-1" style="background-color: #3daa80;"  @click="btn2" >立即聯絡客服</div>
				</div>
			</div>
		</div>
		<div class="nav">
			<div class="navItem flex flex-c">融資記錄</div>
		</div>
		<loanRec></loanRec>
		<van-popup v-model="show1" round position="center" :style="{ width: '90%' }">
			<div class="ipt">
				<input type="number" v-model="jkVal" :placeholder="$t('請輸入金額')" />
				<div class="b-btn" @click="beforeClose1">{{ $t("融資") }}</div>
			</div>
		</van-popup>
		<van-popup v-model="show2" round position="center" :style="{ width: '90%' }">
			<div class="ipt">
				<input type="number" v-model="hkVal" :placeholder="$t('請輸入金額')" />
				<div class="b-btn" @click="beforeClose2">{{ $t("還款") }}</div>
			</div>
		</van-popup>
	</div>
</template>
<script>
	import loanRec from './loanRecords.vue'
	export default {
		name: "loan",
		data() {
			return {
				userdata: {},
				show1: false,
				jkVal: "",
				show2: false,
				hkVal: "",
			};
		},
		components: {
			loanRec
		},
		mounted() {
			this.getUserinfo();
		},
		methods: {
			goBack() {
				this.$router.go(-1);
			},
			getUserinfo() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userdata = res.data;
					}
				});
			},
			btn1() {
				this.jkVal = "";
				this.show1 = true;
			},
			async toKefu() {
				//this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				//this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			btn2() {
				this.toKefu()
				return
				this.hkVal = "";
				this.show2 = true;
			},
			beforeClose1() {
				if (!this.jkVal) {
					this.$toast("請輸入金額");
					return;
				}
				this.$server
					.post("/user/to_jq", {
						money: this.jkVal,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.getUserinfo();
							this.show1 = false;
							this.$toast("借款成功");
						} else {
							this.$toast(this.$t(res.msg));
						}
					});
			},
			beforeClose2() {
				if (!this.hkVal) {
					this.$toast("請輸入金額");
					return;
				}
				this.$api
					.request2("/user/to_hq", {
						money: this.hkVal,
						type: "twd",
					})
					.then((res) => {
						if (res.data.status == 1) {
							this.getUserinfo();
							this.show2 = false;
							this.$toast("還款成功");
						} else {
							this.$toast(this.$t(res.msg));
						}
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.header {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0.2rem 0.15rem;
		background: url('../../assets/v1/hbg.png') no-repeat center/100%;
		.t {
			padding-left: 0.1rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.17rem;
			color: #fff;
		}
	}
	.page {
		.bg{
			padding: 0.5rem 0.15rem 0;
			height: 3.6rem;
			background: url('../../assets/v1/hbg.png') no-repeat center/100%;
			position: fixed;
			width: 100%;
			.jl {
				position: fixed;
				top: 0.15rem;
				right: 0.15rem;
				z-index: 999;
				color: #000000;
			}
			.xyf {
				margin: 0.4rem auto;
				width: 1.5rem;
				height: 1.5rem;
				border-radius: 50%;
				border: 0.05rem solid #549d7e;
			
				div {
					text-align: center;
				}
			
				.t {
					font-size: 0.16rem;
					font-weight: bold;
				}
			}
			.list {
				margin-top: 0.2rem;
				background: rgba(24,33,51,0.3);
				box-shadow: 0rem 0rem 0.1rem 0rem rgba(220,162,92,0.5);
				border-radius: 0.13rem;
				border: 0.02rem solid #0DCCC8;
				padding: 0.2rem .2rem .05rem .2rem;
				.tt{
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #FFFFFF;
				}
				.t{
					padding: 0.05rem 0;
					font-family: DINPro, DINPro;
					font-weight: bold;
					font-size: 0.34rem;
					color: #FFFFFF;
				}
				.t0{
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #9FA9BA;
				}
				.cont{
					margin: 0.2rem 0 0.1rem;
					width: 100%;
					padding: 0.1rem;
					background: linear-gradient( to top, rgba(68,72,82,0) 0%, rgba(255,255,255,0.46) 100%);
					border-radius: 0.13rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #fff;
					.icon{
						margin-right: 0.05rem;
					}
				}
				.btns{
					width: 100%;
				}
			}
		}
	}
	.nav{
		padding: 3.9rem 0.1rem 0.1rem;
		background-color: #F6F8FE;
		.navItem{
			width: 1rem;
			height: 0.26rem;
			background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
			border-radius: 0.13rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #FFFFFF;
		}
	}
	.ipt {
		padding: 0.2rem 0.15rem;
		background-color: #fff;
		input {
			width: 100%;
			background-color: transparent;
			border: 0.01rem solid #cecece;
			border-radius: 0.05rem;
			height: 0.44rem;
			padding: 0 0.1rem;

			&::placeholder {
				font-size: 0.14rem;
				color: #aaaaaa;
			}
		}
	}
	.b-btn{
		margin-top: .05rem;
		margin: .1rem 0;
	}
</style>