<template>
	<div class="page ">
		<!-- <top-back title="股票詳情"></top-back> -->
		<div class="header">
			<div class="flex flex-b otg">
				<div class="icon back animate__animated animate__fadeIn" @click="goBack"></div>
				<div class="flex-1 t">股票詳情</div>
				<div class="icon animate__animated animate__fadeIn" :class="is_zixuan ? 'addzx' : 'cutzx'"
					@click="addSelect(details)"></div>
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="top-data">
				<div class="top-bg">
					<div class="tp flex flex-b">
						<div class="">
							<div class="name">{{ details.local_name }}</div>
							<div class="code">{{ details.symbol }}</div>
						</div>
						<div class="flex">
							<div class="price" :class="details.gain >= 0 ? 'red' : 'green'">
								{{ $formatMoney(details.price) }}
							</div>
							<div class="per" :class="details.gain >= 0 ? 'red' : 'green'">
								<div class="flex">
									<div class="icon" :class="details.gain >= 0 ? 'up' : 'down'"></div>
									{{ Number(details.gainValue).toFixed(2) }}
								</div>
								<div>{{ Number(details.gain).toFixed(2) }}%</div>
							</div>
						</div>
					</div>
					<div class="list flex flex-b">
						<div class="flex flex-b item ">
							<div class="t1">今開</div>
							<div class="t2 red-txt">
								{{isNaN(Number(details.open).toFixed(2))? "0" : $formatMoney(Number(details.open).toFixed(2))}}
							</div>
						</div>
						<div class="flex flex-b item">
							<div class="t1">昨收</div>
							<div class="t2 green-txt">
								{{isNaN(Number(details.preClose).toFixed(2))? "0" : $formatMoney(Number(details.preClose).toFixed(2))}}
							</div>
						</div>
						<div class="flex flex-b item">
							<div class="t1">最高</div>
							<div class="t2">
								{{ $formatMoney(Number(details.high).toFixed(2)) }}
							</div>
						</div>
						<div class="flex flex-b item">
							<div class="t1">最低</div>
							<div class="t2">
								{{ $formatMoney(Number(details.high).toFixed(2)) }}
							</div>
						</div>

						<div class="flex flex-b item">
							<div class="t1">成交量</div>
							<div class="t2">{{ $formatMoney(details.volume, 0) }}</div>
						</div>
						<!-- <div class="flex flex-b item">
							<div class="t1">成交額</div>
							<div class="t2">
								{{ $formatMoney(Number(details.turnoverM).toFixed(2)) }}
							</div>
						</div> -->
					</div>
				</div>
			</div>
			<!-- 切換顯示 -->
			<div class="nav-box flex">
				<div class="nav-item" v-for="(item, index) in tabList" :key="index"
					:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
					{{ item.name }}
				</div>
			</div>

			<div class="bbg">
				<template v-if="currmentIndex == 1">
					<k-line :details="details" :symbol="symbol"></k-line>
					<div class="buy animate__animated animate__fadeIn">
						<div class="tab flex">
							<div class="tab-item flex flex-c" :class="{ active: !isLimit }" @click="setLimit(false)">市場交易</div>
							<div class="tab-item flex flex-c" :class="{ active: isLimit }" @click="setLimit(true)">限價交易</div>
						</div>
						<!-- 买多、空 -->
						<div class="items flex flex-b">
							<div class="tt flex-1">買賣方向</div>
							<div class="btns flex flex-b flex-2">
								<div class="bt" :class="{ active: buyType == 1 }" @click="changeBuyType(1)">買入</div>
								<div class="bt bt1" :class="{ active: buyType == 2 }" @click="changeBuyType(2)">賣出</div>
							</div>
						</div>
						<!-- 限价 -->
						<div class="" v-if="isLimit">
							<div class="items flex flex-b">
								<div class="tt flex-1">買入價格</div>
								<div class="flex-2">
									<van-stepper v-model="buyPrice" />
								</div>
							</div>
							<div class="items flex flex-b">
								<div class="tt flex-1">買入張數(1張=1,000股)</div>
								<div class="flex-2">
									<van-stepper v-model="buyAmount" />
								</div>
							</div>
							<!-- <div class="ipt flex flex-b">
								<div class="flex-1">
									{{ $t("sharesDetails").txt13 }}
								</div>
								<div class="flex-2">
									<van-stepper v-model="buyGang" />
								</div>
							</div> -->
						</div>
						<!-- 市价 -->
						<div class="" v-if="!isLimit">
							<!-- <div class="ipt flex flex-b">
								<div class="flex-1">
									{{ $t("sharesDetails").txt11 }}
								</div>
								<div class="flex-2">
									<van-stepper v-model="buyPrice" />
								</div>
							</div> -->
							<div class="items flex flex-b">
								<div class="tt flex-1">買入張數(1張=1,000股)</div>
								<div class="flex-2">
									<van-stepper v-model="buyAmount" />
								</div>
							</div>
							<!-- <div class="ipt flex flex-b">
								<div class="flex-1">
									{{ $t("sharesDetails").txt13 }}
								</div>
								<div class="flex-2">
									<van-stepper v-model="buyGang" />
								</div>
							</div> -->
						</div>
						<div class="">
							<div class="items flex flex-b">
								<div class="tt flex-1">數量</div>
								<div class="tt1 flex-2">{{ $formatMoney(buyGu) }}</div>
							</div>
							<div class="items flex flex-b">
								<div class="tt flex-1">市值</div>
								<div class="tt1 flex-2">{{ $formatMoney(buySz) }}</div>
							</div>
							<div class="items flex flex-b">
								<div class="tt flex-1">手續費</div>
								<div class="tt1 flex-2">{{ $formatMoney(buyFuwu) }}</div>
							</div>
							<div class="items flex flex-b">
								<div class="tt flex-1">合計</div>
								<div class="tt1 flex-2">{{ $formatMoney(buyTotal) }}</div>
							</div>
							<div class="items flex flex-b">
								<div class="tt flex-1">可用餘額</div>
								<div class="tt1 flex-2">
									{{ $formatMoney(userInfo.twd) }}
								</div>
							</div>

						</div>
					</div>
					<div class="bbtn" @click="buyStock">
						<div class="b-btn ">買入</div>
					</div>
				</template>
				<!-- 概括信息 -->
				<gkData :symbol="symbol" v-if="currmentIndex == 2" />
				<!-- 新闻列表 -->
				<newsList v-if="currmentIndex == 3" />
			</div>
		</van-pull-refresh>
		<!-- 取消顯示彈出 -->
		<van-popup v-model="show" position="center" :round="true" closeable :style="{ width: '90%' }">
			<div class="popup">
				<div class="title">
					<div class="name">{{ $t(details.name) }}</div>

					<div class="code">{{ $t(details.symbol) }}</div>
				</div>

				<div class="item flex flex-b">
					<div class="t">{{ $t("new").a59 }}</div>
					<div class="t1">
						{{ isLimit ? $formatMoney(buyPrice) : $t("sharesDetails").txt17 }}
					</div>
				</div>

				<div class="item flex flex-b">
					<div class="t">{{ $t("new").a60 }}</div>
					<div class="t1">{{ $formatMoney(buyGu) }}</div>
				</div>

				<div class="item flex flex-b">
					<div class="t">{{ $t("new").a61 }}</div>
					<div class="t1">{{ $formatMoney(buySz) }}</div>
				</div>

				<div class="item flex flex-b">
					<div class="t">{{ $t("new").a62 }}</div>
					<div class="t1">{{ $formatMoney(buyFuwu) }}</div>
				</div>

				<div class="item flex flex-b">
					<div class="t">{{ $t("new").a63 }}</div>
					<div class="t1">{{ $formatMoney(buyTotal) }}</div>
				</div>

				<div class="item flex flex-b">
					<div class="t">{{ $t("new").a64 }}</div>
					<div class="t1">{{ $formatMoney(userInfo.twd) }}</div>
				</div>

				<div class="item" @click="buyStock">
					<div class="b-btn ">{{ $t("sharesDetails").btn }}</div>
				</div>
			</div>
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	import kLine from "./components/k-line.vue";
	import gkData from "./components/gkData.vue";
	import newsList from "./components/newsList.vue";

	export default {
		name: "",
		props: {},
		data() {
			return {
				tabList: [{
						name: "行情",
						type: 1
					},
					{
						name: "概括",
						type: 2
					},
					{
						name: "資訊",
						type: 3
					},
				],
				currmentIndex: 1,
				isLoading: false,
				userInfo: {},
				show: false,
				buyPrice: "",
				buyAmount: "",
				buyGang: "",
				buyType: 1,
				isLimit: false,
				details: {},
				detailTime: null,
				symbol: "",
				market: 'taiwan', // 默认台湾市场
				is_zixuan: false,
				cfg: {},
				navList: [{
						name: this.$t("menu").href2,
						type: 0,
					},
					{
						name: this.$t("newt").t42,
						type: 1,
					},
					{
						name: this.$t("newt").t43,
						type: 2,
					},
				],
			};
		},
		components: {
			kLine,
			gkData,
			newsList
		},
		created() {
			this.symbol = this.$route.query.symbol;
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.market = 'us';
			} else {
				this.market = 'taiwan';
			}

			this.requestDetail(true);
			this.getConfig();
			this.getUserInfo();
			this.detailTime = setInterval(() => {
				this.requestDetail();
			}, 5000);
		},
		beforeDestroy() {
			!!this.detailTime && clearInterval(this.detailTime);
		},
		computed: {
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.market === 'taiwan' ? 'twd' : 'usd';
			},
			// 股数
			buyGu() {
				if (this.cfg.buytype == 1) {
					// 买入类型(1手)
					return parseInt(this.buyAmount) * 1000;
				}
				return 0;
			},
			// 市值
			buySz() {
				let scale = this.cfg.gtype == 1 ? this.buyGang : 1;
				return this.buyBj * scale;
			},
			// 本金
			buyBj() {
				//按手
				if (this.cfg.buytype == 1) {
					return this.buyGu * this.buyPrice;
				}
				//按万
				return Number(this.buyAmount) * this.buyPrice;
			},
			// 服务费
			buyFuwu() {
				let val = this.buySz * Number(this.cfg.buycharge);

				if (val < this.cfg.minshouxu) {
					return Number(this.cfg.minshouxu);
				}
				return val;
			},
			// 合计
			buyTotal() {
				return this.buyBj + this.buyFuwu;
			},
		},
		methods: {
			goBack() {
        this.$router.go(-1);
			},
			changeNav(type) {
				this.currmentIndex = type;
			},
			// 下拉刷新
			onRefresh() {
				this.currmentIndex = 1;
				this.requestDetail(true);
				this.getConfig();
				this.getUserInfo();
			},
			setLimit(type) {
				this.isLimit = type;
				if (type == 2) this.buyPrice = this.details.price; //初始价格
			},
			changeBuyType(type) {
				this.buyType = type;
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: this.apiMarketType
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});

				let arr = val.ganggang.split("/");
				this.buyGang = parseInt(arr[0]); //杠杆倍数给默认值

				this.cfg = val;
			},
			requestDetail(isInit) {
				this.$server
					.post("/trade/stockdetails", {
						symbol: this.symbol,
						type: this.apiMarketType
					})
					.then((res) => {
						this.isLoading = false; //下拉刷新状态

						this.details = res.data;

						if (isInit) {
							this.buyPrice = this.details.price; //初始价格
							this.getMine();
						}
					});
			},
			buyStock() {
				this.$refs.loading.open(); //开启加载
				//普通购买
				this.$server
					.post("/trade/buy_stock", {
						symbol: this.details.symbol,
						zhang: this.buyAmount,
						ganggan: this.buyGang,
						buyzd: this.buyType, //1 up ，2down
						buy_price: this.buyPrice,
						is_type: 0,
						// type: this.isLimit ? 2 : 1,
						type: this.apiMarketType,
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						if (res.status == 1) {
							this.show = false;
							this.$toast(this.$t(res.msg));
							setTimeout(() => {
								this.$toPage("/trade/index"); //跳转持仓
							}, 1000);
						}
					});
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: this.apiMarketType
				}).then((res) => {
					if (res.status == 1) {
						// 判断当前是否在自选列表里面
						let arr = res.data.filter(
							(item) => item.symbol == this.details.symbol
						);
						if (arr.length) this.is_zixuan = true;
					}
				});
			},
			addSelect(obj) {
				this.$refs.loading.open(); //加载

				if (!this.is_zixuan) {
					this.$server
						.post("/user/addOptional", {
							symbol: obj.symbol,
							type: this.apiMarketType
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.is_zixuan = true;
							}
						});
				} else {
					this.$server
						.post("/user/removeOptional", {
							symbol: obj.symbol,
							type: this.apiMarketType
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.is_zixuan = false;
							}
						});
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.bbg {
		margin-top: -0.2rem;
		padding-bottom: .8rem;
		.bbtn{
			position: fixed;
			left: 0;
			bottom: 0;
			z-index: 99;
			box-shadow: 0rem 0rem 0.03rem 0rem rgba(0, 0, 0, 0.16);
			width: 100%;
			padding: 0 0.1rem;
			background: #FFFFFF;
		}
	}

	.header {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0rem 0.15rem;
		background: url('../../assets/v1/hbg.png') no-repeat center/100%;
		.t {
			padding-left: 0.1rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.17rem;
			color: #FFFFFF;
		}

		.otg {
			height: 0.5rem;
		}
	}

	::v-deep .van-stepper__minus,
	::v-deep .van-stepper__plus {
		color: #000 !important;
		font-weight: bold !important;
	}

	::v-deep .van-stepper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 0.36rem;
		border-bottom: 0.01rem solid #c0c0c0;
		.van-stepper__input {
			flex: 1;
			background: transparent;
			height: 100%;
			margin: 0;
			// border-top: 0.01rem solid #c0c0c0;
			// border-bottom: 0.01rem solid #c0c0c0;
		}
	}

	::v-deep .van-stepper__minus,
	::v-deep .van-stepper__plus {
		// background: #d8d8d8;
		background: transparent;
		width: 0.2rem;
		height: 0.2rem;
		// border: 0.01rem solid #c0c0c0;
	}

	.page {
		padding: 0.5rem 0rem 0.1rem;
	}

	.popup {
		padding: 0.2rem 0.1rem;

		.title {
			margin-bottom: 0.1rem;
			text-align: center;

			.name {
				font-weight: bold;
			}

			.code {
				font-size: 0.12rem;
				font-weight: 500;
				color: #aeaeae;
			}
		}

		.item {
			padding: 0.1rem 0;

			.t {
				font-size: 0.12rem;
				color: #aeaeae;
			}

			.t1 {
				font-weight: 600;
				font-size: 0.12rem;
			}
		}

		.b-btn {
			margin: 0.1rem 0 0;
		}
	}

	.items {
		padding: 0.1rem 0;
		.tt {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #182133;
		}
		.tt1 {
			font-size: 0.14rem;
			color: #1a1a1a;
			text-align: right;
			font-family: PingFang TC, PingFang TC;
			font-weight: 500;
		}
	}
	.buy {
		padding: 0.1rem;
		margin: 0.1rem;
		background: #FFFFFF;
		box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
		border-radius: 0.07rem;
		.tab {
			margin-bottom: 0.1rem;
			.tab-item {
				height: 0.3rem;
				background: #D8E3F6;
				border-radius: 0.13rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #405476;
				padding: 0 0.2rem;
				position: relative;
				margin-right: 0.2rem;
				// &::after {
				// 	content: "";
				// 	width: 0.32rem;
				// 	height: 0.04rem;
				// 	position: absolute;
				// 	bottom: 0;
				// 	left: 50%;
				// 	transform: translateX(-50%);
				// 	background: transparent;
				// }

				&.active {
					color: #fff;
					background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
					// &::after {
					// 	background: #549d7e;
					// }
				}
			}
		}
		.ipt {
			margin: 0.1rem 0;
			padding: 0.1rem;
			background: #fff;
		}
		.btns {
			.bt {
				height: 0.34rem;
				line-height: 0.34rem;
				text-align: center;
				opacity: 0.5;
				width: 48%;
				background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
				border-radius: 0.23rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #FFFFFF;
				&.bt1 {
					background: #182336;
				}

				&.active {
					opacity: 1;
				}
			}
		}

		.b-btn {
			margin: 0.2rem 0;
		}
	}

	.nav-box {
		padding: 0 0 0.3rem;
		margin-top: -0.7rem;
		background: #F6F8FE;
		border-radius: 0.15rem 0.15rem 0rem 0rem;

		.nav-item {
			flex: 1;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #405476;
			text-align: center;
			position: relative;
			padding: 0.1rem 0.26rem;

			&::after {
				content: "";
				width: 100%;
				height: 0.03rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
				border-radius: 0.3rem;
			}

			&.active {
				font-weight: 500;
				color: #078bde;

				&::after {
					background: #078bde;
				}
			}
		}
	}

	.top-data {
		width: 100%;
		height: 2.5rem;
		background: url('../../assets/v1/hbg.png') no-repeat center/100%;
		padding: 0.15rem 0.1rem;

		.top-bg {
			background: #fff;
			padding: 0.1rem;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
		}

		.tp {
			padding-bottom: 0.1rem;
			border-bottom: 0.01rem solid #F3F3F3;

			.name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.18rem;
				color: #333333;
			}

			.code {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}

			.price {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.26rem;
			}

			.per {
				margin-left: 0.2rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.19rem;

				.icon {
					margin-right: 0.05rem;
				}
			}
		}

		.list {
			flex-wrap: wrap;
			padding-top: 0.1rem;

			.item {
				width: 48%;
				line-height: 0.24rem;

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;
				}

				.t2 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #333333;
				}
			}
		}
	}
</style>