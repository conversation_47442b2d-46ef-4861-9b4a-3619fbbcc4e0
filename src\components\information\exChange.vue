<template>
  <div class="exchange">
    <div class="headf">
      <top-back title="貨幣兌換"></top-back>
    </div>

    <div class="cot">
      <template v-if="changeIndex">
        <div class="input-box">
          <div class="mb-20">
            <div class="flex flex-b mb-20">
              <div class="title flex">
                <div class="icon usd"></div>
                USD
              </div>
              <div class="user" v-if="changeIndex">
                <span>可用餘額:</span>
                <span class="txt">{{ formatMoney(userInfo.usd) }}USD</span>
              </div>
            </div>

            <input v-model="usMoney" @input="usInput" class="ipt flex-1" placeholder="請輸入兌換金額"
              type="text" />
          </div>
        </div>

        <!-- 切换input显示顺序 -->
        <div class="flex flex-c changeIco text-center" @click="exchangeInput">
          <div class="icon qh"></div>
        </div>

        <div class="input-box">
          <div class="mb-20">
            <div class="title mb-20 flex">
              <div class="icon twd"></div>
              TWD
            </div>
            <input v-model="twdMoney" @input="twdInput" class="ipt flex-1" type="text" disabled />
          </div>
          <div class="tips">1USD = {{ hl }} TWD</div>
        </div>
      </template>

      <template v-if="!changeIndex">
        <div class="input-box">
          <div class="mb-20">
            <div class="flex flex-b mb-20">
              <div class="title flex">
                <div class="icon twd"></div>
                TWD
              </div>
              <div class="user">
                <span>可用餘額:</span>
                <span class="txt">{{ formatMoney(userInfo.twd) }}TWD</span>
              </div>
            </div>

            <input v-model="twdMoney" @input="twdInput" class="ipt flex-1" placeholder="請輸入兌換金額"
              type="text" />
          </div>
        </div>

        <!-- 切换input显示顺序 -->
        <div class="flex flex-c changeIco text-center" @click="exchangeInput">
          <div class="icon qh"></div>
        </div>

        <div class="input-box">
          <div class="mb-20">
            <div class="title mb-20 flex">
              <div class="icon usd"></div>
              USD
            </div>
            <input v-model="usMoney" @input="usInput" class="ipt flex-1" type="text" disabled />
          </div>
          <div class="tips">1TWD = {{ hl }} USD</div>
        </div>
      </template>
    </div>
    <div class="btn-big" @click="submit">確認兌換</div>
    <loading ref="loading" />
  </div>
</template>
<script>
export default {
  name: "exChange",
  data() {
    return {
      twdMoney: "",
      usMoney: "",
      inputStatus: 0, // 0: TWD输入, 1: USD输入
      changeIndex: false, // false: TWD转USD, true: USD转TWD
      fromName: "twd", // 开头
      toName: "usd", // 结尾
      hl: "", // 汇率
      userInfo: {},
    };
  },
  computed: {
    formatMoney() {
      return (num) => {
        if (!num) return "0.00";
        return parseFloat(num).toFixed(2);
      };
    },
  },
  created() {
    this.getUserInfo();
    this.getConfig();
  },
  methods: {
    async getUserInfo() {
      // 获取TWD账户信息
      const resTWD = await this.$server.post("/user/getUserinfo", {
        type: "twd"
      });
      if (resTWD.status == 1) {
        this.userInfo.twd = resTWD.data.twd || 0;
      }

      // 获取USD账户信息
      const resUSD = await this.$server.post("/user/getUserinfo", {
        type: "usd"
      });
      if (resUSD.status == 1) {
        this.userInfo.usd = resUSD.data.usd || 0;
      }
    },
    getConfig() {
      this.$server.post("/common/config", {type: 'all'}).then((res) => {
        if (res.status == 1) {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].name == this.fromName + this.toName) {
              this.hl = res.data[i].value;
            }
          }
        }
      });
    },
    exchangeInput() {
      this.changeIndex = !this.changeIndex;
      this.twdMoney = "";
      this.usMoney = "";

      // 切换汇率方向
      if (this.changeIndex) {
        // USD转TWD
        this.fromName = "usd";
        this.toName = "twd";
      } else {
        // TWD转USD
        this.fromName = "twd";
        this.toName = "usd";
      }

      // 重新获取汇率
      this.$server.post("/common/config", {type: 'all'}).then((res) => {
        if (res.status == 1) {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].name == this.fromName + this.toName) {
              this.hl = res.data[i].value;
            }
          }
        }
      });
    },
    submit() {
      if (!this.twdMoney && !this.usMoney) {
        this.$toast("請輸入兌換金額");
        return;
      }

      let params = {};
      let val;
      if (!this.inputStatus) {
        // TWD转换成USD
        val = this.twdMoney.replace(/\,/g, "");
      } else {
        // USD转换成TWD
        val = this.usMoney.replace(/\,/g, "");
      }

      params = {
        money: val,
        from: this.fromName,
        to: this.toName
      };

      this.$refs.loading.open();
      this.$server.post("/user/rotation", params).then((res) => {
        this.$refs.loading.close();
        if (res.status == 1) {
          this.getUserInfo();
          this.$toast("兌換成功");
          this.twdMoney = "";
          this.usMoney = "";
        } else {
          this.$toast(res.msg || "兌換失敗");
        }
      });
    },
    twdInput() {
      this.inputStatus = 0;
      if (!this.twdMoney) {
        this.usMoney = "";
        return;
      }
      this.changeNum();
    },
    usInput() {
      this.inputStatus = 1;
      if (!this.usMoney) {
        this.twdMoney = "";
        return;
      }
      this.changeNum();
    },
    changeNum() {
      if (!this.inputStatus) {
        // TWD转换成USD
        let val = this.twdMoney.replace(/\,/g, "");
        this.usMoney = (val * this.hl).toFixed(6);
        this.usMoney = this.moneyInput(this.usMoney);
        this.twdMoney = this.moneyInput(val);
      } else {
        // USD转换成TWD
        let val = this.usMoney.replace(/\,/g, "");
        this.twdMoney = (val * this.hl).toFixed(6);
        this.twdMoney = this.moneyInput(this.twdMoney);
        this.usMoney = this.moneyInput(val);
      }
    },
    moneyInput(num) {
      if (num < 1000) {
        return num;
      }
      let nums = num;
      let ms = nums
        .replace(/[^\d\.]/g, "")
        .replace(/(\.\d{2}).+$/, "$1")
        .replace(/^0+([1-9])/, "$1")
        .replace(/^0+$/, "0");
      let txt = ms.split(".");
      while (/\d{4}(,|$)/.test(txt[0])) {
        txt[0] = txt[0].replace(/(\d)(\d{3}(,|$))/, "$1,$2");
      }
      nums = txt[0] + (txt.length > 1 ? "." + txt[1] : "");
      return nums;
    },
  },
};
</script>

<style scoped lang="less">
.exchange {
  background: #f8f8f8;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;

  .headf {
    width: 100%;
    height: 0.47rem;
    background: #fff;
    font-weight: 500;
    font-size: .16rem;
    color: #333;
  }

  .mb-20 {
    margin-bottom: 0.2rem;
  }

  .cot {
    margin: 0 .12rem;
    align-items: center;
    position: relative;
    z-index: 300;
    padding-top: 0.2rem;

    .input-box {
      padding: .2rem 0;
      background: #ffffff;
      border-radius: 0.1rem;
      margin-bottom: 0.1rem;
      padding: 0.15rem;

      .title {
        font-weight: 600;
        font-size: .18rem;
        color: #333;
        align-items: center;

        .icon {
          margin-right: .05rem;
        }
      }

      .ipt {
        background: #f5f5f5;
        border-radius: 0.1rem;
        padding: 0.1rem;
        width: 100%;
        height: .52rem;
        color: #333;
        border: 1px solid #e0e0e0;
        font-size: 0.16rem;

        &::placeholder {
          color: #999;
          font-size: 0.14rem;
        }
      }

      .user {
        font-size: .14rem;
        color: #666;

        .txt {
          color: #078bde;
          font-weight: 600;
        }
      }

      .tips {
        font-weight: 400;
        font-size: .14rem;
        color: #F53333;
        margin-top: 0.1rem;
      }
    }

    .changeIco {
      margin: 0.1rem 0;
      cursor: pointer;
    }
  }

  .btn-big {
    margin: .3rem .12rem;
    width: calc(100% - 0.24rem);
    height: 0.5rem;
    background: linear-gradient(90deg, #078bde, #0DCCC8);
    border-radius: 0.25rem;
    color: #fff;
    font-size: 0.16rem;
    text-align: center;
    line-height: 0.5rem;
    font-weight: 600;
  }

  .text-center {
    text-align: center;
  }

  .flex {
    display: flex;
  }

  .flex-b {
    justify-content: space-between;
  }

  .flex-1 {
    flex: 1;
  }
}
</style>
