<template>
  <div class="page ">
    <top-back title="ETF"></top-back>

    <div class="nav-box flex flex-b">
      <div
        class="nav-item"
        v-for="(item, index) in typList"
        :key="index"
        :class="{ active: currmentIndex == item.id }"
        @click="changeNav(item.id)"
      >
        {{ item.name }}
      </div>
    </div>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="25" :loading="loading">
        <no-data v-if="isShow"></no-data>
        <div class="list">
          <div
            class="item "
            v-for="(item, index) in chooseList"
            :key="index"
            @click="stockDetails(item)"
          >
            <div class="flex flex-b ">
              <div class="flex  flex-2">
                <div
                  class="st"
                  :class="
                    item.changePercent.indexOf('-') > -1 ? 'green-bg' : 'red-bg'
                  "
                >
                  {{ item.changePercent.indexOf("-") > -1 ? "賣" : "買" }}
                </div>
                <div>
                  <div class="name">{{ item.symbolName }}</div>
                  <div class="code">{{ item.systexId }}</div>
                </div>
              </div>

              <div class="flex-1 t-c">
                <div class="price">{{ item.price }}</div>
                <div class="t">現價</div>
              </div>
              <div class="flex-1 t-r">
                <div
                  class="red per"
                  :class="{ green: item.changePercent.indexOf('-') > -1 }"
                >
                  {{ item.changePercent }}
                </div>
                <div class="t">漲跌幅</div>
              </div>
            </div>
            <div class="b-btn">買入</div>
          </div>
        </div>
      </van-skeleton>
    </van-pull-refresh>

    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "etf",
  data() {
    return {
      loading: true,
      isShow: false,
      isLoading: false,
      currmentIndex: "taiwan",
      stockObj: {},
      chooseList: [],
      typList: [
        { name: "台灣", id: "taiwan" },
        { name: "中國", id: "china" },
        { name: "美國", id: "us" },
        { name: "亞洲", id: "asia" },
        { name: "全球", id: "global" },
        { name: "其他", id: "others" },
      ],
      timer: null,
    };
  },
  computed: {},
  mounted() {
    this.getNew();
    this.timer = setInterval(() => {
      this.getNew();
    }, 10000);
  },
  destroyed() {
    this.timer && clearInterval(this.timer);
  },
  methods: {
    changeNav(id) {
      this.currmentIndex = id;
      this.$refs.loading.open();
      this.getNew();
    },
    // 下拉刷新
    onRefresh() {
      this.isShow = false;
      this.getNew();
    },
    getNew() {
      this.$server
        .post("/parameter/etf", { regionId: this.currmentIndex })
        .then((res) => {
          this.isLoading = false;
          this.loading = false;
          this.$refs.loading.close();

          if (res.data && res.data.list.length) {
            this.chooseList = res.data.list;
          }

          if (!this.chooseList.length) {
            this.isShow = true;
          }
        });
    },
    stockDetails(item) {
      this.$toDetail(`/market/stockDetail?symbol=${item.systexId}`, item);
    },
  },
};
</script>
<style scoped lang="less">
.page {
  padding: 1rem 0.1rem 0.1rem;
  min-height: 100vh;
  position: relative;
}

.nav-box {
  margin: 0 0 0.1rem;
  position: fixed;
  top: 0.5rem;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 999;
  .nav-item {
    padding: 0.1rem 0;
    flex: 1;
    font-size: 0.14rem;
    color: #8e8e8e;
    text-align: center;
    position: relative;
    &::after {
      content: "";
      width: 50%;
      height: 0.02rem;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background: transparent;
    }
    &.active {
      color: #549d7e;
      &::after {
        background: #549d7e;
      }
    }
  }
}

.list {
  .item {
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.02rem 0rem rgba(125, 187, 179, 0.12),
      0rem -0.02rem 0.02rem 0rem rgba(126, 187, 180, 0.12);
    border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
    padding: 0.15rem 0.1rem;
    margin-bottom: 0.15rem;
    .st {
      font-size: 0.12rem;
      color: #ffffff;
      // padding: 0.02rem;
      width: 0.18rem;
      height: 0.18rem;
      text-align: center;
      line-height: 0.18rem;
      border-radius: 0.02rem;
      margin-right: 0.1rem;
      &.red-bg {
        background-color: #ba3b3a;
      }
      &.green-bg {
        background-color: #68a03d;
      }
    }
    .name {
      font-size: 0.14rem;
      color: #000000;
    }
    .code {
      font-size: 0.12rem;
      color: #9f9fa3;
    }
    .price.per {
      font-size: 0.14rem;
      color: #0d0d0d;
    }
    .t {
      font-size: 0.12rem;
      color: #9f9fa3;
    }

    .red {
      color: #ba3b3a;
    }
    .green {
      color: #68a03d;
    }

    .b-btn {
      background: #549d7e;
      border-radius: 0.06rem;
      width: 2.46rem;
      height: 0.32rem;
      line-height: 0.32rem;
      margin: 0.15rem auto 0;
      font-size: 0.12rem;
      color: #ffffff;
    }
  }
}
</style>
