<template>
	<!-- 自选 -->
	<div class="padd">
		<div class="topHead">
			<div class="flex flex-b">
				<div class="flex">
					<div class="icon user"></div>
<!--					<div class="name">{{ userInfo.realname }}</div>-->
					<!-- {{userInfo.account}} -->
				</div>
				<!-- 市场切换选项卡 -->
				<div class="market-tabs flex flex-c">
					<div class="tab-item" :class="{ active: marketIndex === 0 }" @click="switchMarket(0)">
						台灣市場
					</div>
					<div class="tab-item" :class="{ active: marketIndex === 1 }" @click="switchMarket(1)">
						美國市場
					</div>
				</div>
				<div class="flex">
					<div class="icon ss" @click="$toPage(`/favorite/search?market=${marketIndex === 0 ? 'taiwan' : 'us'}`)"></div>
					<div class="icon xx" @click="$toPage('/information/userInfo')"></div>
				</div>
			</div>
			
		</div>
		<van-skeleton title :row="26" :loading="loading">
			<!-- 指數顯示 -->
			<div class="zx">
				<div class="zx-cot">
					<div class="index">
						<div class="nums flex">
							<div class="nums-item" :class="{ center: i == 1 }" v-for="(item, i) in indexList" :key="i"
								@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}&market=${marketIndex === 0 ? 'taiwan' : 'us'}`, item)">
								<div class="flex">
									<div class="icon" :class="item.icon"></div>
									<div class="name">{{ item.symbol }}</div>
								</div>
								<div class="flex flex-b">
									<div class="t">{{ $formatMoney(item.price, 2) }}</div>
									<img src="../../assets/v2/greenLine.png" alt="" v-if="item.gain < 0" style="width: 0.2rem;height: 0.08rem;"/>
									<img src="../../assets/v2/redLine.png" alt="" v-else style="width: 0.2rem;height: 0.08rem;" />
								</div>
								<div class="t1 red flex flex-e" :class="{ 'green': item.gain < 0 }">
									<div class="icon animate__animated animate__fadeIn" :class="item.gain < 0 ? 'down1' : 'up1'" style="margin-right: 0.05rem;"></div>
									{{ item.gain <= 0 ? "" : "+" }}{{ $formatMoney(item.gainValue, 2) }}
									{{ item.gain <= 0 ? "" : "+" }}{{ item.gain }}%
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="bg">
				<div class="title flex flex-b">
					<div>自選列表</div>
					<div class="editBox flex flex-c" v-if="chooseList.length">
						<div class="flex tt" v-if="!show" @click="changeList">
							<div class="icon bjbtn animate__animated animate__fadeIn"></div>編輯</div>
						<div class="tt" v-if="show" @click="cancle">取消</div>
					</div>
				</div>
				<div class="">
					<no-data v-if="isShow"></no-data>
					<div class="rm-list" v-if="chooseList.length">
						<!-- <div class="flex flex-b titles">
							<div class="flex-1">名稱</div>
							<div class="flex-1 t-c">價格</div>
							<div class="flex-1 t-r flex flex-e" @click="changeListup">
								漲跌
								<div class="icon" :class="isUp ? 'zq' : 'dq'"></div>
							</div>
						</div> -->
						<div class="rm-item flex flex-b" v-for="(item, index) in chooseList" :key="index" @click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
							<div class=" flex-1 flex">
								<div v-if="item.showIcon" class="icon wxz animate__animated animate__fadeIn"
									:class="{ xz: item.choose }" @click.stop="changeItem(index)"></div>
								<div>
									<div class="name">{{ item.name || "-" }}</div>
									<div class="code">{{ item.symbol || "-" }}</div>
								</div>
							</div>
							<div class=" price t-r flex-1 red" :class="{green: Number(item.gain) < 0,}">
								{{ $formatMoney(item.price) || 0 }}
							</div>
							<div class="flex-1 t-r">
								<div class="flex flex-e per red" :class="{green: Number(item.gain) < 0,}">
									<div class="icon down animate__animated animate__fadeIn" :class="{up: Number(item.gain) > 0,}"></div>
									{{ $formatMoney(item.gainValue) || 0 }}
								</div>
								<div class="per red" :class="{green: Number(item.gain) < 0,}">
									{{ item.gain || 0 }}%
								</div>
							</div>
						</div>
					</div>
					<div class="btns flex flex-b" v-if="show">
						<div class="btn flex" @click="chooseAll">
							<div class="icon " :class="chooseShow ? 'xz' : 'wxz'"></div>
							全選
						</div>
						<div class="btn btn1" @click="delItem">刪除 ({{ chooseLength }})</div>
					</div>
					<div class="btn-box" v-if="!show">
						<div class="bt flex flex-c" @click="$toPage('/favorite/search')">
							<!-- <div class="icon zxadd"></div> -->
							添加自選
						</div>
					</div>
				</div>
			</div>
		</van-skeleton>
		<tab-bar :current="3"></tab-bar>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "favorite",
		props: {},
		data() {
			return {
				marketIndex: 0, // 0: 台湾, 1: 美国
				isUp: true, //默认涨幅在前
				list: [],
				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,
				show: false,
				chooseList: [],
				lastSymbol: "",
				chooseShow: false,
				chooseLength: 0,
				indexList: [],
				userInfo: {},
			};
		},
		computed: {
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.marketIndex === 0 ? 'twd' : 'usd';
			}
		},
		created() {
			this.getMine();
		},
		mounted() {
			// this.$refs.firstLoading.open();
			this.getIndexList();
			this.getTotalAssets()
		},
		methods: {
			// 切换市场
			switchMarket(index) {
				this.marketIndex = index;
				this.getIndexList();
				this.getMine();
				this.getTotalAssets();
			},
			onRefresh() {
				this.isShow = false;
				this.getMine();
				this.getIndexList();
			},
			getIndexList() {
				this.$server
					.post("/parameter/zhishu", {
						type: this.apiMarketType
					})
					.then((res) => {
						if (res && res.data) {
							this.indexList = res.data;
							// /parameter/zhishu 返回数据字段：symbol, price, gain, gainValue, icon
						}
					});
			},
			// 获取总资产
			async getTotalAssets() {
				const res = await this.$server.post("/user/getUserinfo", {
					type: this.apiMarketType
				});
				if (res.status == 1) {
					this.userInfo = res.data;
				}
			},
			changeList() {
				if (!this.chooseList.length) return;
				this.chooseList.forEach((item) => {
					item.showIcon = true;
				});
				this.show = true;
			},
			cancle() {
				this.chooseList.forEach((item) => {
					item.showIcon = false;
				});
				this.show = false;
			},
			changeItem(index) {
				this.chooseList.forEach((item, i) => {
					if (index == i) {
						item.choose = !item.choose;
					}
				});
				// 單獨選擇，更新數量
				let arr = this.chooseList.filter((item) => item.choose);
				this.chooseLength = arr.length;

				if (this.chooseLength == this.chooseList.length) {
					this.chooseShow = true;
				} else {
					this.chooseShow = false;
				}
			},
			chooseAll() {
				// 全選
				this.chooseShow = !this.chooseShow;
				this.chooseList.forEach((item) => {
					item.choose = this.chooseShow;
				});

				// 顯示選中數量
				if (this.chooseShow) {
					this.chooseLength = this.chooseList.length;
				} else {
					this.chooseLength = 0;
				}
			},
			delItem() {
				let arr = this.chooseList.filter((item) => item.choose);

				if (arr.length) {
					this.lastSymbol = arr[arr.length - 1].systexId; //记录已选中最后一项的值
					// console.log("lastSymbol", this.lastSymbol);
					this.$refs.loading.open(); //开启加载
					arr.forEach((item) => {
						this.removeOptional(item);
					});
				}
			},
			changeListup() {
				this.isUp = !this.isUp;
				// 漲跌 排序
				if (this.isUp) {
					this.chooseList = this.chooseList.sort(
						(a, b) => Number(b.gainValue) - Number(a.gainValue)
					);
				} else {
					this.chooseList = this.chooseList.sort(
						(a, b) => Number(a.gainValue) - Number(b.gainValue)
					);
				}
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: this.apiMarketType
				}).then((res) => {
					this.isLoading = false; //下拉刷新状态
					// this.$refs.firstLoading.close(); //关闭初始加载的效果
					this.loading = false;

					if (res.status == 1) {
						let arr = res.data;
						if (this.show) {
							// 如果是已经选择删除，但是列表还有数据
							arr.forEach((item) => {
								item.showIcon = true;
								item.choose = false;
							});
						} else {
							arr.forEach((item) => {
								item.showIcon = false;
								item.choose = false;
							});
						}
						this.chooseList = arr;
						this.changeListup(); //重新加载排序，涨在前
						if (!arr.length) {
							this.show = false;
							this.isShow = true;
						}
						// console.log("this.chooseList ", this.chooseList);
					}
				});
			},
			removeOptional(item) {
				this.$server
					.post("/user/removeOptional", {
						symbol: item.symbol,
						type: this.apiMarketType
					})
					.then((res) => {
						if (res.status == 1) {
							if (this.lastSymbol == item.systexId) {
								this.$refs.loading.close(); //删除最后一项成功，结束加载中
								this.getMine();
							}
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.padd {
		padding: 0 0 0.5rem;
		.topHead{
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 2.5rem;
			background: url('../../assets/v1/hbg.png') no-repeat center/100%;
			padding:0.3rem 0.16rem;
			.name{
				margin-left:0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}
			.xx{
				margin-left:0.16rem;
			}

			.market-tabs {
				.tab-item {
					padding: 0.05rem 0.15rem;
					margin: 0 0.05rem;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 0.04rem;
					color: #ffffff;
					font-size: 0.12rem;
					cursor: pointer;
					transition: all 0.3s;

					&.active {
						background: #0DCCC8;
						color: #ffffff;
					}
				}
			}
		}
		.zx{
			position: fixed;
			top: 2.5rem;
			left: 0;
			.zx-cot {
				margin-top: -1.8rem;
				background-color: rgba(5, 8, 13, 0.2);

				.index {
					overflow-x: scroll;
					padding: 0 0.15rem;

					.nums {
						padding: 0.1rem 0;
						width: 580%;
						.nums-item {
							width: auto;
							margin-right: 0.08rem;
							padding: 0.08rem 0.08rem;
							background: #FFFFFF;
							border-radius: 0.13rem;
							.name {
								font-family: PingFangSC, PingFang SC;
								font-weight: 500;
								font-size: 0.08rem;
								color: #0C061C;
							}

							.t {
								font-family: OPPOSans, OPPOSans;
								font-weight: normal;
								padding: 0;
								font-size: 0.1rem;
								color: #0C061C;
								margin: 0;
							}

							.t1 {
								font-family: OPPOSans, OPPOSans;
								font-weight: normal;
								font-size: 0.15rem;
							}
						}
					}
				}
			}
		}

	}
	.bg{
		position: fixed;
		z-index: 22;
		width: 100%;
		height: 100vh;
		margin-top: 1.4rem;
		background: #F6F8FE;
		border-radius: 0.15rem 0.15rem 0rem 0rem;
		padding:0.1rem 0.1rem 2rem;
		overflow: auto;
		.title {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.2rem;
			color: #182133;
			.editBox{
				height: 0.26rem;
				background: #FFFFFF;
				box-shadow: 0rem 0rem 0.08rem 0rem rgba(0,0,0,0.25);
				border-radius: 0.13rem;
				padding: 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #405476;
			}
			.tt {
				font-size: 0.12rem;
				color: #231f20;
			}
			.bjbtn {
				margin-right: 0.05rem;
			}
		}
	}
	.rm-list {
		padding-bottom: 1.5rem;
		.titles {
			margin: 0.1rem 0 0;
			div {
				font-size: 0.12rem;
				color: #787878;
			}

			.icon {
				margin-left: 0.05rem;
			}
		}
		.rm-item {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #EAEDF8;
			.wxz {
				margin-right: 0.1rem;
			}
			div {
				font-size: 0.12rem;
			}
			.name {
				font-weight: bold;
				font-family: PingFangSC, PingFang SC;
				font-size: 0.15rem;
				color: #333333;
			}

			.code {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
				line-height: 0.11rem;
			}
			.price{
				font-weight: bold;
				font-family: PingFangSC, PingFang SC;
				font-size: 0.15rem;
				color: #333333;
				.icon {
					margin-left: 0.05rem;
				}
			}
			.per {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
			}
		}
	}
	.btns {
		background: #ffffff;
		padding: 0.15rem;
		width: 100%;
		position: fixed;
		bottom: 0.5rem;
		left: 0;
		z-index: 999;

		.btn {
			.icon {
				margin-right: 0.05rem;
			}

			&.btn1 {
				font-size: 0.14rem;
				color: #078bde;
			}
		}
	}
	.btn-box {
		padding: 0.15rem 0 0.2rem;
		.bt {
			width: 1.21rem;
			height: 0.29rem;
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
			border: 0.01rem solid #078bde;
			margin: 0 auto;
			font-size: 0.12rem;
			color: #078bde;
			.icon {
				margin-right: 0.05rem;
			}

		}
	}
	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.nums {
		text-align: center;
		padding: 0.1rem 0;
		// border-top: 0.01rem solid #f5f5f5;
		border-bottom: 0.01rem solid #f5f5f5;

		.nums-item {
			width: 32%;

			&.center {
				border-left: 0.02rem solid #bbc5c1;
				border-right: 0.02rem solid #bbc5c1;
			}

			.name {
				font-weight: 600;
				font-size: 0.12rem;
				margin-bottom: 0.05rem;
			}

			.icon {
				margin-left: 0.05rem;
			}

			.t {
				font-weight: 500;
				font-size: 0.16rem;
				color: #c5585e;
				margin: 0.1rem 0;

				&.die {
					color: #4f8672;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #c5585e;

				&.die {
					color: #4f8672;
				}
			}
		}
	}
	.header {
		width: 100vw;
		height: 0.5rem;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		background: #fff;
		padding: 0 0.1rem;

		.t {
			font-weight: 500;
			font-size: 0.18rem;
			color: #000000;
			text-align: center;
			line-height: 0.5rem;
		}

		.sou2 {
			margin-right: 0.1rem;
		}
	}
	.cot {
		.list {
			.titles {
				padding: 0.1rem;
				border-bottom: 0.01rem solid #f5f5f5;

				div {
					font-size: 0.12rem;
					color: #757575;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}

			.list-item {
				padding: 0.1rem;
				// border-bottom: 0.02rem solid #ececec;
				border-bottom: 0.01rem solid #f5f5f5;

				.wxz {
					margin-right: 0.05rem;
				}

				.name {
					font-weight: bold;
					// margin-left: 0.05rem;
				}

				.code {
					font-weight: 500;
					font-size: 0.12rem;
					color: #464646;
					// margin-left: 0.05rem;
				}

				.price {
					font-weight: bold;

					// font-size: 0.18rem;
					.icon {
						margin-left: 0.05rem;
					}
				}

				.per {
					font-weight: bold;
					// font-size: 0.12rem;
				}
			}
		}
	}
</style>