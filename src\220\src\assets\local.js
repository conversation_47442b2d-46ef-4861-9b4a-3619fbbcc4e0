let en = require("./lang/en")
let zh = require("./lang/zh")
let tr = require("./lang/tr")


let didnot = [];

let lang = localStorage.getItem('language') || 'tr';
if(lang!='tr'&&lang!='en'&&lang!='cn'){
	lang = 'tr';
	localStorage.setItem('language',lang);
}

export function initLang(Vue) {
    function containsChinese(str) {
        const regex = /[\u4e00-\u9fff]/;
        return regex.test(str);
    }
    Vue.prototype.$t = (key) => {
        if (!key) {
            return '';
        }
        if (!localStorage.getItem('language')) {
            if (tr[key]) {
                key = tr[key]
            }
        }
        if (localStorage.getItem('language') === 'en') {
            if (en[key]) {
                key = en[key]
            }
        }
		if (localStorage.getItem('language') === 'tr') {
		    if (tr[key]) {
		        key = tr[key]
		    }
		}
        if (localStorage.getItem('language') === 'cn') {
			if (zh[key]) {
				key = zh[key]
			}
        }
        if (localStorage.getItem('language') == 'en' || localStorage.getItem('language') == 'tr' || !localStorage.getItem('language')) {
            if(containsChinese(key)){
                if(localStorage.getItem('language') == 'tr' || !localStorage.getItem('language')){
                    key = "yanlış"
                }
                if(localStorage.getItem('language') == 'en'){
                    key = "error"
                }
            }
        }
        return key;
    }

    let findObj = (obj, lv) => {
        for (let key in obj) {
            if (typeof obj[key] == 'object') {
                findObj(obj[key], lv + 1)
            } else if (lv > 0 && typeof obj[key] == 'string') {
                if (!tw[obj[key]]) {
                    didnot.push(obj[key])
                }
            }
        }
    }

}
