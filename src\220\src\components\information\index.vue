<template>
	<!-- 个人 -->
	<div class="page ">
		<top-back :title="$t('menu').href5"></top-back>
		<div class="language">
			<img src="../../assets/mine/lang.png" @click="$toPage('/information/changeLang')" style="width: 0.2rem;height: 0.2rem;" />
		</div>
		<!-- <div class="top">
			<div class="header flex flex-b">
				{{$t('menu').href5}}
				<img src="../../assets/mine/lang.png" @click="$toPage('/information/changeLang')" />
			</div>
			<div class="user flex">
				<img src="../../assets/mine/user.png" />
				{{userInfo.account}}
				<img src="../../assets/mine/copy.png" class="copy" @click="copyTxt(userInfo.account)" />
			</div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<!-- <div class="top">
				<div class="flex">
					<div class="headImg">
						<img :src="$cfg.logo" />
					</div>
					<div class="name">{{ userInfo.realname || userInfo.account }}</div>
					<div class="flex" v-if="false">
						<div class="icon set animate__animated animate__fadeIn" @click="$toPage('/information/setting')"></div>
					</div>
				</div>
			</div> -->

			<div class="cot">
				<div class="money">
					<div class="tops flex flex-b">
						<div>
							<div class="t flex" @click="show = !show">
								{{ $t("mine").txt1 }}
								<div class="icon animate__animated animate__fadeIn" :class="show ? 'zy' : 'by'"></div>
							</div>
							<div class="num">{{ show ? $formatMoney(totalAssets) || 0 : "****" }}</div>
						</div>
						<div class="flex-column-item" @click="$toPage('/information/setting')">
							<div class="icon user"></div>
							<div class="name">{{ userInfo.realname || userInfo.account }}</div>
						</div>
					</div>
					<div class="nums flex flex-b flex-wrap">
						<div class="item">
							<div class="t2">
								<span class="c1 animate__animated animate__fadeIn"> </span>{{ $t("mine").txt2 }}
							</div>
							<div class="t1">
								{{ show ? $formatMoney(userInfo.try) || 0 : "****" }}
							</div>
						</div>
						<div class="item">
							<div class="t2">
								<span class="c2 animate__animated animate__fadeIn"> </span>{{ $t("mine").txt3 }}
							</div>
							<div class="t1">
								{{ show ? $formatMoney(totalProfit) || 0 : "****" }}
							</div>
						</div>
						<div class="item">
							<div class="t2">
								<span class="c3 animate__animated animate__fadeIn"> </span>{{ $t("mine").txt4 }}
							</div>
							<div class="t1">
								{{ show ? $formatMoney(freezeAssets) || 0 : "****" }}
							</div>
						</div>
						<div class="item">
							<div class="t2">{{ $t("new").a36 }} (USD)</div>
							<div class="t1">{{ $formatMoney2(userInfo.usd) }}</div>
						</div>
					</div>
				</div>
				<div class="btns flex flex-b">
					<div class="btn1" @click="$toPage('/information/recharge')">{{$t("new").b}}</div>
					<div class="btn2" @click="$toPage('/information/cashOut')">{{$t("new").a23}}</div>
				</div>
				<div class="func">
					<div class="tab">
						<!-- <div class="title">{{$t('mine').txt9}}</div> -->
						<div class="tab-item flex flex-b" v-for="(item, i) in tabList" :key="i" @click="goUrl(item.url)">
							<div class="flex">
								<img :src="item.icon" />
								<div>{{ item.name }}</div>
								<div v-if="item.note">
									<span class="txt1" v-if="userInfo.is_true==1">{{$t('new').txt30}}</span>
									<span v-else-if="userInfo.is_true==3">{{$t('new').txt32}}</span>
									<span v-else>{{$t('new').txt31}}</span>
								</div>
							</div>
							<div class="icon jt1"></div>
						</div>
					</div>
					<div class="quit flex flex-c" @click="exit">
						<img src="../../assets/mine/quit.png" />
						{{$t("setting").btn}}
					</div>
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />

		<!-- <tab-bar :current="4"></tab-bar> -->
	</div>
</template>

<script>
	//import * as echarts from "echarts";
	export default {
		name: "information",
		props: {},
		data() {
			return {
				chartData: [],
				show: true,
				loading: true,
				isLoading: false,
				kfUrl: "",
				userInfo: {},
				currentIndex: 0,
				funcList: [{
						name: this.$t("new").b,
						icon: "m1",
						url: "/information/recharge",
					},
					{
						name: this.$t("new").a23,
						icon: "m2",
						url: "/information/cashOut",
					},
				],
				tabList: [
					/* {
						name: this.$t("mine").menu3,
						icon: "m3",
						url: "/information/bankList",
					},
					{
						name: this.$t("mine").menu5,
						icon: "m4",
						url: "/information/fundRecord",
					}, */
					{
						name: this.$t("mine").menu3,
						icon: require('../../assets/mine/fast1.png'),
						url: "/information/bankList",
					},
					{
						name: this.$t("mine").menu5,
						icon: require('../../assets/mine/fast2.png'),
						url: "/information/fundRecord",
					},
					{
						name: this.$t("exchange").title,
						icon: require('../../assets/mine/fast3.png'),
						url: "/information/exChange",
					},
					{
						name: this.$t("mine").menu10,
						icon: require('../../assets/mine/fast4.png'),
						url: "kefu",
					},
					{
						name: this.$t('setting').title,
						icon: require('../../assets/mine/fast5.png'),
						url: "/information/setting",
					},
					{
						name: this.$t("mine").menu4,
						icon: require('../../assets/mine/fast6.png'),
						url: "/information/authInfo",
						note: true
						//   url: "authInfo",
					},
					/* {
						name: this.$t("setting").txt3,
						icon: require('../../assets/mine/ico2.png'),
						url: "/information/loginPass",
					}, */
					/* {
						name: this.$t("setting").txt4,
						icon: require('../../assets/mine/ico3.png'),
						url: "/information/fundPass",
					}, */

					/* {
						name: this.$t("关于我们"),
						icon: require('../../assets/mine/ico5.png'),
						url: "/information/aboutUs",
					}, */
					/* {
						name: this.$t("new").a48,
						icon: require('../../assets/mine/ico7.png'),
						url: "/information/userInfo",
					}, */
					/* {
						name: this.$t("mine").menu9,
						icon: require('../../assets/mine/ico6.png'),
						url: "/information/changeLang",
					}, */


					/* {
						name: this.$t("setting").btn,
						icon: require('../../assets/mine/ico8.png'),
						url: "exit",
					}, */
					// {
					//   name: this.$t("exchange").title,
					//   icon: "m4",
					//   url: "/information/exChange",
					// },
				],
				totalProfit: 0,
				totalAssets: 0,
				freezeAssets: 0,
				myChart: null,
			};
		},
		computed: {},
		mounted() {
			// this.getConfig();
			//this.getTotalProfit();
			this.getTotalAssets();
		},
		methods: {
			goAuthInfo() {
				if (this.userInfo.is_true == 1) {
					this.$toast(this.$t("new").tt);
				} else {
					this.$toPage("/information/authInfo");
				}
			},
			goUrl(url) {
				if (url == "kefu") {
					this.goKefu();
				} else if (url == "authInfo") {
					if (this.userInfo.is_true == 1) {
						this.$toast(this.$t("new").tt);
					} else {
						this.$toPage("/information/authInfo");
					}
				} else if (url == 'exit') {
					this.exit();
				} else {
					this.$toPage(url);
				}
			},
			exit() {
				this.$storage.remove("tokend");
				this.$refs.loading.open();
				setTimeout(() => {
					this.$refs.loading.close();
					this.$toPage("/login/login");
				}, 1000);
			},
			showCzTips() {
				this.$toast(this.$t("czts"));
			},
			// 下拉刷新
			onRefresh() {
				this.isLoading = false;
				// this.getConfig();
				//this.getTotalProfit();
				//this.getTotalAssets();
			},
			goKefu() {
				this.getConfig();
			},
			//盈利資金
			async getTotalProfit() {
				const res = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let fdyk = 0;
				if (res.status == 1) {
					fdyk = Number(res.data.fdyk);
				}
				this.totalProfit = fdyk;
			},
			// 获取总资产
			async getTotalAssets() {
				//this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo");
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let krw = Number(res.data.try || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: this.$stockType,
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: this.$stockType,
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 日内交易的申请冻结 接口报错
				const res3 = await this.$server.post("/trade/urnjylist", {
					type: this.$stockType,
				});
				let dayDelFreeze = 0;
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					res3.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit));
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDelFreeze = total;
				}

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: this.$stockType,
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 日内交易持仓
				const res6 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					buy_type: 1,
				});
				let dayDealFreeze = 0;
				delete res6.data.ccsz;
				delete res6.data.fdyk;
				let dataArr1 = res6.data;
				if (dataArr1.length) {
					let arr = [];
					dataArr1.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					followingFreeze +
					dayDelFreeze +
					bigDealFreeze +
					positionFreeze +
					dayDealFreeze;
				// 总资产
				this.totalAssets = krw + this.freezeAssets;
				this.$refs.loading.close();

				this.chartData = [
					// {
					//   value: this.totalAssets || 0,
					//   name: "",
					// },
					{
						value: krw,
						name: "",
					},
					{
						value: this.totalProfit || 0,
						name: "",
					},
					{
						value: this.freezeAssets || 0,
						name: "",
					},
				];
				//   console.log("this.chartData", this.chartData);
				//   this.getEcharts();
			},

			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				// this.kfUrl = val.kefu;
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			getEcharts() {
				let that = this;
				if (that.myChart !== null) {
					echarts.dispose(that.myChart);
				}

				let chartDom = document.getElementById("main");
				that.myChart = echarts.init(chartDom);
				let option;
				option = {
					// color: ["#C5585E", "#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
					color: ["#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
					tooltip: {
						trigger: "item",
					},
					// 頂部圖例
					legend: {
						top: "5%",
						left: "center",
						show: false,
					},
					series: [{
						name: "",
						type: "pie",
						// radius: ['40%', '70%'], //圆环
						radius: "100%",
						center: ["50%", "50%"],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: "center",
						},
						emphasis: {
							label: {
								show: true,
								fontSize: "40",
								fontWeight: "bold",
							},
						},
						labelLine: {
							show: false,
						},
						data: this.chartData,
					}, ],
				};

				option && that.myChart.setOption(option);
			},
			copyTxt(text) {
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select(); // 选中文本内容
				textarea.setSelectionRange(0, text.length); // 设置选定区的开始和结束点
				this.$toast(this.$t("capitalChannel").tit3);
				var result = document.execCommand("copy"); //将当前选中区复制到剪贴板
				textarea.remove();
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: .5rem 0 .8rem;
		min-height: 100vh;
	}
	.language{
		position: absolute;
		top: 0.16rem;
		right: 0.2rem;
		z-index: 999;
	}
	.top {
		.header {
			background: #f0f2f5;
			font-weight: 500;
			font-size: .18rem;
			color: #001A0B;

			img {
				width: .2rem;
			}
		}

		.user {
			font-weight: 500;
			font-size: .18rem;
			color: #001A0B;
			margin: 0 .13rem;
			position: relative;
			z-index: 888;

			img {
				height: .64rem;
				margin-right: .1rem;
			}

			.copy {
				width: .14rem;
				height: .14rem;
				margin-left: .1rem;
			}
		}
	}

	/* .top {
		padding: 0.2rem 0.16rem;
		color:#fff;font-size: .16rem;
		.headImg{
			width: .44rem;
			height: .44rem;
			background: #FFFFFF;
			border-radius: .04rem;
			border: .01rem solid #8A8A8A;
			margin-right:.15rem;
			img{
				width:100%;height: 100%;
			}
		}
		.set {
			margin-left: 0.05rem;
		}
	
		.user-info {
			padding: 0.15rem 0.1rem;
			background: #6970af;
	
			.user {
				margin: 0 0.05rem 0 0;
			}
	
			.name {
				font-weight: 500;
				font-size: 0.16rem;
				color: #333;
				font-family: PingFang TC, PingFang TC;
			}
	
			.account {
				font-weight: 600;
				color: #333;
			}
	
			.rz-btn {
				.bt {
					background: #eceefe;
					border-radius: 0.04rem;
					padding: 0.05rem 0.1rem;
					font-weight: 500;
					color: #777779;
					margin-right: 0.05rem;
				}
			}
		}
	} */

	#main {
		width: 0.66rem;
		height: 0.66rem;
		border-radius: 50%;
	}

	.cot {
		.money {
			margin: 0.12rem auto;
			width: 3.51rem;
			height: 2.15rem;
			background: url('../../assets/v2/moneyBg.png') no-repeat center/100%;
			padding: 0.16rem 0.2rem;
			.tops {
				margin-bottom: 0.1rem;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #666666;
					.icon {
						margin-left: 0.05rem;
					}
				}

				.num {
					margin-top: 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.24rem;
					color: #E10414;
				}
				.name{
					margin-top: 0.05rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #999999;
				}
			}

			.nums {
				padding: .12rem 0;
				.item {
					width: 50%;
					text-align: center;
					padding: 0.05rem 0;
					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.19rem;
						color: #333333;
					}

					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #999999;
					}
				}
			}
		}
		.btns {
			padding: 0.05rem 0.12rem;
			.btn1 {
				width: 50%;
				height: 0.44rem;
				line-height: 0.44rem;
				background: #E10414;
				border-radius: 0.5rem 0rem 0rem 0.5rem;
				text-align: center;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #FFFFFF;
				clip-path: polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%);
			}
			.btn2{
				clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
				width: 50%;
				height: 0.44rem;
				line-height: 0.44rem;
				background: #FFFFFF;
				border-radius: 0rem 0.5rem 0.5rem 0rem;
				text-align: center;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #000;
			}
		}
		.ts {
			font-weight: bold;
			font-size: 0.16rem;
			color: #333333;
			padding: 0.2rem 0;
		}

		.func {
			padding: .12rem;
			.tab {
				padding: 0 .12rem;
				overflow: hidden;
				background: #FFFFFF;
				border-radius: 0.13rem;
				margin-bottom: 0.1rem;
				.title {
					font-weight: 500;
					font-size: .16rem;
					color: #001A0B;
				}
				.tab-item {
					padding: 0.16rem 0;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #323233;
					border-bottom: 0.01rem solid #E6E6E6;
					&:last-child{
						border-bottom: none;
					}
					img {
						width: .2rem;
						margin-right: 0.1rem;
					}
					span {
						display: inline-block;
						font-weight: 400;
						font-size: .1rem;
						color: #6F7274;
						background: #EEEEEE;
						border-radius: .02rem;
						padding: .02rem .06rem;

						&.txt1 {
							background: #FFF7EB;
							color: #EF9D1C;
						}
					}
				}
			}

			.quit {
				height: .42rem;
				background: #FFFFFF;
				border-radius: .21rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #999999;

				img {
					width: .2rem;
					margin-right: .1rem;
				}
			}
		}

	}
</style>