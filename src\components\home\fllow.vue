<template>
	<div class="page ">
		<!-- <top-back title="量化跟單"></top-back> -->
		<top-back title="傳承基金"></top-back>

		<!-- 图 -->
		<!--   <div class="chart"></div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="25" :loading="loading">
				<no-data v-if="isShow"></no-data>
				<div class="list" v-if="chooseList.length">
					<div class="item" v-for="(item, index) in chooseList" :key="index">
						<!-- <div class="icon ic animate__animated animate__fadeIn"
							:class="(index + 1) % 2 == 0 ? 'gd1' : 'gd0'"></div> -->
							<div class="icon ic animate__animated animate__fadeIn"
							:class="index == 0 ? 'gd1' :index==1? 'gd0':'gd2'"></div>
						<div class="info">
							<div class="">
								<div class="name">{{ item.name }}</div>
								<div class="code">發單人: {{ item.sender }}</div>
							</div>
							<div class="flex flex-b middle">
								<div class="flex-column-item">
									<div class="t02">資金規模</div>
									<div class="per">{{ $formatMoney(item.capital) }}</div>
								</div>
								<div class="flex-column-item">
									<div class="t02">預計盈利</div>
									<div class="per flex flex-e"
										:class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
										{{ item.dprofit }}-{{ item.yprofit }}
										<div class="icon animate__animated animate__fadeIn"
											:class="item.dprofit.indexOf('-') > -1 ? 'down' : 'up'"></div>
									</div>
								</div>
							</div>
							<div class="">
								<div v-if="item.state == -1" class="s-btn bt">
									已終止
								</div>
								<div v-else class="s-btn" @click="stockDetails(item)">
									買入
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<van-popup v-model="show" round position="center" :style="{ width: '90%' }">
			<div class="pop">
				<!-- <div class="pop-title">{{ stockObj.name }}</div> -->
				<div class="pop-price t-c">
					<div class="t1">{{ stockObj.name }}</div>
					<div class="t">
						資金規模：<span>{{ $formatMoney(stockObj.capital) }}</span>
					</div>
				</div>
				<div class="pad">
					<div class="ipt ">
						<div class="tt">買入金額</div>
						<input class="flex-1" v-model="buyObj.handle" type="number" placeholder="請輸入買入金額" />

<!--						<div class="flex">-->
<!--							<div class="t1">最低投資</div>-->
<!--							<div class="t2">-->
<!--								{{ $formatMoney(stockObj.money) }}-->
<!--							</div>-->
<!--						</div>-->
					</div>
					<div class="flex flex-b">
						<!-- <div class="b-btn bt" @click="show = false">
							{{ $t("取消") }}
						</div> -->
						<div @click="buyFn" class="b-btn">買入</div>
					</div>
				</div>
			</div>
			<div class="icon close animate__animated animate__fadeIn" @click="show = false"></div>
		</van-popup>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "fllow",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: 0,
				stockObj: {},
				chooseList: [
					// {
					// 	name: "名称",
					// 	sender: "发单人",
					// 	capital: 100000,
					// 	dprofit: "-1%",
					// 	yprofit: "10%",
					// 	money: 1000,
					// },
					// {
					// 	name: "名称",
					// 	sender: "发单人",
					// 	capital: 100000,
					// 	dprofit: "1%",
					// 	yprofit: "10%",
					// 	money: 1000,
					// },
				],
				show: false,
				buyObj: {
					handle: "",
				},
			};
		},
		computed: {},
		mounted() {
			this.getNew();
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getNew();
			},
			getNew() {
				this.$server.post("/trade/productlist", {
					type: "twd"
				}).then((res) => {
					this.isLoading = false;
					this.loading = false;

					res.data.forEach((vo) => {
						let now = Date.now() * 0.001;
						vo.state = now > vo.end ? -1 : now > vo.start ? 1 : 0;
					});
					this.chooseList = res.data;

					if (!this.chooseList.length) {
						this.isShow = true;
					}
				});
			},
			stockDetails(stock) {
				this.stockObj = stock;
				this.show = true;
			},
			buyFn() {
				if (this.buyObj.handle < Number(this.stockObj.money)) {
					this.$toast("最低投資" + this.stockObj.money);
					return;
				}
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/buy_product ", {
						id: this.stockObj.id,
						money: this.buyObj.handle,
						type: "twd",
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						this.show = false;
						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
						}
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	.list {
		padding: 0.1rem;

		.item {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0rem 0rem 0.07rem 0.07rem;
			margin-bottom: 0.25rem;
			position: relative;

			.info {
				padding: 0.1rem;

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #182133;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #69717D;
				}

				.middle {
					background: #F6F8FE;
					border-radius: 0.06rem;
					padding: 0.15rem;

					.per {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.14rem;
						color: #000000;

						.icon {
							margin-left: 0.05rem;
						}
					}

					.t02 {
						margin-bottom: 0.1rem;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}
				}
			}

			.ic {
				width: 100%;
				border-radius: 0.06rem;
			}

			.red {
				color: #DA5B4C;
			}

			.green {
				color: #2BC488;
			}
		}

		.s-btn {
			margin: 0.1rem auto 0;
			background: #078bde;
			border-radius: 0.16rem;
			border: 0.01rem solid #078bde;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.13rem;
			color: #FFFFFF;
			height: 0.34rem;
			line-height: 0.34rem;
			text-align: center;

			&.bt {
				background: #d5d5d5;
			}
		}
	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background-color: #fff;
		border-radius: 0.08rem;
		position: relative;

		.pad {
			padding: 0.2rem 0.15rem;
		}

		.btips {
			padding: 0.15rem 0;
			line-height: 0.24rem;

			.t2 {
				color: #a91111;
			}
		}

		.pop-title {
			font-size: 0.16rem;
			text-align: center;
		}

		.pop-price {
			padding: 0.15rem 0;
			background: #f6f8fe;

			.t {
				font-size: 0.14rem;
				color: #9a9fa5;

				span {
					color: #60bb74;
				}
			}

			.t1 {
				font-size: 0.16rem;
				color: #000000;
				margin-bottom: 0.05rem;
			}
		}

		.ipt {
			.tt {
				font-size: 0.14rem;
				color: #0e1028;
			}

			input {
				background: transparent;
				border-bottom: 0.01rem solid #cecece;
				height: 0.42rem;
				line-height: 0.42rem;
				margin: 0.1rem 0;
				width: 100%;

				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}

			.t2 {
				font-size: 0.12rem;
				color: #078bde;
			}

			.mt10 {
				margin-top: 0.05rem;
			}
		}

		.pop-num {
			margin-top: 0.15rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				background: #f8f8f8;
				border-radius: 0.06rem;
				border: 0.01rem solid #b8b8b8;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #606060;
				}
			}
		}

		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;

			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}

		.b-btn {
			width: 100%;
		}
	}
</style>