
// let en = require("./lang/en")
let tw = require("./lang/tw")



let didnot = [];
export function initLang(Vue) {
    Vue.prototype.$t = (key) => {
        if (!key) {
            return '';
        }

        // if (en[key]) {
        //     key = en[key]
        // }

        if (tw[key]) {
            key = tw[key]
        }


        // if (!localStorage.getItem('language')) {
        //     if (en[key]) {
        //         key = en[key]
        //     }
        // }
        // if (localStorage.getItem('language') === 'en') {
        //     if (en[key]) {
        //         key = en[key]
        //     }
        // }
        return key;
    }

    let findObj = (obj, lv) => {
        for (let key in obj) {
            if (typeof obj[key] == 'object') {
                findObj(obj[key], lv + 1)
            } else if (lv > 0 && typeof obj[key] == 'string') {
                if (!tw[obj[key]]) {
                    didnot.push(obj[key])
                }
            }
        }
    }

}
