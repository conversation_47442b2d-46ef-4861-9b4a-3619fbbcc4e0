<template>
	<div class="page ">
		<top-back :title="$t('bankManagement').btn"></top-back>

		<div class="cot">
			<div class="item flex">
				<!-- <div class="t">
					{{ $t("bandBankCard").tit1 }}
				</div> -->
				<div class="icon zh"></div>
				<input :placeholder="$t('bandBankCard').tip1" v-model="realname" />
			</div>
			<div class="item flex">
				<div class="icon yhk"></div>
				<!-- <div class="t">{{ $t("bandBankCard").tit2 }}</div> -->
				<input :placeholder="$t('bandBankCard').tip2" v-model="bank_num" />
			</div>
<!--			<div class="item flex">-->
<!--				<div class="icon yhk"></div>-->
<!--				<input :placeholder="$t('bandBankCard').tip5" v-model="bank_code" />-->
<!--			</div>-->
			<div class="item flex">
				<div class="icon jg"></div>
				<!-- <div class="t">{{ $t("bandBankCard").tit3 }}</div> -->
				<input :placeholder="$t('bandBankCard').tip3" v-model="bank_name" />
			</div>

<!--			<div class="item flex">-->
<!--				<div class="icon jg"></div>-->
<!--				<input :placeholder="$t('bandBankCard').tip4" v-model="bank_address" />-->
<!--			</div>-->
			
		</div>
		<div class="big_btn animate__animated animate__fadeIn" style="margin: 0.12rem;" @click="addCard">
			{{ $t("bandBankCard").btn }}
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "addBankCard",
		props: {},
		data() {
			return {
				bank_num: "",
				bank_name: "",
				bank_address: "",
				realname: "",
				bank_code: "",
			};
		},
		components: {},
		created() {},
		computed: {},
		methods: {
			inputName(){
				this.realname = this.realname.replace(/[^a-zA-Z]/g, '');
			},
			addCard() {
				let that = this;
				if (!!that.bank_num && !!that.bank_name && !!that.realname) {
					let parmes = {
						bank_num: that.bank_num,
						bank_name: that.bank_name,
						bank_address: that.bank_name,
						realname: this.realname,
						bank_code: this.bank_name,
						type: this.$stockType,
					};
					this.$refs.loading.open(); //开启加载

					this.$server.post("/user/addCard", parmes).then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(that.$t("new").a7);
							setTimeout(() => {
								this.$router.go(-1);
							}, 1500);
						}
					});
				} else {
					this.$toast(that.$t("bandBankCard").tip6);
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0rem 0;
		min-height: 100vh;

		.cot {
			margin: 0 0.12rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.2rem 0rem rgba(255, 255, 255, 0.35);
			border-radius: 0.07rem;
			padding: 0.2rem 0.15rem;

			.item {
				margin-bottom: 0.2rem;
				padding: 0.05rem 0;
				border-bottom: 0.01rem solid #E4EAF1;
				.t {
					font-weight: 500;
					font-size: 0.14rem;
					color: #24272C;
				}

				input {
					margin-left: 0.1rem;
					width: 100%;
					background: transparent;
					margin-top: 0.05rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.15rem;
					color: #333333;
					&::placeholder {
						font-size: 0.12rem;
						color: #999;
					}
				}
			}
		}
	}
</style>