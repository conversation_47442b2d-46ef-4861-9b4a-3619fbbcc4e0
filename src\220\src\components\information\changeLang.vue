<template>
	<div class="page">
		<top-back :title="$t('mine').menu9"></top-back>
		<div class="list">
			<div class="item flex flex-b" @click="change(item.key)" v-for="(item, i) in langList" :key="i">
				<span>{{ item.name }}</span>
				<div class="icon checked animate__animated animate__fadeIn" v-if="lang == item.key"></div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "",
		props: {},
		data() {
			return {
				lang: "",
				langList: [{
						name: "English",
						key: "en",
					},
					// {
					//   name: "中文简体",
					//   key: "cn",
					// },
					{
						name: "Türkçe",
						key: 'tr'
					}
				],
			};
		},
		created() {
			this.lang = localStorage.getItem("language") || "kor";
		},
		methods: {
			change(type) {
				localStorage.setItem("language", type);
				this.$refs.loading.open(); //开启加载
				setTimeout(() => {
					this.$refs.loading.close();

					this.goBack();
				}, 1500);
			},
			goBack() {
				let _this = this
				let a = window.history.back()
				if (a == undefined) {
					_this.$router.push({
						path: "/home/<USER>"
					});
				}
				return;
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.15rem 0.1rem;
		min-height: 100vh;
	}

	.list {
		background: #ffffff;
		border-radius: 0.08rem;

		.item {
			padding: 0.2rem 0.15rem;
			font-size: 0.14rem;
			color: #000000;
			border-bottom: 0.01rem solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}
		}
	}
</style>