<template>
  <!-- 个人 -->
  <div class="page ">
    <top-menu :title="$t('钱包')"></top-menu>
    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <div class="cot">
        <div class="money">
          <div class="flex flex-b tops ">
            <div class="flex-1">
              <div class="t flex" @click="show = !show">
                {{ $t("mine").txt1 }}
                <div
                  class="icon animate__animated animate__fadeIn"
                  :class="show ? 'bageye' : 'bagby'"
                ></div>
              </div>
              <div class="num">
                {{ show ? $formatMoney(totalAssets) || 0 : "****" }}
              </div>

              <div class="txt">
                <span class="">{{ $t("今日盈亏") }} </span>
                <span :class="percent > 0 ? 'red' : 'green'"
                  >{{ percent > 0 ? "+" : "" }}{{ percent }}%</span
                >
              </div>
            </div>

            <!-- 图 -->
            <div class=" animate__animated animate__fadeIn">
              <!-- <div class="" id="main"></div> -->
              <div class="icon lines"></div>
            </div>
          </div>

          <div class="nums flex flex-b">
            <div class="item">
              <div class="t2">
                <!-- <span class="c1 animate__animated animate__fadeIn"> </span> -->
                {{ $t("mine").txt2 }}
              </div>
              <div class="t1">
                {{ show ? $formatMoney(userInfo.dollar) || 0 : "****" }}
              </div>
            </div>
            <div class="item center">
              <div class="t2">
                <!-- <span class="c2 animate__animated animate__fadeIn"> </span> -->
                {{ $t("mine").txt3 }}
              </div>
              <div class="t1">
                {{ show ? $formatMoney(totalProfit) || 0 : "****" }}
              </div>
            </div>
            <div class="item">
              <div class="t2">
                <!-- <span class="c3 animate__animated animate__fadeIn"> </span> -->
                {{ $t("mine").txt4 }}
              </div>
              <div class="t1">
                {{ show ? $formatMoney(freezeAssets) || 0 : "****" }}
              </div>
            </div>
            <!-- <div class="item">
              <div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
              <div class="t2">{{ $t("new").a36 }} (USD)</div>
            </div> -->
          </div>
        </div>

        <div class="btns flex flex-b">
          <div
            class="btn animate__animated animate__fadeIn flex flex-c"
            @click="$toPage('/information/recharge')"
          >
            <div class="icon cz"></div>
            {{ $t("new").b }}
          </div>
          <div
            class="btn btn1 animate__animated animate__fadeIn flex flex-c"
            @click="$toPage('/information/cashOut')"
          >
            <div class="icon tx"></div>
            {{ $t("new").a23 }}
          </div>

          <div
            class="btn btn1 animate__animated animate__fadeIn flex flex-c"
            @click="$toPage('/information/fundRecord')"
          >
            <div class="icon zj"></div>
            {{ $t("资金明细") }}
          </div>
        </div>

        <!-- 记录合并展示 -->

        <template>
          <div class="tt flex flex-b">
            <div>{{ $t("流水详情") }}</div>
            <!-- <div class="icon sx"></div> -->
          </div>

          <div class="list">
            <div
              class="list-item flex flex-b"
              v-for="(item, i) in logList"
              :key="i"
            >
              <div>
                <div class="t">{{ $t(item.name) }}</div>
                <div class="t1">
                  {{ $formatDate("YYYY-MM-DD hh:mm:ss", item.time) }}
                </div>
              </div>
              <div class="t2" :class="Number(item.money) > 0 ? 'red' : 'green'">
                {{ item.money }}
              </div>
            </div>
          </div>
        </template>
      </div>
    </van-pull-refresh>
    <loading ref="loading" />

    <tab-bar :current="4"></tab-bar>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "payBag",
  props: {},
  data() {
    return {
      chartData: [],
      show: false,
      loading: true,
      isLoading: false,
      kfUrl: "",
      userInfo: {},
      currentIndex: 0,
      moneyType: ["baht", "hkd", "dollar"],
      tabList: [
        {
          name: this.$t("mine").menu3,
          icon: "m1",
          url: "/information/bankList",
        },
        {
          name: this.$t("mine").menu5,
          icon: "m2",
          url: "/information/fundRecord",
        },
        {
          name: this.$t("mine").menu4,
          icon: "m3",
          // url: "/information/authInfo",
          url: "authInfo",
        },
        {
          name: this.$t("setting").txt3,
          icon: "m4",
          url: "/information/loginPass",
        },
        {
          name: this.$t("setting").txt4,
          icon: "m5",
          url: "/information/fundPass",
        },
        {
          name: this.$t("mine").menu10,
          icon: "m6",
          url: "kefu",
        },
        {
          name: this.$t("mine").menu9,
          icon: "m7",
          url: "/information/changeLang",
        },
        {
          name: this.$t("setting").btn,
          icon: "m8",
          url: "exit",
        },
        // {
        //   name: this.$t("exchange").title,
        //   icon: "m4",
        //   url: "/information/exChange",
        // },
      ],
      totalProfit: 0,
      totalAssets: 0,
      freezeAssets: 0,
      myChart: null,
      logList: [],
      logList1: [],
      logList2: [],
      percent: "",
    };
  },
  computed: {},
  mounted() {
    this.getTotalProfit();
    this.getTotalAssets();
    this.getRecord();
  },
  methods: {
    getRecord() {
      this.$refs.loading.open(); //开启加载
      this.$server
        .post("/user/capitalloglist", {
          type: "dollar",
        })
        .then((res) => {
          if (res.status == 1) {
            for (let i = 0; i < res.data.length; i++) {
              var row = res.data[i];
              const chineseToEnglish = {
                购买股票: this.$t("购买股票"),
                返还资金: this.$t("返还资金"),
                股票代码: this.$t("股票代码"),
                股票名称: this.$t("股票名称"),
                订单编号: this.$t("订单编号"),
                撤單: this.$t("撤單"),
                返回本金和手續費: this.$t("返回本金和手續費"),
                購買股票: this.$t("購買股票"),
                買手續費: this.$t("買手續費"),
                平仓: this.$t("平仓"),
                卖手续费: this.$t("卖手续费"),
                訂單編號: this.$t("訂單編號"),
                股票名稱: this.$t("股票名稱"),
                股票代碼: this.$t("股票代碼"),
                跟單返利: this.$t("跟單返利"),
                發單人: this.$t("發單人"),
                产品名稱: this.$t("产品名稱"),
                訂單編號: this.$t("訂單編號"),
                跟單: this.$t("跟單"),
                借券返利: this.$t("借券返利"),
                借券返還: this.$t("借券返還"),
                跟单返利: this.$t("跟单返利"),
              };

              row.detailed = row.detailed.replace("DHZG", "SUNTON");
              if (row.detailed) {
                const text = row.detailed.replace(/！[\s\S]*?股票名称/, "");
                const regex = new RegExp(
                  Object.keys(chineseToEnglish).join("|"),
                  "g"
                );
                if (text) {
                  const result = text.replace(
                    regex,
                    (matched) => chineseToEnglish[matched]
                  );
                  row.detailed = result;
                }
              }
            }
            this.logList1 = res.data;
            this.getRecharge();
          }
        });
    },
    getRecharge() {
      // this.$refs.loading.open(); //开启加载

      this.$server
        .post("/user/rechargelist", {
          type: "dollar",
        })
        .then((res) => {
          // this.$refs.loading.close();

          this.logList2 = res.data;
          this.getWithdrawal();
        });
    },
    getWithdrawal() {
      // this.$refs.loading.open(); //开启加载

      this.$server
        .post("/user/withdrawallist", {
          type: "dollar",
        })
        .then((res) => {
          this.$refs.loading.close();

          let arr = [...this.logList1, ...this.logList2, ...res.data];

          arr.forEach((item) => {
            // item.time = new Date(item.create_time * 1000).getTime();
            item.time = new Date(item.create_time).getTime();

          });
          let lastData = arr.sort((a, b) => b.time - a.time);

          this.logList = lastData;

          // console.log("this.logList", this.logList);
        });
    },
    // 下拉刷新
    onRefresh() {
      // this.getConfig();
      this.getTotalProfit();
      this.getTotalAssets();
    },
    goKefu() {
      this.getConfig();
    },
    //盈利資金
    async getTotalProfit() {
      const res = await this.$server.post("/transaction/userstocklist", {
        is_type: 0,
      });
      let fdyk = 0;
      if (res.status == 1) {
        fdyk = Number(res.data.fdyk);
      }
      this.totalProfit = fdyk;
    },
    // 获取总资产
    async getTotalAssets() {
      this.$refs.loading.open(); //开启加载

      const res = await this.$server.post("/user/getUserinfo", {});
      if (res.status == 1) {
        this.userInfo = res.data;
      }
      let dollar = Number(res.data.dollar || 0); //用户可用余额
      // 获取跟单盈亏
      const res1 = await this.$server.post("/transaction/userproductlist", {});
      let followingFreeze = 0; //量化跟单的认缴冻结
      if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
        let arr = [];
        res1.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.money)); //跟单冻结的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        followingFreeze = total;
      }

      // 申購列表的投資
      const res2 = await this.$server.post("/transaction/usernewstocklist", {});
      let subscriptionProfit = 0; //新股申请的认缴冻结
      if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
        let arr = [];
        let arr1 = [];
        res2.data.forEach((item) => {
          if (item.status == 1) {
            arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
          }
          if (item.status == 0) {
            arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        let total1 = arr1.reduce((a, b) => a + b, 0);

        subscriptionProfit = total + total1;
      }

      // 日内交易的申请冻结 接口报错
      // const res3 = await this.$server.post("/transaction/urnjylist", {
      //   type: 1,
      // });
      // let dayDelFreeze = 0;
      // if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
      //   let arr = [];
      //   res3.data.forEach((item) => {
      //     if (item.status == 0) {
      //       arr.push(Number(item.credit));
      //     }
      //   });
      //   let total = arr.reduce((a, b) => a + b, 0);
      //   dayDelFreeze = total;
      // }

      // 持仓中的申请冻结
      const res4 = await this.$server.post("/transaction/userstocklist", {
        is_type: 0,
      });
      let positionFreeze = 0;
      delete res4.data.ccsz;
      delete res4.data.fdyk;
      let dataArr = Object.values(res4.data);
      if (dataArr.length) {
        let arr = [];

        let arr1 = [];
        let arr2 = [];

        dataArr.forEach((item) => {
          if (item.status == 0) {
            // arr.push(Number(item.credit)); //認繳的资金
            arr.push(
              Number(item.buy_price) * Number(item.stock_num) + item.yingkui
            ); //認繳的资金  买入本金+盈利

            arr1.push(Number(item.yingkui));
            arr2.push(Number(item.buy_price) * Number(item.stock_num));
          }
        });

        let total = arr.reduce((a, b) => a + b, 0);
        let total1 = arr1.reduce((a, b) => a + b, 0);
        let total2 = arr2.reduce((a, b) => a + b, 0);

        this.percent = ((total1 / total2) * 100).toFixed(2); //总盈亏比例

        positionFreeze = total;
      }

      // 大宗交易申请冻结
      const res5 = await this.$server.post("/transaction/ustockslist", {
        type: 0,
      });
      let bigDealFreeze = 0;
      if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
        let arr = [];
        res5.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.credit)); //認繳的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        bigDealFreeze = total;
      }

      // 日内交易持仓
      // const res6 = await this.$server.post("/transaction/userstocklist", {
      //   buy_type: 1,
      // });
      // let dayDealFreeze = 0;
      // delete res6.data.ccsz;
      // delete res6.data.fdyk;
      // let dataArr1 = res6.data;
      // if (dataArr1.length) {
      //   let arr = [];
      //   dataArr1.forEach((item) => {
      //     if (item.status == 0) {
      //       arr.push(Number(item.credit));
      //     }
      //   });
      //   let total = arr.reduce((a, b) => a + b, 0);
      //   dayDealFreeze = total;
      // }

      // 冻结资产
      this.freezeAssets =
        subscriptionProfit + followingFreeze + bigDealFreeze + positionFreeze;
      // 总资产
      this.totalAssets = dollar + this.freezeAssets;
      this.$refs.loading.close();

      this.chartData = [
        // {
        //   value: this.totalAssets || 0,
        //   name: "",
        // },
        {
          value: dollar,
          name: "",
        },
        {
          value: this.totalProfit || 0,
          name: "",
        },
        {
          value: this.freezeAssets || 0,
          name: "",
        },
      ];

      // this.getEcharts();
    },

    async getConfig() {
      this.$refs.loading.open();
      const res = await this.$server.post("/common/config", { type: 'twd' });
      let val = {};
      res.data.forEach((vo) => {
        val[vo.name] = vo.value;
      });
      // this.kfUrl = val.kefu;
      this.$refs.loading.close();
      this.$openUrl(val.kefu); //重新获取
    },
    getEcharts() {
      let that = this;
      if (that.myChart !== null) {
        echarts.dispose(that.myChart);
      }

      let chartDom = document.getElementById("main");
      that.myChart = echarts.init(chartDom);
      let option;
      option = {
        // color: ["#C5585E", "#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
        color: ["#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
        tooltip: {
          trigger: "item",
        },
        // 頂部圖例
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            // radius: ['40%', '70%'], //圆环
            radius: "100%",
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "40",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: this.chartData,
          },
        ],
      };

      option && that.myChart.setOption(option);
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.5rem 0;
}

.tt {
  padding: 0.1rem;
  div {
    font-size: 0.16rem;
  }
}
.list {
  padding: 0 0.1rem;
  .list-item {
    background: #f8f8f8;
    border-radius: 0.08rem;
    border: 0.01rem solid #b8b8b8;
    padding: 0.15rem 0.1rem;
    margin-bottom: 0.1rem;
    .t {
      font-weight: 500;
      font-size: 0.14rem;
    }
    .t1 {
      font-size: 0.12rem;
      color: #949494;
      margin-top: 0.1rem;
    }
    .t2 {
      font-weight: 600;
      font-size: 0.16rem;
    }

    .red {
      color: #a91111;
    }
    .green {
      color: #6bb831;
    }
  }
}

#main {
  width: 0.66rem;
  height: 0.66rem;
  border-radius: 50%;
}

.cot {
  .money {
    padding: 0rem 0.1rem;
    // box-shadow: 0rem 0rem 0.14rem 0rem #eeeeee;
    .tops {
      // background: linear-gradient(90deg, #469d6f, #184856);
      margin-bottom: 0.1rem;
      .t {
        .icon {
          margin-left: 0.1rem;
        }
      }
      .num {
        font-weight: 600;
        font-size: 0.24rem;
        color: #000000;
        margin: 0.05rem 0;
      }

      .txt {
        span {
          font-size: 0.12rem;
        }
        .red {
          color: #a91111;
        }
        .green {
          color: #6bb831;
        }
      }

      .line {
        background: rgba(255, 255, 255, 0.5);
        border-radius: 0.03rem;
        height: 0.07rem;
        position: relative;
        &::after {
          content: "";
          width: 80%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          background: linear-gradient(45deg, #88d68b, #ffffff);
          border-radius: 0.03rem;
        }
      }
      .zc {
        margin-left: 0.3rem;
      }
    }
    .nums {
      margin-top: 0.2rem;
      .item {
        padding: 0 0 0.1rem;
        text-align: center;
        flex: 1;
        &.center {
          border-left: 0.01rem solid #898989;
          border-right: 0.01rem solid #898989;
        }
        .t1 {
          font-size: 0.12rem;
          color: #000000;
        }
        .t2 {
          font-size: 0.12rem;
          color: #898989;
          margin-bottom: 0.05rem;
        }
      }
    }
  }

  .btns {
    margin: 0.2rem 0.1rem 0.1rem;
    .btn {
      width: 30%;
      background: #eeeeee;
      border-radius: 0.04rem;
      font-size: 0.12rem;
      color: #000000;
      text-align: center;
      padding: 0.1rem 0;
      .icon {
        margin-right: 0.05rem;
      }
    }
  }
  .ts {
    font-weight: bold;
    font-size: 0.16rem;
    color: #333333;
    padding: 0.2rem 0;
  }
}

.top {
  padding: 0.15rem;
  .set {
    margin-left: 0.05rem;
  }
  .pad10 {
    padding: 0 0.1rem 0.1rem;
  }
  .user-info {
    .user {
      margin: 0 0.1rem 0 0;
    }
    .account {
      font-size: 0.12rem;
      color: #8f8f8f;
    }
    .rz-btn {
      .bt {
        background: #eceefe;
        border-radius: 0.04rem;
        padding: 0.05rem 0.1rem;
        font-weight: 500;
        color: #777779;
        margin-right: 0.05rem;
      }
    }
  }
}
</style>
