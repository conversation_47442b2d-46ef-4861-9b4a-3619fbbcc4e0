<template>
  <!-- 新股申购 -->
  <div class="page ">
    <top-back title="新股圈購"></top-back>

    <!--    <div class="nav-box">
      <div class="nav-item" v-for="(item, idx) in navList" :key="idx" :class="{ active: currmentIndex === item.type }" @click="changeNav(item.type)">
        {{ item.name }}
        <span></span>
      </div>
    </div> -->

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="26" :loading="loading">
        <div class="cot" v-if="ksgList.length">
          <div
            class="item"
            v-for="(item, i) in ksgList"
            :key="i"
            @click="toDetail(item)"
          >
            <div class="item-top flex flex-b">
              <div class="">
                <div class="name">{{ item.name || "-" }}</div>
                <div class="code">{{ item.symbol || "-" }}</div>
              </div>

              <div class="item-list">
                <div
                  class="t3"
                  :class="item.price - item.bprice < 0 ? 'green' : 'red'"
                >
                  <!-- {{ item.rate }}% -->
                  <!-- 報酬率 -->

                  {{ item.price - item.bprice > 0 ? "+" : ""
                  }}{{
                    ((item.price - item.bprice) / item.bprice) * 100 == -100
                      ? "-"
                      : (
                          ((item.price - item.bprice) / item.bprice) *
                          100
                        ).toFixed(2)
                  }}%
                </div>
                <div class="t4 t-r">
                  溢價差
                </div>
              </div>
            </div>

            <div class="item-middle flex flex-b">
              <div class="item-list flex flex-b">
                <div class="t2">市價</div>
                <div
                  class="t3"
                  :class="item.price - item.bprice < 0 ? 'green' : 'red'"
                >
                  {{ $formatMoney(item.price) }}
                </div>
              </div>

              <div class="item-list flex flex-b">
                <div class="t2 ">總申購</div>
                <div class="t3">
                  {{ $formatMoney(item.num, 0) }}
                </div>
              </div>

              <div class="item-list flex flex-b">
                <div class="t2 ">差價</div>
                <div class="t3">
                  {{ $formatMoney(item.price - item.bprice) }}
                </div>
              </div>

              <div class="item-list flex flex-b">
                <div class="t2 ">承銷價</div>
                <div class="t3 ">
                  {{ $formatMoney(item.bprice) }}
                </div>
              </div>

              <!-- <div class="item-list flex flex-b">
                <div class="t2 ">申購期間</div>
                <div class="t3 ">
                  {{ item.subdate }}
                </div>
              </div>

              <div class="item-list flex flex-b">
                <div class="t2 ">截止日</div>
                <div class="t3 ">
                  {{ item.endTime }}
                </div>
              </div>

              <div class="item-list flex flex-b">
                <div class="t3">撥券日</div>
                <div class="t2">{{ item.amtdate }}</div>
              </div> -->
            </div>
          </div>
        </div>

        <no-data v-if="isShow"></no-data>
      </van-skeleton>
    </van-pull-refresh>

    <!--点击按钮加载效果组件 -->
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "newStock",
  data() {
    return {
      loading: true,
      isShow: false,
      isLoading: false,
      currmentIndex: 0,
      navList: [
        // { name: this.$t("新股申购"), type: 0 },
        // { name: this.$t("新股配售"), type: 1 },
      ],
      ksgList: [
        // {
        //   name: "名称",
        //   symbol: "0089",
        //   code: "100000",
        //   price: 1000,
        //   bprice: 10,
        //   amount: 100,
        //   markets: "999",
        //   drawdate: "2024-03-04 18:00",
        //   ssdate: "2024-03-04 18:00",
        //   amtdate: "2024-03-04 18:00",
        //   isKsg: true,
        // },
      ],
      userInfo: {},
      quantity: "",
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.isShow = false;
      this.getList();
    },
    changeNav(type) {
      this.currmentIndex = type;
      this.getList();
    },
    // 获取列表
    getList() {
      this.$server.post("/trade/placinglist", { type: "twd" }).then((res) => {
        this.isLoading = false; //下拉刷新状态
        this.loading = false;
        if (res.status == 1) {
          let now = new Date().getTime();
          let arr = [];
          res.data.forEach((item) => {
            // 可申购
            if (item.start * 1000 < now && now < item.end * 1000) {
              item.time = Math.floor(
                (item.end * 1000 - now) / 1000 / 60 / 60 / 24
              );
              item.isKsg = true; //是否可申购
            } else if (now < item.start * 1000) {
              item.time = Math.floor(
                (item.start * 1000 - now) / 1000 / 60 / 60 / 24
              );
              // 待申购
              item.isKsg = false;
            }

            arr.push(item);
          });

          this.ksgList = [...new Set(arr)];

          if (!this.ksgList.length) {
            this.isShow = true;
          }
        }
      });
    },
    toDetail(item) {
      this.$storage.save("itemTemp", item);
      this.$toPage(`/home/<USER>
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.5rem 0rem 0.1rem;
  min-height: 100vh;
}

.nav-box {
  position: fixed;
  width: 100%;
  left: 0;
  top: 100px;
  background-color: #fff;
  z-index: 999;
  padding: 0 20px;
  border-bottom: 2px solid #dedede;
  .nav-item {
    width: calc(100% / 2);
    font-size: 32px;
    font-weight: 500;
    color: #666666;
    text-align: center;
    position: relative;
    padding: 20px 0;
    &::after {
      content: "";
      width: 50%;
      height: 4px;
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
    }
  }

  .active {
    color: #3f5ad8;
    font-weight: 600;
    &::after {
      background: #3f5ad8;
    }
  }
}

.cot {
  padding: 0.1rem;
  .item {
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.02rem 0rem rgba(125, 187, 179, 0.12),
      0rem -0.02rem 0.02rem 0rem rgba(126, 187, 180, 0.12);
    border-radius: 0.12rem 0.12rem 0.12rem 0.12rem;
    padding: 0.1rem;
    margin-bottom: 0.15rem;
    .red {
      color: #cf2829;
    }
    .t3 {
      font-size: 0.12rem;
    }

    .item-top {
      .name {
        font-size: 0.14rem;
        color: #000000;
        // font-weight: bold;
      }
      .code {
        font-size: 0.12rem;
        color: #909090;
        margin-top: 0.05rem;
      }
      .t3 {
        font-size: 0.16rem;
      }
      .t4 {
        font-size: 0.12rem;
        color: #909090;
        margin-top: 0.05rem;
      }
    }

    .item-middle {
      flex-wrap: wrap;
      padding: 0.1rem;
      background: rgba(186, 234, 238, 0.16);
      border-radius: 0.06rem;
      margin-top: 0.1rem;

      .item-list {
        line-height: 0.3rem;
        width: 48%;
        .t2 {
          font-size: 0.12rem;
          color: #6c7079;
        }
        .t3 {
          font-size: 0.12rem;
          color: #202626;
        }

        .green {
          color: #68a03d;
        }
        .red {
          color: #ba3b3a;
        }
      }
    }
  }
}

.list {
  .item {
    padding: 40px 20px;
    border-bottom: 2px solid #cccccc;
    .mb20 {
      margin-bottom: 20px;
    }
    .name {
      font-size: 36px;
      font-weight: bold;
      color: #333333;
    }
    .code {
      padding: 10px 20px;
      background: #f0f3fa;
      border-radius: 10px;
      font-size: 24px;
      font-family: Roboto;
      font-weight: 400;
      color: #333333;
      text-align: center;
      margin-top: 10px;
    }
    .time {
      padding: 20px;
      background: #f7e8e8;
      border-radius: 20px;
      font-size: 32px;
      font-weight: bold;
      color: #ff3636;
    }
  }
  .per {
    width: 180px;
    height: 180px;
    border: 10px solid #d4e0eb;
    border-radius: 50%;
    overflow: hidden;
    padding: 40px 0 0;
    text-align: center;
    position: relative;
    margin-right: 20px;
    .t {
      // font-size: 44px;
      font-size: 36px;
      font-family: Roboto;
      font-weight: bold;
      color: #ff3636;
      line-height: 48px;
    }
    .t1 {
      width: 100%;
      color: #333333;
      background-color: #d4e0eb;
      position: absolute;
      padding: 5px 0;
      bottom: 0px;
      font-size: 24px;
    }
  }
  .b-btn {
    margin: 0;
    width: 45%;
    height: 60px;
    line-height: 60px;
    &.bt {
      background-color: #ccc;
    }
  }
}
</style>
