<template>
	<div class="page ">
		<top-back title="添加銀行卡/存摺"></top-back>

		<div class="cot">
			<div class="item">
				<!-- <div class="t">持卡人姓名</div> -->
				<input placeholder="請輸入持有人姓名" v-model="realname" />
			</div>
			<div class="item">
				<!-- <div class="t">金融卡卡種</div> -->
				<input placeholder="請輸入銀行" v-model="bank_address" />
			</div>
			<div class="item">
				<!-- <div class="t">分行代碼</div> -->
				<input placeholder="請輸入銀行代碼" v-model="bank_code" />
			</div>
			<div class="item">
				<!-- <div class="t">金融卡卡號</div> -->
				<input placeholder="請輸入帳號" v-model="bank_num" />
			</div>


			<div class="item">
				<!-- <div class="t">分行名稱</div> -->
				<input placeholder="請輸入分行名稱" v-model="bank_name" />
			</div>
			<div class="b-btn animate__animated animate__fadeIn" @click="addCard">
				確認
			</div>
		</div>
		
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "addBankCard",
		props: {},
		data() {
			return {
				bank_num: "",
				bank_name: "",
				bank_address: "",
				realname: "",
				bank_code: "",
				userInfo: {},
			};
		},
		components: {},
		created() {
			this.getUserInfo();
		},
		computed: {},
		methods: {
			async getUserInfo() {
				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				this.userInfo = res.data;
			},
			addCard() {
				// 未完成實名跳轉
				if (this.userInfo.is_true !== 1) {
					this.$toast("請先完成實名認證");
					setTimeout(() => {
						this.$toPage("/information/authInfo");
					}, 1500);

					return;
				}

				let that = this;
				if (
					!!that.bank_num &&
					!!that.bank_name &&
					!!that.realname &&
					!!that.bank_address &&
					!!that.bank_code
				) {
					let parmes = {
						type: "twd",
						bank_num: that.bank_num,
						bank_name: that.bank_name,
						bank_address: that.bank_address,
						realname: this.realname,
						bank_code: this.bank_code,
					};
					this.$refs.loading.open(); //开启加载

					this.$server.post("/user/addCard", parmes).then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							setTimeout(() => {
								this.$router.go(-1);
							}, 1500);
						}
					});
				} else {
					this.$toast("請填寫金融卡信息");
				}
			},
		},
	};
</script>

<style scoped lang="less">

	.page {
		padding: 0.6rem 0.15rem 0;
		min-height: 100vh;
		background: #f7f7f7;

		.cot {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
			border-radius: 0.07rem;
			padding: 0.2rem 0.15rem;

			.item {
				margin-bottom: 0.2rem;

				.t {
					font-weight: 600;
					color: #0e1028;
				}

				&.last {
					margin-bottom: 0;
				}

				input {
					width: 100%;
					border-bottom: 0.01rem solid #cecece;
					height: 0.3rem;
					color:#000;
					&::placeholder {
						color: #8a8a8a;
						font-weight: 500;
						font-size: 0.12rem;
					}
				}
			}
		}
	}
</style>