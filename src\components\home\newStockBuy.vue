<template>
	<div class="page ">
		<top-back title="申購詳情"></top-back>

		<div class="cot">
			<div class="item">
				<div class="item-top flex flex-b">
					<div class="">
						<div class="name">{{ item.name || "-" }}</div>
						<div class="code">{{ item.symbol || "-" }}</div>
					</div>
					<div class="item-list">
						<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">
							<!-- {{ item.rate }}% -->
							<!-- 報酬率 -->
							{{item.rate}}
							<!-- {{ item.price - item.bprice > 0 ? "+" : ""}}{{((item.price - item.bprice) / item.bprice) * 100 == -100? "-" : (((item.price - item.bprice) / item.bprice) * 100).toFixed(2)}}% -->
						</div>
						<div class="t4 t-r">報酬率</div>
					</div>
				</div>
				<div class="item-middle flex flex-b">
					<div class="item-list flex flex-b">
						<div class="t2">市價</div>
						<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">
							{{ $formatMoney(item.price*1000) }}
						</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t2 ">承銷張數</div>
						<div class="t3">
							{{ $formatMoney(item.num, 0) }}
						</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t2 ">獲利</div>
						<div class="t3" v-if="Number(item.price)">
							{{ $formatMoney(item.price*1000 - item.bprice*1000) }}
						</div>
						<div class="t3" v-else>-</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t2 ">承銷價</div>
						<div class="t3 ">
							{{ $formatMoney(item.bprice) }}
						</div>
					</div>
					<!-- <div class="item-list flex flex-b">
						<div class="t2 ">申購期間</div>
						<div class="t3 ">
							{{ item.subdate }}
						</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t2 ">截止日</div>
						<div class="t3 ">
							{{ item.endTime }}
						</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t3">撥券日</div>
						<div class="t2">{{ item.amtdate }}</div>
					</div> -->
				</div>
			</div>
			<div class="info">
				<div class="title">基本信息</div>
				<div class="info-item flex flex-b">
					<div class="t">申購日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", item.start * 1000) }}
					</span>
				</div>
				<div class="info-item flex flex-b">
					<div class="t">截止日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", item.end * 1000) }}
					</span>
				</div>

				<div class="info-item flex flex-b">
					<div class="t">中籤日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", new Date(item.gb_date)) }}</span>
				</div>

				<div class="info-item flex flex-b">
					<div class="t">撥券日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", new Date(item.fq_date)) }}</span>
				</div>

				<!-- <div class="info-item flex flex-b">
					<div class="t">上市日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", new Date(item.ss_date)) }}</span>
				</div> -->
			</div>

			<div class="info">
				<div class="title">發行信息</div>

				<div class="">
					<div class="info-item flex flex-b">
						<div class="t">發行市場</div>
						<span class="t1">
							{{ item.exchange }}
						</span>
					</div>

					<div class="info-item flex flex-b">
						<div class="t">實際承銷價</div>
						<div class="t1  num-font">
							{{ $formatMoney(item.price) || 0 }}
						</div>
					</div>

					<!-- <div class="info-item flex flex-b">
						<div class="t">報酬率</div>
						<span class="t1">{{ item.rate }}%</span>
					</div> -->
					<!-- <div class="info-item flex flex-b">
						<div class="t">主辦券商</div>
						<div class="t1">{{ item.name || "-" }}</div>
					</div> -->
					<div class="info-item flex flex-b">
						<div class="t">暫時承銷價</div>
						<span class="t1">{{ $formatMoney(item.bprice) || 0 }}</span>
					</div>
				</div>
			</div>

			<!-- 是否可申购 -->
			<div class="bottom" v-if="item.isKsg">
				<div class="wbg">
					<div class="flex flex-b">
						<div class="t">張數</div>
						<div class="t">帳戶可用資金：{{ $formatMoney(userInfo.twd) }}</div>
					</div>
					<div class="flex flex-b Input">
						<input v-model="quantity" @input="quantity = quantity.replace(/[^0-9]/g, '')" placeholder="請輸入張數" type="number" />
						<!-- <div class="fullBtn" @click="submitSg1">滿額申購</div> -->
					</div>
					
				</div>
				<div class="money-list" v-if="false">
					<div class="inner flex flex-b">
						<div class="money-item flex flex-c" v-for="(item, index) in moneyList" :key="index"
							:class="{ active: currmentIndex == index }" @click="quantity = item.money">
							{{ $formatMoney(item.money,0) }}
						</div>
					</div>
				</div>
			</div>
			<div @click="submitSg" class="b-btn animate__animated animate__fadeIn flex-1" v-if="item.isKsg">
				立即申購
			</div>
			<!-- <div @click="submitSg1" class="b-btn1 animate__animated animate__fadeIn flex-1" v-if="false">
				一鍵申購
			</div> -->
		</div>

		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	import {Dialog} from "vant";

  export default {
		name: "newStockBuy",
		data() {
			return {
				item: this.$storage.get("itemTemp") || {},
				quantity: "",
				flag: false,
				type: 0,
				userInfo: {},
				moneyList: [{
						money: "100"
					},
					{
						money: "200"
					},
					{
						money: "500"
					},
					{
						money: "1000"
					},
					{
						money: "1200"
					},
					{
						money: "1500"
					},
					{
						money: "1800"
					},
					{
						money: "2000"
					},
				],
				currmentIndex: -1,
			};
		},
		created() {
			this.type = this.$route.query.type;
			this.getUserInfo();
		},
		methods: {
			async getUserInfo() {
				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
				}
			},
			submitSg1() {
				Dialog.confirm({
					title: '滿額申購100000張',
					confirmButtonText: '確認',
					cancelButtonText: '取消',
					message: '',
				}).then(() => {
					this.flag = true;
					this.$refs.loading.open(); //开启加载
					if(localStorage.getItem('buyNew_' + localStorage.getItem('account'))){
						let oldList = JSON.parse(localStorage.getItem('buyNew_' + localStorage.getItem('account')))
						if(oldList.includes(this.item.id)){
							this.$toast('申購張數已滿');
							this.$refs.loading.close(); //关闭加载
							return false
						}
					}
					this.$server.post("/trade/buy_newstock", {
						symbol: this.item.symbol,
						zhang: 100000,
						type: "twd",
						id: this.item.id,
						buy_type :0
					}).then((res) => {
                    this.$refs.loading.close(); //关闭加载

					this.$toast(this.$formText(res.msg).replace('单只新股，只能申购',this.$t('单只新股，只能申购')));

                    this.getUserInfo();
                    if(res.status==1){
                      if(localStorage.getItem('buyNew_' + localStorage.getItem('account'))){
                        let nowList = JSON.parse(localStorage.getItem('buyNew_' + localStorage.getItem('account')))
                        nowList.push(this.item.id)
                        localStorage.setItem('buyNew_' + localStorage.getItem('account'),JSON.stringify(nowList))
                      }else{
                        let idList = []
                        idList.push(this.item.id)
                        localStorage.setItem('buyNew_' + localStorage.getItem('account'),JSON.stringify(idList))
                      }
                    }
                    setTimeout(() => {
                      this.flag = false;
                    }, 2000);
                  });
            })
            .catch(() => {
              // on cancel
            });

			},
			submitSg() {
				if (!this.quantity) {
					this.$toast("請輸入申購數量");
					return;
				}
				if (this.quantity<50) {
				this.$toast("申購數量最低50張");
				return;
				}
				if (this.quantity>50000) {
				this.$toast("申購數量最高50000張");
				return;
				}

				if (this.flag) {
					return;
				}

				this.flag = true;
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/buy_newstock", {
						symbol: this.item.symbol,
						zhang: this.quantity,
						// zhang:20000,
						type: "twd",
						id: this.item.id,
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						this.$toast(this.$formText(res.msg).replace('单只新股，只能申购',this.$t('单只新股，只能申购')));
						this.getUserInfo();
						setTimeout(() => {
							this.flag = false;
						}, 2000);
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0.1rem;
		min-height: 100vh;
	}

  .fullBtn{
    width: .8rem;
    height: .3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: .15rem;
    background: #DCA25C;
    color: #fff;
    font-size: .12rem;
  }

	.money-list {
		padding: 0.2rem 0 0.2rem;

		.title {
			font-weight: 600;
			color: #0e1028;
			margin-bottom: 0.1rem;
		}

		.inner {
			flex-wrap: wrap;

			.money-item {
				width: 24%;
				height: 0.58rem;
				background: #F8F9FD;
				border-radius: 0.04rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.17rem;
				color: #182133;
				padding: 0 0.1rem;
				text-align: center;
				margin-bottom: 0.1rem;

				&.active {
					background: #F9F0E7;
					border-radius: 0.04rem;
					border: 0.01rem solid #DCA25C;
				}
			}
		}
	}

	.cot {
		.item {
			margin-bottom: 0.15rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;

			.red {
				color: #cf2829;
			}

			.t3 {
				font-size: 0.12rem;
			}

			.item-top {
				.name {
					font-size: 0.14rem;
					color: #000000;
					// font-weight: bold;
				}

				.code {
					font-size: 0.12rem;
					color: #909090;
					margin-top: 0.05rem;
				}

				.t3 {
					font-size: 0.16rem;
				}

				.t4 {
					font-size: 0.12rem;
					color: #909090;
					margin-top: 0.05rem;
				}
			}

			.item-middle {
				flex-wrap: wrap;
				padding: 0.1rem;
				background: rgba(246, 248, 254, 1.0);
				border-radius: 0.06rem;
				margin-top: 0.1rem;

				.item-list {
					line-height: 0.3rem;
					width: 48%;

					.t2 {
						font-size: 0.12rem;
						color: #6c7079;
					}

					.t3 {
						font-size: 0.12rem;
						color: #202626;
					}

					.green {
						color: #68a03d;
					}

					.red {
						color: #ba3b3a;
					}
				}
			}
		}

		.info {
			margin-bottom: 0.1rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
				color: #333333;
			}

			.info-item {
				padding: 0.15rem 0;
				border-bottom: 0.01rem solid #ededed;

				&:last-child {
					border-bottom: 0;
				}

				.t {
					font-size: 0.12rem;
					color: #5c5c5c;
				}

				.t1 {
					font-size: 0.12rem;
					color: #272a2f;
				}
			}
		}

		.bottom {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;

			.wbg {
				padding: 0.1rem 0;

				.t {
					font-size: 0.12rem;
				}
				.Input{
					border-bottom: 0.01rem solid #cecece;
					span{
						flex:none;
					}
				}
				input {
					width: 100%;
					height: 0.4rem;
					margin-top: 0.1rem;
					background: transparent;
					
					color: #000;

					&::placeholder {
						font-size: 0.15rem;
						color: #9a9fa5;
					}
				}
			}

			.b-btn {
				margin: 0.1rem 0rem;
			}
		}

		.top {
			padding: 0.1rem;

			.txt {
				font-size: 0.12rem;
				color: #464646;
				margin-top: 0.05rem;
			}

			.st-btn {
				font-weight: 500;
				font-size: 0.12rem;
				color: #c5585e;
			}
		}
	}

	.b-btn1 {
		height: 0.39rem;
		border: .01rem solid #DCA25C;
		border-radius: 0.2rem;
		line-height: 0.39rem;
		text-align: center;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.14rem;
		color: #DCA25C;
		margin: 0.2rem 0;
	}
</style>