<template>
  <div class="page">
    <top-back :title="$t('mine').menu9"></top-back>
    <div class="list">
      <div
        class="item flex flex-b"
        @click="change(item.key)"
        v-for="(item, i) in langList"
        :key="i"
      >
        <span>{{ item.name }}</span>
        <div
          class="icon xzl animate__animated animate__fadeIn"
          v-if="lang == item.key"
        ></div>
        <!-- <div class="icon nocheck animate__animated animate__fadeIn" v-else></div> -->
      </div>
    </div>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "",
  props: {},
  data() {
    return {
      lang: "",
      langList: [
        {
          name: "English",
          key: "en",
        },
        // {
        //   name: "한국어",
        //   key: "kor",
        // },
      ],
    };
  },
  created() {
    this.lang = localStorage.getItem("language") || "kor";
  },
  methods: {
    change(type) {
      localStorage.setItem("language", type);
      this.$refs.loading.open(); //开启加载
      setTimeout(() => {
        this.$refs.loading.close();

        this.$toPage("/information/index");
      }, 1500);
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.6rem 0rem 0.2rem;
  min-height: 100vh;
}

.list {
  .item {
    padding: 0.15rem;
    font-size: 0.16rem;
    color: #0a0a0a;
    border-bottom: 0.01rem solid #e3e3e3;
    span {
      font-size: 0.16rem;
      color: #0a0a0a;
    }
  }
}
</style>
