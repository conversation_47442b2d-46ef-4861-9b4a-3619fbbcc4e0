<template>
	<div class="page ">
		<top-back title="持倉詳情"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="wbg">
				<div class="flex flex-b top">
					<div class="">
						<div class="name">{{ item.stock_name || "-" }}</div>
						<div class="code">{{ item.stock_code || "-" }}</div>
					</div>
					<div>
						<div class="st" :class="{ pc: item.sell_time }">
							{{ statusStr[item.status] }}
						</div>
						<div class="per" :class="currentItem.gain > 0 ? 'red' : 'green'">
							{{ currentItem.gain > 0 ? "+" : ""}}{{ $formatMoney(currentItem.gain)}}%
							<span style="margin-left: .05rem;">{{ currentItem.gain > 0 ? "+" : ""}}{{ $formatMoney(currentItem.yingkui)}}</span>
						</div>
						<!-- <div class="per" :class="currentItem.gain > 0 ? 'green' : 'red'">
							{{ currentItem.gain > 0 ? "+" : ""}}{{ currentItem.yingkui.toFixed(2) }}
							{{ currentItem.gain > 0 ? "+" : ""}}{{ currentItem.gain.toFixed(2) }}%
						</div> -->
					</div>
				</div>
				<div>
					<!-- <div class="yk mt5" :class="currentItem.gain > 0 ? 'green' : 'red'">
						{{isNaN(Number(currentItem.yingkui).toFixed(2))? "0": Number(currentItem.yingkui).toFixed(2)}}
					</div> -->
				</div>
			</div>
			<div class="list">
				<div class="cot">
					<div class="list-item">
						<div class="item-left">買入價格</div>
						<div class="item-right">{{ $formatMoney(item.buy_price) }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">買入張數</div>
						<div class="item-right">{{ $formatMoney(item.stock_num/1000,0) }}</div>
					</div>
					<div class="list-item" v-if="nowprice">
						<div class="item-left">市場現價</div>
						<div class="item-right">{{ $formatMoney(nowprice) }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">本金</div>
						<div class="item-right">{{ $formatMoney(item.credit) }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">槓桿倍數</div>
						<div class="item-right">{{ item.ganggang }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">市值</div>
						<div class="item-right">
							{{ $formatMoney(parseFloat(item.market_value)+parseFloat(currentItem.yingkui)) }}</div>
						<!-- <div class="item-right">{{$formatMoney(parseFloat(item.sell_price * item.stock_num).toFixed(2))}}
						</div> -->
					</div>
					<!-- <div class="list-item">
						<div class="item-left">買進手續費</div>
						<div class="item-right">
							{{ $formatMoney(item.buy_poundage) || 0 }}
						</div>
					</div>
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">賣出手續費</div>
						<div class="item-right">
							{{ $formatMoney(item.sell_poundage) || 0 }}
						</div>
					</div> -->
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">系統服務費</div>
						<div class="item-right">
							{{ $formatMoney((parseFloat(currentItem.yingkui))*0.05) || 0 }}
						</div>
					</div>
					
					<div class="list-item">
						<div class="item-left">類型</div>
						<div class="item-right">{{ item.type == 1 ? "市價" : "限價" }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">買賣類型</div>
						<div class="item-right" v-if="parseInt(item.buyzd) === 1">買漲</div>
						<div class="item-right" v-if="parseInt(item.buyzd) === 2">買跌</div>
					</div>
					<div class="list-item">
						<div class="item-left">訂單號</div>
						<div class="item-right">{{ visibilityChange(item.strategy_num) }}</div>
					</div>
					<!-- <div class="list-item">
						<div class="item-left">買入時間</div>
						<div class="item-right">
							{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.buy_time * 1000) }}
						</div>
					</div>
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">賣出時間</div>
						<div class="item-right">
							{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.sell_time * 1000) }}
						</div>
					</div> -->
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">賣出價格</div>
						<div class="item-right">{{ $formatMoney(item.sell_price) }}</div>
					</div>
					<!-- <div class="list-item">
						<div class="item-left">{{ $t("positionDetail").txt7 }}</div>
						<div class="item-right">{{ $formatMoney(item.credit) }}</div>
					</div> -->
					<!-- <div class="list-item">
						<div class="item-left">{{ $t("positionDetail").txt8 }}</div>
						<div class="item-right">{{ $formatMoney(item.ganggang) }}</div>
					</div> -->
				</div>
			</div>
		</van-pull-refresh>
	</div>
</template>
<script>
	export default {
		name: "positionDetail",
		data() {
			return {
				isLoading: false,
				currentItem: {
					// gain:1,
					// yingkui:10,
				},
				item: {
					// stock_name: "stock_name",
					// stock_code: "stock_code",
					// status: "审核中",
					// yingkui: 1000,
					// buy_poundage:100,
					// sell_poundage:20,
					// type:1,
					// yingkuiBi: 1000,
					// stock_num: 1000,
					// buy_price: 1000,
					// sell_price:10,
					// sell_time: "2024-03-02",
					// buy_time:"2024-03-02",
					// strategy_num: 1000,
					// buyzd:1,
				},
				statusStr: ["持倉中", "已平倉", "準備平倉", "掛單中"],
				nowprice: ''
			};
		},
		computed: {},
		created() {
			this.nowprice = this.$route.query.nowprice
			this.currentItem = this.$storage.get("currentItem");
			this.getDetail();
		},
		methods: {
			visibilityChange(val) {
				let _len = val.length
				return val.substring(0, 4) + '****' + val.substring(_len - 4, _len)
			},
			// 下拉刷新
			onRefresh() {
				this.getDetail();
			},
			// 获取详情
			getDetail() {
				this.$server
					.post("/trade/stockdetail", {
						id: this.currentItem.id,
						type: "twd",
					})
					.then((res) => {
						this.isLoading = false;
						if (res.status == 1) {
							this.item = res.data;
						}
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.5rem 0.1rem 0.1rem;
		min-height: 100vh;
		background: #f7f7f7;
	}

	.wbg {
		margin: 0.1rem 0;

		.top {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;
		}

		.name {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #182133;
		}

		.st {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.13rem;
			color: #DA5B4C;

			&.pc {
				color: #979797;
			}
		}

		.per {
			font-weight: 500;
			font-size: 0.12rem;
			margin-top: 0.05rem;
		}

		.code {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.11rem;
			color: #69717D;
		}

		.yk {
			font-weight: 500;
			margin-top: 0.1rem;

			.c {
				width: 0.12rem;
				height: 0.12rem;
				background: #6970af;
				border-radius: 50%;
				margin-right: 0.05rem;
			}

			&.mt5 {
				margin-top: 0.05rem;
			}
		}

		.red {
			color: #cf2829;
		}

		.green {
			color: #60bb74;
		}
	}

	.list {
		.cot {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;

			.list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0.12rem 0.1rem;
				border-bottom: 0.01rem solid #EAEDF8;

				&:last-child {
					border-bottom: 0;
				}

				.item-left {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #69717D;
				}

				.item-right {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #182133;
				}
			}
		}
	}
</style>