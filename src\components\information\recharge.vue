<template>
	<div class="page ">
		<top-back title="儲值" :isList="true"></top-back>

		<div class="cot">
			<div class="money">
				<div class="t flex">
					可用資金
					<div class="icon bageye animate__animated animate__fadeIn" @click="show = !show">
					</div>
				</div>
				<div class="t1">
					NT$ {{ show ? $formatMoney(userInfo.twd) : "****" }}
				</div>
			</div>
			<div class="bg">
				<!-- <div class="nums">{{ $formatMoney(money) }}</div>
				<div class="title">儲值金額</div> -->
				<div class="tt">金額</div>
				<div class="ipt">
					<div class="flex flex-b">
						<input class="input flex-1" v-model="money" type="number" placeholder="請輸入儲值金額" />
					</div>
				</div>
				<div class="money-list">
					<!-- <div class="title">{{ $t("new").a40 }}</div> -->
					<!-- 充值金额 -->
					<div class="inner flex flex-b">
						<div class="money-item flex flex-c" v-for="(item, index) in moneyList" :key="index"
							:class="{ active: currmentIndex == index }" @click="changeMoney(index)">
							{{ $formatMoney(item.money) }}
						</div>
					</div>
				</div>
				<div class="tt">通道密碼</div>
				<div class="ipt">
					<div class="flex flex-b">
						<input class="input flex-1" v-model="rpassword" type="number" placeholder="請輸入通道密碼" />
					</div>
				</div>
				<div class="pad15">
					<div class="b-btn animate__animated animate__fadeIn" @click="chongzhi">
						確認
					</div>
				</div>

				<!-- <div class="tips">
					<div class="t1">
						{{ $t("new").a43 }}：{{ $t("new").a44 }}{{ open }}-{{ close }}
					</div>
				</div> -->
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "recharge",
		props: {},
		data() {
			return {
				show: true,
				open: "",
				close: "",
				currmentIndex: -1,
				money: "",
				userInfo: {},
				moneyList: [{
						money: "5000"
					},
					{
						money: "10000"
					},
					{
						money: "15000"
					},
					{
						money: "20000"
					},
					{
						money: "25000"
					},
					{
						money: "30000"
					},
				],
				type: 0,
				minMoney: 0,
				minrecharge: 0,
				rpassword: "",
			};
		},
		components: {},
		created() {
			this.getUserInfo();
			this.getConfig();
		},
		computed: {},
		methods: {
			changeMoney(index) {
				this.currmentIndex = index;
				this.money = this.moneyList[index].money.replace(/,/g, "");
			},
			chongzhi() {
				// 测试跳转通道页
				//   setTimeout(() => {
				//     this.$toPage(
				//       `/information/rechargeChannel?money=${this.money}&type=${this.type}`
				//     );
				//   }, 100);
				//   return;

				if (this.userInfo.is_true !== 1) {
					this.$toast("請先完成實名認證");
					setTimeout(() => {
						this.$toPage("/information/authInfo");
					}, 2000);
					return;
				}

				if (!this.money) {
					this.$toast("請輸入儲值金額");
					return;
				}

				if (!this.rpassword) {
					this.$toast("請輸入通道密碼");
					return;
				}

				let val = parseInt(this.money.replace(/\,/g, ""));
				if (val < this.minrecharge) {
					this.$toast("最低儲值" + this.$formatMoney(this.minrecharge));
					return;
				}
				this.$refs.loading.open(); //开启加载
				this.$server
					.post("/user/ischongzhi", {
						money: val,
						type: "twd",
						rpassword: this.rpassword,
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							//   this.$toast(this.$t("充值请联系客服"));
							//   return;

							setTimeout(() => {
								this.$toPage(
									`/information/rechargeChannel?money=${val}&type=${this.type}`
								);
							}, 2000);
						}
					});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						this.isChg = !!res.data.passwords;
					}
				});
			},
			getConfig() {
				this.$server.post("/common/config", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === "srecharge") {
								this.open = list[a].value;
							}
							if (list[a].name === "erecharge") {
								this.close = list[a].value;
							}
							if (list[a].name === "minrecharge") {
								this.minrecharge = list[a].value;
							}
							// if (list[a].name === 'kefu') {
							//   this.customer = list[a].value;
							// }
						}
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0rem 0;
		min-height: 100vh;
	}

	.tips {
		.t {
			color: #888888;
		}

		.t1 {
			font-size: 0.12rem;
			color: #888888;
			line-height: 0.2rem;
		}
	}

	.cot {
		.money {
			padding: 0.2rem 0.15rem 0.5rem;
			background: url('../../assets/v1/hbg.png') no-repeat center/100%;
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;

				.icon {
					margin-left: 0.05rem;
				}
			}

			.t1 {
				margin-top: 0.1rem;
				font-weight: 600;
				font-size: 0.26rem;
				color: #ffffff;
				font-family: Poppins, Poppins;
			}
		}

		.bg {
			background: #FFFFFF;
			border-radius: 0.15rem 0.15rem 0rem 0rem;
			padding: 0.2rem 0.15rem;
			margin: -0.3rem 0 0;

			.nums {
				font-family: Poppins, Poppins;
				font-weight: bold;
				font-size: 0.3rem;
				color: #39324b;
				text-align: center;
			}

			.title {
				color: #6b6b6b;
				text-align: center;
			}
		}

		.money-list {
			padding: 0.2rem 0 0.2rem;

			.title {
				font-weight: 600;
				color: #0e1028;
				margin-bottom: 0.1rem;
			}

			.inner {
				flex-wrap: wrap;

				.money-item {
					width: 32%;
					height: 0.58rem;
					background: #F8F9FD;
					border-radius: 0.04rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
					color: #182133;
					padding: 0 0.1rem;
					text-align: center;
					margin-bottom: 0.1rem;

					&.active {
						background: #d8e3f6;
						border-radius: 0.04rem;
						border: 0.01rem solid #078bde;
					}
				}
			}
		}

		.tt {
			font-weight: 600;
			color: #0e1028;
			margin: 0 0 0.15rem;
		}

		.ipt {
			border-bottom: 0.01rem solid #cecece;

			.input {
				width: 100%;
				background: transparent;
				height: 0.42rem;
				color: #000;

				&::placeholder {
					font-size: 0.14rem;
					color: #aaaaaa;
				}
			}
		}

		.pad15 {
			padding: 0.2rem 0;
		}

		.b-btn {
			margin: 0;
		}
	}
</style>