<template>
	<div class="page ">
		<!-- <top-back :title="$t('news').title"></top-back> -->
		<div class="header flex flex-b">
			<div class="icon mine" @click="$toPage('/information/index')"></div>
			<div class="tit">{{$t('news').title}}</div>
			<div class="flex">
				<div class="icon ss1" style="margin-right: 0.1rem;"@click="$toPage('/favorite/search')"></div>
				<div class="icon msg2" @click="$toPage('/information/userInfo')"></div>
			</div>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3" :loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-list v-model="loading" :finished="finished" :loading-text="$t('new').a" :finished-text="$t('没有更多了')" @load="onLoad">
        <div class="nav-box flex">
          <div class="nav-item" v-for="(item, index) in navList" :key="index"
               :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
            {{ item.name }}<span></span>
          </div>
        </div>
				<no-data v-if="!articleList.length"></no-data>
				<div class="news-list" v-if="articleList.length">
					<!-- <div class="icon news"></div> -->

					<div class="news-item" v-for="(item, index) in articleList" :key="index"
						@click="clickNewsDetail(item)">
						<div class="flex flex-b">
							<div class="img" v-if="item.img">
								<img :src="item.img" alt="" />
							</div>
							<div class="flex-1">
								<div class="t">{{ item.title }}</div>
								<div class="time">
									{{ $formatDate("DD-MM-YYYY hh:mm", item.created*1000) }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-list>
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
<!--		<loading ref="loading" />-->
		<tab-bar :current="4"></tab-bar>
	</div>
</template>

<script>
	export default {
		name: "newsList",
		props: {},
		data() {
			return {
				isLoading: false,
				loading: false,
				finished: false,
				articleList: [],
				page: -1,
        navList: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
			};
		},
		components: {},
		created() {
			// this.getNews();
		},
		computed: {},
		methods: {
			// 下拉刷新
      changeStock(type){
        this.stockType = type
        this.getNews();
      },
			onRefresh() {
				this.page = 0;
				this.getNews();
			},
			onLoad() {
				this.page += 1;
				this.getNews();
			},
			getNews() {
        let per = {}
        if(this.stockType == 'try'){
          per = {
            exchange: "tr",
            lang: "tr"
          }
        }
        if(this.stockType == 'usd'){
          per = {
            exchange: "us",
            lang: "en"
          }
        }
				this.$server.post("/common/newss", per).then((res) => {
					this.isLoading = false; //下拉刷新状态
					this.loading = false;
					if(res && res.data.result){
						this.articleList = res.data.result;
						this.finished = true;
					}
				});
			},
			clickNewsDetail(item){
				window.localStorage.setItem('newsDetail', JSON.stringify(item));
				this.$toPage('/home/<USER>');
			}
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.12rem 1rem;
		.header {
			width: 100vw;
			height: .5rem;
			background: #F3F2F2;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 999;
			padding: 0 0.12rem;
			.tit{
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.17rem;
				color: #111111;
			}
		}
	}

  .nav-box {
    width: 100%;
    height: .4rem;
    top: .5rem;
    padding: 0.15rem;
    .nav-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.14rem;
      color: #111111;
      text-align: center;
      margin-right: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span{
        width: .5rem;
        height: .03rem;
        background: transparent;
        margin-top: .05rem;
        display: block;
      }
      &.active {
        font-weight: 600;
        font-size: 0.14rem;
        color: #E10414;
        position: relative;
        span{
          background: #E10414;
        }
      }
    }
  }

	.news-list {
		flex-wrap: wrap;
		background: #FFFFFF;
		border-radius: 0.13rem;
		.news {
			margin-bottom: 0.1rem;
		}

		.news-item {
			padding: 0.12rem;

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #0C061C;
			}

			.time {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
				margin-top: 0.05rem;
			}

			.img {
				margin-right: 0.1rem;
				img {
					width: 1.15rem;
					height: 0.76rem;
					border-radius: 0.06rem;
				}
			}
		}
	}
</style>
