<template>
	<!-- 市场 -->
	<div class="page ">
		<!-- <top-index></top-index> -->
		<div class="header flex flex-b">
			<!-- <div class="tab flex">
				<div class="item" :class="{'sel':tabIdx==idx}" v-for="(item,idx) in tabList" :key="idx" @click="clickTab(idx)">{{item}}</div>
			</div> -->
			<div class="icon mine" @click="$toPage('/information/index')"></div>
			<div class="tit">{{$t('menu').href3}}</div>
			<div class="flex">
				<div class="icon msg" @click="$toPage('/information/userInfo')"></div>
				<div class="icon kset" style="margin-left: 0.1rem;" @click="$toPage('/information/setting')"></div>
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
      <div class="nav-box flex">
        <div class="nav-item" v-for="(item, index) in navList" :key="index"
             :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
          {{ item.name }}<span></span>
        </div>
      </div>
			<div class="zhiShu">
				<div class="itemBox flex">
					<div class="item" v-for="(item,idx) in zhiShulist" :key="idx"
						:class="item.gainValue>=0?'red':'green'"
						@click="clickZhiDetail(item),$toPage('/market/stockDetail?symbol='+item.symbol + '&stockType=' + stockType)">
						<div class="name">{{item.symbol}}</div>
						<div class="price">{{$formatMoney(item.price)}}</div>
						<div class="per flex flex-b" :class="item.gain>0?'red':'green'">
							<div>{{$formatMoney(item.gainValue)}}</div>
							<div> {{stockType=='try'?'%':''}}{{$formatMoney(item.gain)}}{{stockType=='try'?'':'%'}}</div>
						</div>
						<img src="../../assets/v2/redLine.png" style="width: 0.98rem;height: 0.36rem;" alt=""
							v-if="item.gain>0" />
						<img src="../../assets/v2/greenLine.png" style="width: 0.98rem;height: 0.36rem;" alt=""
							v-else />
					</div>
				</div>
			</div>
			<div class="fastList flex">
				<div class="item text-center" v-for="(item,idx) in fastList" :key="idx" @click="fastIndexClick(idx)"
        >
					<!-- <img :src="item.icon" /> -->
					<div>{{item.name}}</div>
				</div>
			</div>
			<template v-if="fastIndex==0">
<!--				<indexLine></indexLine>-->
				<div class="new">
					<div class="flex flex-b tab">
						<div class="tab-item" :class="{ active: typeIndex == i }" v-for="(item, i) in typeList" :key="i" @click="changeType(i)">{{ $t(item) }}</div>
					</div>

					<div class="news-item flex flex-b" v-for="(item, index) in newsList" :key="index" @click="clickNewsDetail(item)" v-if="index<max&&index>min">
						<div class="img">
							<img :src="item.img" alt="" />
						</div>
						<div class="pad flex-1">
							<div class="tt">{{ item.title }}</div>
							<div class="time">
								{{ $formatDate( "DD-MM-YYYY hh:mm", new Date(item.created*1000) ) }}
							</div>
						</div>
					</div>
				</div>

				<!-- 市场列表 -->
				<teShe ref="teShe" :stockType="stockType" @upData="upData" />
			</template>
<!--			<template v-if="fastIndex == 1">-->
<!--				<featuredItems />-->
<!--			</template>-->
<!--			<template v-if="fastIndex == 2">-->
<!--				<marketIndicators />-->
<!--			</template>-->
<!--			<template v-if="fastIndex == 3">-->
<!--				<marketProblem />-->
<!--			</template>-->
		</van-pull-refresh>
		<loading ref="loading" />
		<tab-bar :current="1"></tab-bar>
	</div>
</template>

<script>
	import teShe from "./components/teShe.vue";
	import indexLine from "../components/index-line.vue";
	import marketProblem from "./components/marketProblem";
	import marketIndicators from "./components/marketIndicators";
	import featuredItems from "./components/featuredItems";
  import kLine from "@/components/market/components/k-line.vue";

	export default {
		name: "",
		props: {},
		data() {
			return {
				loading: true, //控制显示骨架屏占位显示
				isLoading: false,
				isShow: false,
				tabList: [this.$t('行情'), this.$t('自选')],
				tabIdx: 0,
				zhiShulist: [],
				fastList: [{
						name: this.$t('自选行情'),
						icon: require('../../assets/market/ico1.png'),
						url: '/favorite/index'
					},
					{
						name: this.$t('market').fast1, //股票
						icon: require('../../assets/market/ico2.png'),
						url: '/market/stock'
					},
					{
						name: this.$t('market').fast2, //指数
						icon: require('../../assets/market/ico3.png'),
						url: '/market/zhiShu'
					},
					{
						name: this.$t('news').title,
						icon: require('../../assets/market/ico4.png'),
						url: '/home/<USER>'
					}
					/* {
						name: this.$t('market').fast3,//新股申购
						icon: require('../../assets/market/fastIco3.png'),
						url: '/home/<USER>'
					}, */
					/* {
						name: this.$t('market').fast4,//盘前
						icon: require('../../assets/market/fastIco4.png'),
						url: '/home/<USER>'
					}, */
					/* {
						name: this.$t('market').fast5,//盘后
						icon: require('../../assets/market/fastIco5.png'),
						url: '/home/<USER>'
					}, */
					/* {
						name: this.$t('market').fast6,//大宗
						icon: require('../../assets/market/fastIco6.png'),
						url: '/home/<USER>'
					}, */
					/* {
						name: this.$t('market').fast7,//量化
						icon: require('../../assets/market/fastIco7.png'),
						url: '/home/<USER>'
					} */
				],
				fastIndex: 0,
				typeList:['新闻','股票','7X24'],
				typeIndex:0,
				newsList:[],
				min:0,
				max:6,
        navList: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
			};
		},
		components: {
      kLine,
			teShe,
			indexLine,
			marketProblem,
			marketIndicators,
			featuredItems
		},
		created() {

		},
		mounted() {
			this.getZhiShu();
			this.getNews();
		},
		computed: {},
		methods: {
      changeStock(type){
        let _this = this
        this.stockType = type
        this.getZhiShu();
        this.getNews();
        setTimeout(function (){
          _this.$refs.teShe.getInfo();
        },1000)
      },
      fastIndexClick(e){
        this.fastIndex = e
        if(e == 3){
          this.$toPage('/home/<USER>')
        }else if(e == 0){
          this.$toPage('/favorite/index')
        }else if(e==1){
          this.$toPage('/market/stock')
        }else if(e==2){
          this.$toPage('/market/zhiShu')
        }
      },
			changeType(e){
				this.typeIndex=e
				if(e==1){
					this.min=5
					this.max=11
				}else if(e==2){
					this.min=11
					this.max=17
				}else{
					this.min=0
					this.max=6
				}
			},
			getNews() {
        let per = {}
        if(this.stockType == 'try'){
          per = {
            exchange: "tr",
            lang: "tr"
          }
        }
        if(this.stockType == 'usd'){
          per = {
            exchange: "us",
            lang: "en"
          }
        }
				this.$server.post("/common/newss", per).then((res) => {
					if (res && res.data) {
						this.newsList = res.data.result;
					}
				});
			},
			clickTab(idx) {
				if (idx == 1) {
					this.$toPage('/favorite/index');
				}
			},
			upData() {
				this.isLoading = false;
			},
			// 下拉刷新
			onRefresh() {
				this.$refs.teShe.getInfo();
			},
			getZhiShu() {
				this.$server.post("/parameter/zhishu", {
					type: this.stockType
				}).then((res) => {
					this.loading = false;
					this.isLoading = false;
					if (res && res.data) {
						this.zhiShulist = res.data;
					}
				});
			},
			clickZhiDetail(item) {
				this.$storage.save("stockDetail", item);
			}
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 1rem;
		min-height: 100vh;
	}

  .nav-box {
    width: 100%;
    height: .4rem;
    top: .5rem;
    padding: 0.15rem;
    .nav-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.14rem;
      color: #111111;
      text-align: center;
      margin-right: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span{
        width: .5rem;
        height: .03rem;
        background: transparent;
        margin-top: .05rem;
        display: block;
      }
      &.active {
        font-weight: 600;
        font-size: 0.14rem;
        color: #E10414;
        position: relative;
        span{
          background: #E10414;
        }
      }
    }
  }

	.header {
		width: 100vw;
		height: .5rem;
		background: #F3F2F2;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0 0.12rem;
		font-weight: 500;
		font-size: .18rem;
		color: #000000;

		.tit {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.17rem;
			color: #111111;
		}

		.tab {
			.item {
				font-weight: 500;
				font-size: .14rem;
				color: rgba(255, 255, 255, 0.49);
				margin-right: .2rem;

				&.sel {
					font-size: .15rem;
					color: #FFFFFF;
				}
			}
		}

		img {
			width: .16rem;
		}
	}

	.zhiShu {
		.itemBox {
			overflow: scroll;
			margin: 0.1rem 0.12rem;
			background: #FFFFFF;
			border-radius: 0.13rem;
			padding: 0.1rem 0;

			.item {
				width: 1.2rem;
				padding: 0 .12rem;
				flex: none;
				border-right: 0.01rem solid #E4EAF1;

				&:last-child {
					border-right: none;
				}

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #666666;
				}

				.price {
					font-family: OPPOSans, OPPOSans;
					font-weight: normal;
					font-size: 0.18rem;
					color: #0C061C;
					padding: .1rem 0;
				}

				.per {
					margin-bottom: 0.1rem;
					font-family: OPPOSans, OPPOSans;
					font-weight: normal;
					font-size: 0.13rem;
				}
			}
		}
	}

	.new{
		margin: 0.12rem;
		background: #FFFFFF;
		border-radius: 0.13rem;
		.news-item {
			padding: .12rem;
			.img {
				flex: none;
				margin-right: .12rem;

				img {
					width: 1.15rem;
					height: 0.76rem;
					border-radius: .06rem;
					// object-fit:fill;
				}
			}

			.tt {
				font-weight: 600;
				font-size: 0.16rem;
				color: #0C061C;
				-webkit-line-clamp: 2;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}

			.time {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #999999;
				margin-top: .1rem;
			}
		}
	}
	.tab {
		padding: 0.12rem;
		border-bottom: 0.01rem solid #E4EAF1;

		.tab-item {
			margin-right: 0.2rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #999999;
			text-align: center;
			text-align: center;
			padding: 0.05rem 0;

			&.active {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #111111;
				position: relative;
				&::after{
					position: absolute;
					content: '';
					left: 0;
					bottom: -0.12rem;
					width: 100%;
					height: 0.02rem;
					background: #E10414;
				}
			}
		}
	}

	.fastList {
		background: #fff;
		border-radius: 0.16rem;
		margin: 0.12rem;
		.item {
			width: 25%;
			padding: 0.1rem 0;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #666666;
			white-space: nowrap;

			img {
				width: .24rem;
			}
		}

		.active {
			background: #FFFFFF;
			border-radius: 0.19rem;
			border: 0.01rem solid #E10414;
			color: #E10414;
			font-weight: 600;
		}

	}
</style>
