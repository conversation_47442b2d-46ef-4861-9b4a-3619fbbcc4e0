<template>
	<div class="page">
		<!-- <div class="flex flex-c">
			<div class="txt">獻給每一個努力生活的你</div>
		</div> -->
		<!-- <div class="text-container">
			<div class="line">啟動中...</div>
			<div class="line">加載首頁菜單...</div>
			<div class="line">獲取用戶登入狀態...</div>
			<div class="line">獲取用戶信息⋯</div>
			<div class="line">獲取配置文件...</div>
			<div class="line">加載底部tab..</div>
			<div class="line">加載完成...</div>
		</div> -->
		<div style="margin-top: .5rem;">
			<div class="txt" v-for="initTxt in initArr">{{ initTxt }}</div>
		</div>

		<!-- <div class="load flex flex-c">
			<img  src="../../assets/v2/loading.png" style="width: 0.31rem;height: 0.31rem;" alt=""  />
		</div> -->
		<!-- <img class="img" src="../../assets/v2/bg.png" style="width: 100%;height: 3.74rem;" alt="" /> -->
	</div>
</template>

<script>
	export default {
		name: "start",
		props: {},
		data() {
			return {
				initArr: [],
				num: 0
			};
		},
		components: {},
		mounted() {
			this.setInt();
		},
		methods: {
			setInt() {
				let _this = this;
				let arr = ['開始更新.............', '檔案更新伺服器 連線中......', 'TFN機房 連線中...', 
				'台灣證券交易所（TWSE）機房 TCP連線成功', '會員中心伺服器 連線成功', '會員登入認證成功', '連接報價主機成功', '認證成功'];
				_this.initTxt = arr[_this.num];
				let int = setInterval(function () {
					_this.num += 1;
					_this.initArr.push(arr[_this.num]);
					if (_this.num === 9) {
					clearInterval(int);
					setTimeout(() => {
						_this.startTime();
					}, 1000);
					}
				}, 500);
			},

			startTime() {
				if (localStorage.getItem("tokend") && localStorage.getItem("account")) {
					this.$toPage("/home/<USER>");
				} else {
					this.$toPage("/login/login");
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.text-container {
		display: flex;
		justify-content: end;
		flex-direction: column;
		width: 100%;
		position: absolute;
		left: 0.2rem;
		top: 50%;
		transform: translateY(-50%);
		z-index: 999;
		.line {
			margin: 0;
			opacity: 0;
			font-size: 0.16rem;
			color: #000;
			font-weight: bold;
			margin-bottom: 0.15rem;
			animation: showLine 1s ease forwards;
		}
	}

	@keyframes showLine {
		0% {
			opacity: 0;
			transform: translateY(0.05rem);
		}

		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}
	@keyframes show {
		0% {
			opacity: 0;
		}
	
		100% {
			opacity: 1;
		}
	}
	@keyframes roate {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	/* 设置每行的延迟，以便逐行显示 */
	.line:nth-child(1) {
		animation-delay: 0.5s;
	}

	.line:nth-child(2) {
		animation-delay: 1s;
	}

	.line:nth-child(3) {
		animation-delay: 1.5s;
	}

	.line:nth-child(4) {
		animation-delay: 2s;
	}

	.line:nth-child(5) {
		animation-delay: 2.5s;
	}

	.line:nth-child(6) {
		animation-delay: 3s;
	}

	.line:nth-child(7) {
		animation-delay: 3.5s;
	}

	.page {
		width: 100vw;
		min-height: 100vh;
		background: url('../../assets/newbg.png') no-repeat;
		background-size: 100%;
		padding: 2rem 0 0;
		flex-direction: column;
		.img{
			position: absolute;
			bottom: 0;
			left: 0;
		}
		.txt {
			animation: showLine 1s ease forwards;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #fff;
			margin-left: .3rem;
			margin-top: .1rem;
			
		}
		.load{
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%,-50%);
			z-index: 999;
			width: 0.89rem;
			height: 0.89rem;
			background: rgba(0, 0, 0, 0.5);
			border-radius: 0.11rem;
			animation: show 2s ease forwards;
			img{
				animation: roate 1.5s infinite linear;
			}
		}
		.txt1 {
			font-size: 0.16rem;
			color: #59a082;
		}
	}
</style>