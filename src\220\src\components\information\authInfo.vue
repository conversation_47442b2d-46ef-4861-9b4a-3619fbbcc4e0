<template>
	<div class="page ">
		<top-back :title="$t('mine').menu4"></top-back>

		<template v-if="isEdit==true">
			<div class="editing">
				<div class="top" v-if="false">
					<div class="icon rzwc animate__animated animate__fadeIn"></div>
					<!-- <div class="t"> {{ $t("new").a8 }} </div> -->
				</div>

				<div class="bg">
					<div class="title" v-if="false">{{ $t("请按以下资料完成实名认证") }}</div>
					<!-- <div class="title1">{{ $t("完善以下资料并通过认证") }}</div> -->
					<div class="wbg">
						<div class="ipt flex">
							<div class="icon zh"></div>
							<!-- <div class="t1">{{ $t("new").a9 }}</div> -->
							<input v-model="form.true_name" placeholder-style="color: #999" :placeholder="$t('new').a10" />
						</div>
						<div class="ipt flex">
							<div class="icon sfz"></div>
							<!-- <div class="t1">{{ $t("new").a11 }}</div> -->
							<input v-model="form.card_id" placeholder-style="color: #999" :placeholder="$t('new').a12" />
						</div>
					</div>
					
					

					<div class="upload">
						<div class="t">
							{{ $t("new").a13 }}
						</div>
						<div class="flex flex-b" style="padding: 0.12rem 0.12rem 0.16rem;">
							<div class="item flex-column-item">
								<div class="up-item" v-if="!showFrontcard">
									<div class="icon sf animate__animated animate__fadeIn"></div>
								</div>
								<img class="animate__animated animate__fadeIn" v-if="showFrontcard" :src="showFrontcard" />
								<div class="t1 flex flex-c" v-if="!showFrontcard">{{ $t("new").a14 }}</div>
								<input class="inp" accept="image/*" type="file" @change="uploadFile($event, 1)" />
							</div>
							<div class="item flex-column-item">
								<div class="up-item" v-if="!showBackcard">
									<div class="icon sf animate__animated animate__fadeIn"></div>
								</div>
								<img class="animate__animated animate__fadeIn" v-if="showBackcard" :src="showBackcard" />
								<div class="t1 flex flex-c" v-if="!showBackcard">{{ $t("new").a15 }}</div>
								<input class="inp" accept="image/*" type="file" @change="uploadFile($event, 2)" />
							</div>
						</div>
					</div>

					<div class="big_btn animate__animated animate__fadeIn" @click="submit">
						{{ $t("new").a16 }}
					</div>

					<div class="tips">
						{{ $t("bandTips") }}
					</div>
				</div>
			</div>
		</template>

		<template v-if="isEdit==false">
			<div class="edited">
				<div class="top">
					<div class="icon rzwc animate__animated animate__fadeIn"></div>
					<div class="t">
						{{ userInfo.is_true == 1 ? $t("new").a17 : $t("mine").name2 }}
					</div>
				</div>
				<div class="bg" v-if="false">
					<div>
						<div class="t1">{{ $t("new").a9 }}</div>
						<div class="ipt">{{ form.true_name || "-" }}</div>
					</div>

					<div>
						<div class="t1">{{ $t("new").a11 }}</div>
						<div class="ipt">{{ form.card_id || "-" }}</div>
					</div>

					<div class="tips">
						{{ $t("bandTips") }}
					</div>
				</div>
			</div>
		</template>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "authInfo",
		props: {},
		data() {
			return {
				isEdit: null,
				showFrontcard: "",
				showBackcard: "",
				form: {
					frontcard: "",
					backcard: "",
					true_name: "",
					card_id: "",
				},
				userInfo: {},
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {},
		methods: {
			initData() {
				this.$server.post("/user/getUserinfo").then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						if (res.data.frontcard) {
							this.showFrontcard = this.$server.url.imgUrls + res.data.frontcard;
							this.showBackcard = this.$server.url.imgUrls + res.data.backcard;
						}
						this.form.true_name = res.data.realname;
						this.form.card_id = res.data.id_card;

						//0未认证 1已实名 2审核失败 3审核中
						if (res.data.is_true == 1 || res.data.is_true == 3) {
							this.isEdit = false;
						}
						if (res.data.is_true == 0 || res.data.is_true == 2) {
							this.isEdit = true;
						}
					}
				});
			},
			submit() {
				if (!this.form.true_name) {
					this.$toast(this.$t("new").a18);
					return;
				}
				if (!this.form.card_id) {
					this.$toast(this.$t("new").a19);
					return;
				}

				if (!this.form.frontcard || !this.form.backcard) {
					this.$toast(this.$t("new").a20);
					return;
				}
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/user/shiming", {
						id_card: this.form.card_id,
						realname: this.form.true_name,
						frontcard: this.form.frontcard,
						backcard: this.form.backcard,
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							this.initData();
						}
					});
			},
			uploadFile(e, type) {
				var file = e.target.files[0];
				var that = this;
				var formdata = new FormData();
				formdata.append("card", file);
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/common/upload1", formdata)
					.then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast(this.$t("new").a21);
							if (type == 1) {
								// 正面
								this.showFrontcard = this.$server.url.imgUrls + res.data; //显示用
								this.form.frontcard = res.data; //提交用
							} else {
								// 反面
								this.showBackcard = this.$server.url.imgUrls + res.data;
								this.form.backcard = res.data;
							}
						}
					})
					.catch((data) => {});
			},
		},
	};
</script>

<style scoped lang="less">
	.inp {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		opacity: 0;
	}

	.page {
		padding: 0.6rem 0rem 0;

		.edited {
			.top {
				padding: 0.9rem 0;
				.icon {
					margin: 0 auto 0.1rem;
				}
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.16rem;
					color: #333333;
					text-align: center;
				}
			}

			.t1 {
				font-weight: 600;
				font-size: 0.14rem;
				color: #0e1028;
				margin-bottom: 0.05rem;
			}

			.ipt {
				background: #f8f8f8;
				border-radius: 0.04rem;
				padding: 0.15rem 0.1rem;
				margin-bottom: 0.2rem;
			}
		}

		.editing {
			margin: 0 0.12rem;
			.title {
				font-size: 0.14rem;
				color: #000000;
				margin-bottom: 0.2rem;
				font-weight: 600;
			}

			.title1 {
				margin: 0.05rem 0 0.2rem;
				font-size: 0.12rem;
				color: #7f7f7f;
			}

			.top {
				padding: 0.3rem 0;

				.icon {
					margin: 0 auto 0.15rem;
				}

				.t {
					text-align: center;
					font-weight: 500;
					font-size: 0.16rem;
					color: #444444;
				}
			}
			.wbg{
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.2rem 0rem rgba(255,255,255,0.35);
				border-radius: 0.07rem;
				padding: 0 0.12rem;
			}
			.ipt {
				padding: 0.14rem 0;
				border-bottom: 0.01rem solid #E4EAF1;
				&:last-child{
					border-bottom: none;
				}
				.t1 {
					font-weight: 600;
					font-size: 0.14rem;
					color: #0e1028;
					margin-bottom: 0.05rem;
				}

				input {
					margin-left: 0.1rem;
					width: 100%;
					background: transparent;
					&::placeholder {
						font-weight: 500;
						font-size: 0.12rem;
						color: #b6b6b6;
					}
				}
			}

			.upload {
				margin-top: 0.1rem;
				// padding: 0.1rem 0;
				background: #FFFFFF;
				border-radius: 0.13rem;
				.t {
					padding: 0.12rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.15rem;
					color: #0C061C;
					border-bottom: 0.01rem solid #E4EAF1;
				}

				.item {
					width: 48%;
					position: relative;
					text-align: center;
					height: 1.01rem;
					background: #FFFFFF;
					border-radius: 0.04rem;
					border: 0.01rem dashed #CBCBCB;
					justify-content: center;
					.up-item {
						position: relative;
						overflow: hidden;
					}
					img {
						width: 1.6rem;
						height: 1rem;
						border-radius: 0.06rem;
					}

					.icon {
						margin: 0 auto;
					}

					.t1 {
						margin-top: 0.08rem;
						font-size: 0.13rem;
						color: #999;
					}
				}
			}

			.b-btn {
				margin: 0.2rem 0;
			}
		}

		.tips {
			font-size: 0.12rem;
			color: #7f7f7f;
			line-height: 0.24rem;
		}
	}
</style>