<template>
	<div class="page ">
		<top-back title="搜尋"></top-back>

		<div class="cot">
			<div class="top">
				<div class="flex flex-b  search">
					<div class="icon sou animate__animated animate__fadeIn"></div>
					<input class="flex-1" @input="handleInput" type="text" v-model="keyword" placeholder="股票名稱/股票代碼" />
				</div>
			</div>

			<!-- 历史搜索 -->
			<div class="keyword-block" v-if="!!oldKeywordList.length">
				<div class="flex flex-b">
					<div class="keyword-list-header">搜尋歷史</div>
					<div @click="clearHistory">
						<!-- <van-icon name="close" size=".2rem" /> -->
						<div class="icon del animate__animated animate__fadeIn"></div>
					</div>
				</div>
				<div class="keyword">
					<div v-for="(keyword, index) in oldKeywordList" @click="doSearch(keyword)" :key="index">
						{{ keyword }}
					</div>
				</div>
			</div>

			<div class="rm-list" v-show="keyword" v-if="listData.length > 0">
				<!-- 股票列表 -->
				<div class="">
					<div class="titles flex flex-b">
						<div class="flex-1">名稱</div>
						<div class="flex-1 t-c">價格</div>
						<div class="flex-1 t-c">漲跌</div>
						<div style="width:5%"></div>
					</div>

					<div class="list">
						<div class="rm-item flex flex-b" v-for="(item, idx) in listData" :key="idx" @click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
							<div class="flex flex-1">
								<div>
									<div class="name">
										{{ $t(item.local_name) }}
									</div>
									<div class="code">
										{{ $t(item.symbol) }}
									</div>
								</div>
							</div>
							<div class="price flex-1 t-c red" :class="{green: Number(item.gain) < 0,}">{{ $formatMoney(item.price) || 0 }}
							</div>
							<div class="flex-1">
								<div class="t-c per flex flex-c red" :class="{green: Number(item.gain) < 0,}">
									<div class="icon animate__animated animate__fadeIn"
										:class="Number(item.gain) > 0 ? 'up' : 'down'"></div>

									{{ $formatMoney(item.gainValue) || "-" }}
								</div>

								<div class="per flex flex-c" :class="Number(item.gain) > 0 ? 'red' : 'green'">
									{{ item.gain }}%
								</div>
							</div>

							<div style="width:5%" class="flex flex-e animate__animated animate__fadeIn"
								@click.stop="optional(item)">
								<van-icon v-if="!item.isZx" name="add-o" color="#078bde" size="20" />
								<van-icon v-if="item.isZx" name="close" color="#078bde" size="20" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "search",
		props: {},
		data() {
			return {
				marketType: 'taiwan', // 默认台湾市场
				currmentIndex: 0,
				keyword: "",
				imgList: {
					search: "",
					delete: "",
					hook: "",
					jia: "",
				},
				oldKeywordList: [],
				listData: [],
				flag: true,
				account: "",
			};
		},
		computed: {
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.marketType === 'taiwan' ? 'twd' : 'usd';
			}
		},
		components: {},
		created() {
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.marketType = 'us';
			} else {
				this.marketType = 'taiwan';
			}

			this.account = this.$storage.get("account");
			this.oldKeywordList = !!this.$storage.get(this.account + "searchHistory") ?
				this.$storage.get(this.account + "searchHistory") :
				[];
		},
		mounted() {
			// 使用 lodash 的防抖函数
			this.handleInput = _.debounce(this.handleInput, 1000);
		},
		methods: {
			handleInput() {
				this.searchFn();
			},
			changePage(index) {
				this.currmentIndex = index;
			},
			optional(obj) {
				this.$refs.loading.open(); //开启加载
				if (!obj.isZx) {
					this.$server
						.post("/user/addOptional", {
							type: this.apiMarketType,
							symbol: obj.symbol
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.listData = this.listData.map((item) => {
									if (item.symbol == obj.symbol) {
										item.isZx = true;
									}

									return item;
								});
							}
						});
				} else {
					this.$server
						.post("/user/removeOptional", {
							type: this.apiMarketType,
							symbol: obj.symbol
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.listData = this.listData.map((item) => {
									item.isZx = false;
									return item;
								});
							}
						});
				}
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: this.apiMarketType
				}).then((res) => {
					if (res.status == 1) {
						// 判断当前是否在自选列表里面
						let arr = this.listData.map((item) => {
							res.data.forEach((it) => {
								if (item.symbol == it.symbol) {
									item.isZx = true;
								}
							});
							return item;
						});

						this.listData = arr;
					}
				});
			},
			doSearch(str) {
				this.keyword = str;
				this.searchFn();
			},
			clearHistory() {
				this.oldKeywordList = [];
				this.$storage.remove(this.account + "searchHistory");
			},
			searchFn() {
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/search", {
						type: this.apiMarketType,
						content: this.keyword
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							if (res.data.length == 1) {
								let searchHistory = this.oldKeywordList;
								if (searchHistory.length > 10) {
									searchHistory.shift();
								}
								if (!searchHistory.includes(this.keyword)) {
									searchHistory.push(this.keyword);
								}

								this.$storage.save(this.account + "searchHistory", searchHistory);

								this.oldKeywordList = searchHistory;
							}
							this.listData = [];
							if (res.data.length > 0) {
								this.listData = res.data;
								// 判断是否自选
								this.getMine();
							}
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.rm-list {
		.titles {
			padding: 0.05rem 0;

			div {
				font-size: 0.12rem;
				color: #787878;
			}

			.icon {
				margin-left: 0.05rem;
			}
		}

		.rm-item {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #dedede;

			div {
				font-size: 0.12rem;
			}

			.name {
				color: #000000;
			}

			.code {
				font-size: 0.11rem;
				color: #909090;
				margin-top: 0.05rem;
			}

			.per {
				.icon {
					margin-right: 0.05rem;
				}
			}
		}
	}

	.page {
		padding: 0.5rem 0 0;
		min-height: 100vh;
	}

	.red {
		color: #da5b4c !important;
	}

	.green {
		color: #2cc489 !important;
	}

	.cot {
		padding: 0.1rem 0.15rem;
		background: #fff;
		min-height: 92vh;
		.top {
			.search {
				border-radius: 0.3rem;
				background: #f3f3f3;
				height: 0.3rem;
				padding: 0 0.1rem;

				input {
					font-size: 0.14rem;
					color: #000;
					margin-left: 0.1rem;

					&::placeholder {
						font-size: 0.12rem;
						color: #8f8f8f;
					}
				}
			}
		}

		.keyword-block {
			padding: 0.15rem 0 0;

			.keyword-list-header {
				font-size: 0.14rem;
				font-weight: 500;
				color: #000;
			}

			.keyword {
				display: flex;
				flex-flow: wrap;
				align-items: center;
				padding: 0.1rem 0;

				div {
					background: #f3f3f3;
					border-radius: 0.06rem;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0.05rem 0.1rem;
					font-size: 0.12rem;
					color: #000000;
					margin: 0 0.1rem 0.1rem 0;
				}
			}
		}
	}

	.box {
		.titles {

			// padding: 0.1rem 0 0;
			div {
				font-size: 0.12rem;
				color: #535353;
			}
		}

		.list {
			.list-item {
				padding: 0.1rem 0;
				border-bottom: 0.01rem solid #f4f4f4;

				.name {
					font-size: 0.12rem;
					color: #000000;
				}

				.code {
					font-size: 0.1rem;
					color: #c4c4c4;
				}

				.price {
					font-size: 0.12rem;
					color: #0c0c0c;
					text-align: center;
				}

				.per {
					margin-right: 0.05rem;

					.t {
						font-size: 0.12rem;
						color: #0c0c0c;

						&.t1 {
							margin-left: 0.05rem;
						}
					}
				}
			}
		}

		.red {
			color: #c04649;
		}

		.green {
			color: #4f8672;
		}
	}
</style>