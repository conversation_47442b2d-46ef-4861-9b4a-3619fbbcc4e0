<template>
	<div class="page ">
		<top-back :title="$t('capitalChannel').title"></top-back>

		<div class="money animate__animated animate__fadeIn text-center">
			<div class="" v-if="false">
				<div class="t"> {{ $t("newt").a76 }} </div>
				<div class="t1">{{$currency}}{{ $formatMoney(totalAssets) }} </div>
			</div>
			<div class="flex flex-b">
				<div class="flex-1">
					<div class="t"> {{ $t("可用资金") }} </div>
					<div class="t2">{{$currency}}{{ $formatMoney(userInfo.Germany) }} </div>
				</div>
				<div class="flex-1" v-if="false">
					<div class="t"> {{ $t("冻结资金") }} </div>
					<div class="t2">{{$currency}}{{ $formatMoney(freezeAssets) }} </div>
				</div>
			</div>
		</div>

		<div class="cot">
			<div class="flex  flex-b money1">
				<div class="t">{{ $t("capitalChannel").txt4 }}</div>
				<div class="t1">{{ money }}</div>
			</div>
			<!-- v-if="logList.length > 1" -->
			<div class="nav-box flex flex-b">
				<div class="nav-item" v-for="(item, index) in logList" :key="index"
					:class="{ active: currmentIndex == index }" @click="changeNav(item, index)">
					{{ $t("capitalChannel").tit1 }}{{ index + 1 }}
				</div>
			</div>

			<div class="list" v-if="logList[currmentIndex]">
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">{{ $t("capitalChannel").txt1 }}</div>
						<div class="t1">{{ logList[currmentIndex].name }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn" @click="copy(logList[currmentIndex].name)">
					</div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">{{ $t("capitalChannel").txt2 }}</div>
						<div class="t1">{{ logList[currmentIndex].bankcard }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn"
						@click="copy(logList[currmentIndex].bankcard)"></div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">{{ $t("capitalChannel").txt3 }}</div>
						<div class="t1">{{ logList[currmentIndex].bankname }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn"
						@click="copy(logList[currmentIndex].bankname)"></div>
				</div>
				<!-- 上传凭证 -->
				<div class="upload animate__animated animate__fadeIn">
					<div class="up-box flex flex-c">
						<div v-if="!showFrontcard">
							<div class="t">+</div>
							<div class="t1">{{ $t("new").a47 }}</div>
						</div>
						<img v-if="showFrontcard" :src="showFrontcard" />
						<input class="inp" accept="image/*" type="file" @change="uploadFile($event)" />
					</div>
				</div>

				<div class="big_btn animate__animated animate__fadeIn" @click="chongzhi">
					{{ $t("new").a30 }}
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "rechargeChannel",
		props: {},
		data() {
			return {
				money: "",
				type: 0,
				logList: [{
					name: "name",
					bankcard: "bankcard",
					bankname: "bankname",
				}, ],
				currmentIndex: 0,
				showFrontcard: "",
				form: {
					frontcard: "",
				},
				userInfo: {},
				totalAssets: 0, //总资产
				freezeAssets: 0, //冻结资金
			};
		},
		components: {},
		created() {
			this.money = this.$route.query.money;
			this.type = this.$route.query.type;
			this.initData();
			this.getUserInfo();
			this.getTotalAssets();
		},
		computed: {},
		methods: {
			initData() {
				let per = {
					type: this.$stockType,
				};
				if (this.type == 1) {
					per = {
						type: "usd"
					};
				}
				this.$server.post("/common/recharge_channel", per).then((res) => {

					let arr = [];
					for (let key in res.data) {
						let obj = res.data[key];

						if (obj.name) {
							if (obj.name.indexOf(":") > -1) {
								let arr = obj.name.split(":");
								obj.title = arr[0];
								obj.name = arr[1];
							}
							arr.push(obj);
						}
					}
					this.logList = arr;
					// this.logList = this.logList.filter((item, index) => index < 4);
				});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			changeNav(item, index) {
				this.currmentIndex = index;
			},
			chongzhi() {
				if (!this.showFrontcard) {
					this.$toast(this.$t("new").a47);
					return;
				}

				let per = {};
				if (this.type == 0) {
					per = {
						money: this.money,
						type: this.$stockType,
					};
				}
				if (this.type == 1) {
					per = {
						money: this.money,
						type: this.$stockType,
					};
				}
				per.rjpz = this.form.frontcard;
				this.$refs.loading.open(); //开启加载

				this.$server.post("/user/chongzhi", per).then((res) => {
					this.$refs.loading.close();
					if (res.status == 1) {
						this.$toast(this.$t(res.msg));
						this.showFrontcard = "";
					}
				});
			},
			uploadFile(e) {
				var file = e.target.files[0];
				var that = this;
				var formdata = new FormData();
				formdata.append("card", file);
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/common/upload1", formdata)
					.then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast(this.$t("new").a21);
							// 正面
							this.showFrontcard = this.$server.url.imgUrls + res.data; //显示用
							this.form.frontcard = res.data; //提交用
						}
					})
					.catch((data) => {});
			},
			copy(text) {
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select(); // 选中文本内容
				textarea.setSelectionRange(0, text.length); // 设置选定区的开始和结束点
				this.$toast(this.$t("capitalChannel").tit3);
				var result = document.execCommand("copy"); //将当前选中区复制到剪贴板
				textarea.remove();
			},
			// 获取总资产
			async getTotalAssets() {
				//this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo");
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let krw = Number(res.data.Germany || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: this.$stockType,
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: this.$stockType,
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 日内交易的申请冻结 接口报错
				const res3 = await this.$server.post("/trade/urnjylist", {
					type: this.$stockType,
				});
				let dayDelFreeze = 0;
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					res3.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit));
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDelFreeze = total;
				}

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					page: 1,
					size: 300,
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: this.$stockType,
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 日内交易持仓
				const res6 = await this.$server.post("/trade/userstocklist", {
					type: this.$stockType,
					buy_type: 1,
				});
				let dayDealFreeze = 0;
				delete res6.data.ccsz;
				delete res6.data.fdyk;
				let dataArr1 = res6.data;
				if (dataArr1.length) {
					let arr = [];
					dataArr1.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					followingFreeze +
					dayDelFreeze +
					bigDealFreeze +
					positionFreeze +
					dayDealFreeze;
				// 总资产
				this.totalAssets = krw + this.freezeAssets;
				//this.$refs.loading.close();


			},
		},
	};
</script>

<style scoped lang="less">
	.inp {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		opacity: 0;
	}

	.page {
		padding: 0.6rem 0.12rem 0;
	}

	.money {
		padding: 0.2rem 0;

		.t {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #999999;
		}

		.t1 {
			font-weight: 500;
			font-size: 0.18rem;
			color: #272727;
		}

		.t2 {
			margin-top: 0.1rem;
			font-family: OPPOSans, OPPOSans;
			font-weight: 600;
			font-size: 0.22rem;
			color: #333333;
		}

	}

	.cot {
		background: #FFFFFF;
		border-radius: .08rem .08rem 0px 0px;
		padding: 0 .12rem;

		.mt10 {
			margin-top: .1rem;
		}

		.money1 {
			border-bottom: 0.01rem solid #ececec;
			padding: .15rem 0;

			.t {
				font-size: 0.12rem;
				color: #aaaaaa;
			}

			.t1 {
				font-weight: 500;
				color: #333333;
			}
		}

		.nav-box {
			padding: .2rem 0 0;
			background: #fff;
			white-space: nowrap;
			overflow: scroll;
			border-bottom: 0.01rem solid #ececec;

			.nav-item {
				height: .28rem;
				padding: 0 .2rem;
				flex: 1;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #111111;
				text-align: center;

				&.active {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.17rem;
					color: #E10414;
					position: relative;

					&:after {
						content: '';
						display: block;
						width: .1rem;
						height: .03rem;
						background: #E10414;
						border-radius: .02rem;
						position: absolute;
						bottom: .03rem;
						left: 40%;
					}
				}
			}
		}

		.list {
			padding: 0.1rem 0;

			.item {
				padding: 0.1rem 0;
				margin-bottom: .2rem;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #999999;
					margin-right: 0.1rem;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #333333;
					margin-left: 0.05rem;
				}
			}
		}

		.upload {
			padding: 0.1rem 0 0;

			.up-box {
				width: 1.2rem;
				height: 1.2rem;
				position: relative;
				margin: 0 auto;
				border-radius: 0.04rem;
				border: 0.01rem solid #f1f1f1;

				text-align: center;

				.t {
					font-weight: bold;
					font-size: 0.2rem;
				}

				.t1 {
					font-weight: 500;
					font-size: 0.12rem;
					color: #353535;
				}

				img {
					width: 1rem;
					height: 1rem;
				}
			}
		}
	}
</style>