<template>
	<div class="page trade">
		<!-- <top-index></top-index> -->
		<div class="header">
			<div class="search flex" @click="$toPage('/favorite/search')">
				<img src="../../assets/home/<USER>" />
				{{$t('股票名称')}}/{{$t('代码')}}
			</div>
      <div class="nav-box flex">
        <div class="nav-item" v-for="(item, index) in navList2" :key="index"
             :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
          {{ item.name }}<span></span>
        </div>
      </div>
			<!-- 切换列表内容显示 -->
			<div class="tabs">
				<div class="tabBox flex">
					<div class="tab-item flex flex-c" v-for="(item, i) in navList" :key="i"
						@click="changeNav(item.type)" :class="{ active: currmentIndex == item.type }">
						{{ item.name }}
					</div>
				</div>
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<!-- 收益-->
			<template v-if="currmentIndex==1||currmentIndex==2">
				<div class="money">

					<div class="nums flex flex-b">
						<div class="item">
							<div class="t2"> {{ $t("市值") }} </div>
							<div class="t1" :class="$formatMoney(szAssets)>=0?'red':'green'">
								{{ show ? $formatMoney(szAssets) || 0 : "****" }}
							</div>
						</div>
						<div class="item">
							<div class="t2"> {{ $t("newt").t80 }} </div>
							<div class="t1">
								{{ show ? $formatMoney(userInfo.try) || 0 : "****" }}
							</div>
						</div>
						<div class="item">
							<div class="t2"> {{ $t("newt").t79 }} </div>
							<div class="t1" :class="ykAssets>=0?'red':'green'">
								{{ show ? $formatMoney(ykAssets,2) || 0 : "****" }}
							</div>
						</div>
					</div>
				</div>
			</template>

			<!-- 申购 -->
			<div class="sg-list" v-if="currmentIndex === 0">
				<van-skeleton title :row="26" :loading="loading1">
					<no-data v-if="!xinguList.length"></no-data>
					<div class="sg-item" v-for="(item, index) in xinguList" :key="index">
						<div class="top flex flex-b">
							<div class="name flex">
								<div>{{ item.stock_name || "-" }}</div>
								<span>{{ item.stock_code || "-" }}</span>
							</div>
							<div class="status animate__animated animate__fadeIn">
								<img  src="../../assets/v2/jxz.png" style="width: 0.26rem;height: 0.06rem;margin-right: 0.05rem;"/>
								{{ $t(item.xgstate) }}
							</div>
						</div>

						<div class="data flex flex-b">
							<div class="data-item">
								<div class="t1 price">
									{{ $formatMoney(item.apply_price) || "-" }}
								</div>
								<div class="t">{{ $t("申购量") }}</div>
							</div>
							<div class="data-item">
								<div class="t1">
									{{ $formatMoney(item.lucky_total) || "-" }}
								</div>
								<div class="t">{{ $t("中签") }}</div>
							</div>
							<div class="data-item">
								<div class="t1">
									{{ $formatMoney(item.apply_total) || "-" }}
								</div>
								<div class="t">{{ $t("newt").t83 }}</div>
							</div>
							<!-- <div class="data-item flex flex-b">
								<div class="t">{{ $t("newt").t84 }}</div>
								<div class="t1">
									{{ $formatMoney(item.subs_value) || 0 }}
								</div>
							</div> -->
							<div class="data-item">
								<div class="t1">{{ $formatMoney(item.rjmoney) || 0 }}</div>
								<div class="t">{{ $t("已认缴") }}</div>
							</div>
							<!-- <div class="data-item flex flex-b">
								<div class="t">{{ $t("newt").t88 }}</div>
								<div class="t1">{{ $formatMoney(item.rjmoney) || 0 }}</div>
							</div> -->
							<div class="data-item">
								<div class="t1">
									{{ $formatMoney(item.subs_value - item.rjmoney) || 0 }}
								</div>
								<div class="t">{{ $t("需认缴") }}</div>
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>

			<!-- 进行中 -->
			<div class="cy-list" v-if="currmentIndex === 1">
				<van-skeleton title :row="26" :loading="loading3">
					<no-data v-if="!positionList.length"></no-data>
					<div class="cy-title flex flex-b" v-else>
						<div class="flex-1">{{$t('股票名称')}}</div>
						<div class="flex-1 text-center">{{$t('现价')}}</div>
						<div class="flex-1 text-right">{{$t('成本')}}</div>
					</div>
					<div class="cy-item" v-for="(item, index) in positionList" :key="index" @click="sellItem(item)">
						<div class="flex flex-b cy-top">
							<div class="name">
								<div>{{ item.stock_name }}</div>
								<span>{{ item.stock_code }}</span>
							</div>
							<div class="price text-center">
								<div>{{ $formatMoney(item.buy_price,2) }}</div>
								<span>{{ $t("持有") }}{{ $formatMoney(item.stock_num) }}</span>
							</div>
							<div class="price text-right">
								<div>{{ $formatMoney(item.buy_price * item.stock_num) }}</div>
								<span style="color: #E10414;">{{$t('市值')}}
									{{ $formatMoney( parseFloat(item.market_value) + parseFloat(item.yingkui) ) }}</span>
							</div>
						</div>

						<div class="inner" v-if="false">
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t87 }}</div>
								<div class="t2">
									{{ $formatMoney(item.buy_price) }}
								</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t88 }}</div>
								<div class="t2">{{ $formatMoney(item.stock_num) }}</div>
							</div>

							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t89 }}</div>
								<div class="t2">{{ $formatMoney(item.nowprice) }}</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t78 }}</div>
								<div class="t2">
									{{ $formatMoney( parseFloat(item.market_value) + parseFloat(item.yingkui) ) }}
								</div>
							</div>
						</div>

						<div class="cy-bottom flex flex-b"
							:class="{ red: parseFloat(item.yingkui) >= 0, green: parseFloat(item.yingkui) < 0, }">
							<div class="per">
								{{$t('new').a75}}<span :class="item.gain>=0?'red':'green'">{{stockType=='try'?'%':''}}{{ parseFloat(item.gain).toFixed(2) }}{{stockType=='try'?'':'%'}}</span>
								<img src="../../assets/v2/gline.png"
									style="width: 0.38rem;height: 0.13rem;margin-left: 0.1rem;" v-if="item.gain>=0"
									alt="" />
								<img src="../../assets/v2/rline.png"
									style="width: 0.38rem;height: 0.13rem;margin-left: 0.1rem;" v-else alt="" />
							</div>
							<div class="status">{{$t('持仓中')}}</div>
						</div>

						<div class="bts flex flex-b" v-if="false">
							<div class="bt" @click="goItem(detailItem)">{{ $t("详情") }}</div>
							<div class="bt bt1" @click="sellItem(item)">
								{{ statusStr[item.status] }}
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
			<!-- 已终止 -->
			<div class="cy-list" v-if="currmentIndex === 2">
				<van-skeleton title :row="26" :loading="loading3">
					<no-data v-if="!positionCloseList.length"></no-data>
					<div class="cy-item" v-for="(item, index) in positionCloseList" :key="index" @click="goItem(item)">
						<div class="cy-title flex flex-b">
							<div class="flex-1">{{$t('股票名称')}}</div>
							<div class="flex-1 text-center">{{$t('卖价')}}</div>
							<div class="flex-1 text-right">{{$t('成本')}}</div>
						</div>
						<div class="flex flex-b cy-top"
							:class="{ red: parseFloat(item.yingkui) >= 0, green: parseFloat(item.yingkui) < 0, }">
							<div class="name">
								<div>{{ item.stock_name }}</div>
								<span>{{ item.stock_code }}</span>
							</div>
							<div class="price text-center">
								<div>{{ $formatMoney(item.sell_price,2) }}</div>
								<span>{{ $t("持有") }}{{ $formatMoney(item.stock_num) }}</span>
							</div>
							<div class="price text-right">
								<div>{{ $formatMoney(item.buy_price * item.stock_num) }}</div>
								<span>{{$t('市值')}}
									{{ $formatMoney( parseFloat(item.market_value) + parseFloat(item.yingkui) ) }}</span>
							</div>
							<!-- <div>
								<div class="t">{{ item.gain.toFixed(2) }}%</div>

								<div class="t1">
									{{ Number(item.yingkui) > 0 ? "+" : "" }}{{ isNaN(Number(item.yingkui).toFixed(2)) ? 0 : $formatMoney(item.yingkui) }}
								</div>
							</div> -->
						</div>

						<div class="inner" v-if="false">
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t92 }}</div>
								<div class="t2">
									{{ $formatMoney(item.sell_price) }}
								</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t93 }}</div>
								<div class="t2">{{ $formatMoney(item.stock_num) }}</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t87 }}</div>
								<div class="t2">{{ $formatMoney(item.buy_price) }}</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t("newt").t78 }}</div>
								<div class="t2">
									{{ $formatMoney( parseFloat(item.market_value) + parseFloat(item.yingkui) ) }}
								</div>
							</div>
						</div>

						<div class="cy-bottom flex flex-b"
							:class="{ red: parseFloat(item.yingkui) >= 0, green: parseFloat(item.yingkui) < 0, }">
							<div class="per">
								{{$t('new').a75}}{{stockType=='try'?'%':''}}{{ parseFloat(item.gain).toFixed(2)}}{{stockType=='try'?'':'%'}}
							</div>
							<div class="status end">
								<span>{{$t('已平仓')}}</span>
							</div>
						</div>

						<div class="bts flex flex-b" @click="goItem(detailItem)" v-if="false">
							<div class="bt">{{ $t("详情") }}</div>
							<div class="bt bt1">
								{{ statusStr[item.status] }}
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>

			<!-- 跟单记录 -->
			<div class="" v-if="currmentIndex === 3">
				<van-skeleton title :row="26" :loading="loading2">
					<div class="gd-list">
						<no-data v-if="!gdData.length"></no-data>
						<div class="gd-item" v-for="(item, index) in gdData" :key="index" v-if="gdData.length">
							<div class="top">
								<div class="flex flex-b">
									<div class="name flex">
										<div>{{ item.name || "-" }}</div>
										<span>({{ item.sender || "-" }})</span>
									</div>
									<div class="shouyi flex" :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
										<div> {{ $t("new").a65 }} </div>
										<span :class="item.dprofit>=0?'red':'green'"> {{ item.dprofit || "-" }} </span>
									</div>
								</div>

								<div class="flex flex-b">
									<!-- 时间 -->
									<div class="time flex flex-b flex-1">
										<div class="flex-1 flex">
											<div class="icon tm animate__animated animate__fadeIn"></div>
											<span>{{ $t("new").txt16 }}</span>
											{{ $formatDate("MM-DD hh:mm:ss",item.buy_time) }}
										</div>
										<div class="flex-1 flex flex-e">
											<div class="icon suo animate__animated animate__fadeIn"></div>
											<span class="">{{ $t("fllow").txt13 }}</span>
											{{ item.locktime||'-' }}
										</div>
									</div>
									<!-- <div class="sylb flex" @click="changeGdshow(index)">
									  {{ $t("new").b44 }}
									  <div
										class="icon animate__animated animate__fadeIn"
										:class="item.isShow ? 's1' : 'x1'"
									  ></div>
									</div> -->
								</div>
							</div>

							<div class="inner flex flex-b">
								<div class="inner-item">
									<div class="t1">{{ $t("买入金额") }}</div>
									<div class="t2">
										{{ $formatMoney(item.money) }}
									</div>
								</div>
								<div class="inner-item">
									<div class="t1">{{ $t("总盈利") }}</div>
									<div class="t2">{{ $formatMoney(item.aprofit) }}</div>
								</div>

								<div class="inner-item">
									<div class="t1">{{ $t("每日盈利") }}</div>
									<div class="t2">{{ $formatMoney(item.dprofit) }}</div>
								</div>
								<!-- <div class="inner-item">
								  <div class="t1">{{ $t("策略单号") }}</div>
								  <div class="t2">
									{{ item.strategy_num || "-" }}
								  </div>
								</div> -->
							</div>

							<!-- 内层列表 v-if="item.isShow"-->
							<template>
								<div class="inner-list flex flex-b flex-wrap" v-if="item.allstock && item.allstock.length">
									<!-- <div class="flex flex-b title">
										<div class="">{{ $t("new").a66 }}</div>
										<div class="t-r">{{ $t("new").a67 }}</div>
									</div> -->
									<div class="inner-item flex" v-for="(items, i) in item.allstock" :key="i" v-if="items&&i<2">
										<div class="flex">
											<div class="point"></div>
											<div class="t1">{{ items.symbol || items.code || "-" }}</div>
										</div>
										<div class="t-r" :class="items.gain < 0 ? 'green' : 'red'">
											{{ items.gain > 0 ? "+" : "" }}{{ items.gain }}
										</div>
									</div>
									<div class="inner-item flex" v-for="(items, i) in item.allstock" :key="i+'a'" v-if="i>1&&showGd">
										<div class="flex">
											<div class="point"></div>
											<div class="t1">{{ items.symbol || items.code || "-" }}</div>
										</div>
										<div class="t-r" :class="items.gain < 0 ? 'green' : 'red'">
											{{ items.gain > 0 ? "+" : "" }}{{ items.gain }}
										</div>
									</div>
								</div>
								
								<div class="flex flex-c" @click="showGd=false" v-if="showGd">
									<div style=" color: #006AFF;margin-right: 0.02rem;">{{$t('收起')}}</div>
									<div class="icon xl" style="transform: rotate(180deg);"></div>
								</div>
								<div class="flex flex-c" @click="showGd=true" v-else>
									<div style=" color: #006AFF;margin-right: 0.02rem;">{{$t('展开')}}</div>
									<div class="icon xl"></div>
								</div>
							</template>
						</div>
					</div>
				</van-skeleton>
			</div>

			<!-- 持仓中弹出层 -->
			<van-popup v-model="show1" position="center" :style="{ width: '90%' }" :round="true">
				<div class="popup">
					<div class="title">
						<img src="../../assets/market/positionImg.png" />
						<!-- <div class="name">{{ detailItem.stock_name }}</div>
						<div class="code">{{ detailItem.stock_code }}</div> -->
					</div>

					<div class="pdd">
						<div class="flex flex-c">
							<div class="">
								<div class="t num-font" :class="Number(detailItem.yingkui) >= 0?'red':'green'">
									{{ Number(detailItem.yingkui) > 0 ? "+" : "" }}{{ isNaN(Number(detailItem.yingkui)) ? 0 : $formatMoney(detailItem.yingkui) }}
								</div>
								<div class="t1">{{ $t("当前收益") }}</div>
							</div>
						</div>
						<div class="flex flex-b">
							<div class="bt" @click="goItem(detailItem)">
								{{ $t("position").txt2 }}
							</div>
							<div class="bt bt1" @click="sellstrategy(detailItem.id, detailItem.status)">
								{{ detailItem.status == 3 ? $t("position").txt23 : $t("position").txt1 }}
							</div>
						</div>
					</div>
					<div class="flex flex-c" @click="show1=false">
						<div class="icon close"></div>
					</div>
				</div>
			</van-popup>
		</van-pull-refresh>

		<tab-bar :current="3"></tab-bar>

		<loading ref="loading" />
	</div>
</template>

<script>
	import * as echarts from "echarts";
	import {
		change
	} from "../../assets/lang/kor";
	export default {
		name: "trade",
		props: {},
		data() {
			return {
        navList2: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
				chartData: [],
				chartData1: [],
				show: true,
				show1: false,
				myChart: null,
				statusStr: [
					this.$t("持仓中"),
					this.$t("已平仓"),
					this.$t("准备平仓"),
					this.$t("挂单中"),
				],

				loading1: true,
				loading2: true,
				loading3: true,
				loading4: true,
				isLoading: false,
				cfg: {},
				detailItem: {},

				look: true,
				positionList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	yingkui: 1000,
					// 	stock_num: 1000,
					// 	buy_price: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 3,
					// },
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	yingkui: 1000,
					// 	stock_num: 1000,
					// 	buy_price: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 3,
					// },
				],
				positionCloseList: [
					// {
					// stock_name: "stock_name",
					// stock_code: "stock_code",
					// xgstate: "审核中",
					// yingkui: 1000,
					// stock_num: 1000,
					// buy_price: 1000,
					// sell_time: "2024-03-02",
					// market_value: 1000,
					// sell_price: 100,
					// status: 3,
					// }, 
				],
				gdData: [
					// {
					// 	dprofit: "10%",
					// 	name: "name",
					// 	code: "code",
					// 	yprofit: "100%",
					// 	isShow: false,
					// 	allstock: [{
					// 		symbol: "symbol",
					// 		code: "code",
					// 		gain: 10,
					// 	}, 
					// 	{
					// 		symbol: "symbol",
					// 		code: "code",
					// 		gain: 10,
					// 	},
					// 	{
					// 		symbol: "symbol",
					// 		code: "code",
					// 		gain: 10,
					// 	},],
					// 	buy_time: "2024-03-02",
					// 	locktime: "2024-03-02",
					// },
					// {
					// 	dprofit: "10%",
					// 	name: "name",
					// 	code: "code",
					// 	yprofit: "100%",
					// 	isShow: false,
					// 	allstock: [{
					// 		symbol: "symbol",
					// 		code: "code",
					// 		gain: 10,
					// 	}, ],
					// 	buy_time: "2024-03-02",
					// 	locktime: "2024-03-02",
					// },
				],
				xinguList: [
					// {
					// stock_name: "stock_name",
					// stock_code: "stock_code",
					// xgstate: "审核中",
					// apply_price: 1000,
					// lucky_total: 1000,
					// apply_total: 1000,
					// rjmoney: 1000,
					// buy_time: "2024-03-02",
					// market_value: 1000,
					// status: 1,
					// }, 
					// {
					// stock_name: "stock_name",
					// stock_code: "stock_code",
					// xgstate: "审核中",
					// apply_price: 1000,
					// lucky_total: 1000,
					// apply_total: 1000,
					// rjmoney: 1000,
					// buy_time: "2024-03-02",
					// market_value: 1000,
					// status: 1,
					// }, 
				],
				navList: [{
						name: this.$t("new").a71,
						type: 0,
					},
					{
						name: this.$t("new").a72,
						type: 3,
					},
					{
						name: this.$t("new").a73,
						type: 1,
					},
					{
						name: this.$t("new").a74,
						type: 2,
					},
				],
				currmentIndex: 1,
				positionlistinteval: null,
				userInfo: {},
				szAssets: 0,
				ykAssets: 0,
				totalAssets: 0,
				totalProfit: 0,
				freezeAssets: 0,
				showGd:false
			};
		},
		components: {},
		computed: {},
		created() {
			//this.getConfig();
			this.initData();
			this.changeTime();
		},
		mounted() {
			// this.$refs.firstLoading.open();
			this.getTotalProfit();
			this.getTotalAssets();
		},
		beforeDestroy() {
			clearInterval(this.positionlistinteval);
		},
		methods: {
      changeStock(type){
        this.stockType = type
        this.initData();
        this.changeTime();
        this.getTotalProfit();
        this.getTotalAssets();
      },
			// 下拉刷新
			onRefresh() {
				this.getConfig();
				this.initData();
				this.getTotalProfit();
				this.getTotalAssets();
			},
			// 获取配置
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/login/logo.png");
				}
				this.cfg = val;
			},
			changeGdshow(index) {
				let arr = this.gdData.map((item, i) => {
					if (index == i) {
						item.isShow = !item.isShow;
					}
					return item;
				});
				this.gdData = arr;
			},
			getGd() {
				this.$server
					.post("/trade/userproductlist", {
						type: this.stockType
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading2 = false;
						/* res = {"status": 1,"msg": "查询成功","data": [{"id": 32,"strategy_num": "202406171416568034","agent_id": 399,"account": "999990","sender": "OAKRelay","name": "EquiBotics","money": "********.000","buy_time": **********,"dprofit": "0.000","aprofit": "0.000","pid": 4,"status": 0,"locktime": null,"scday": 40,"type": "usd","cj_money": "********.000","is_del": 0},{"id": 32,"strategy_num": "202406171416568034","agent_id": 399,"account": "999990","sender": "OAKRelay","name": "EquiBotics","money": "********.000","buy_time": **********,"dprofit": "0.000","aprofit": "0.000","pid": 4,"status": 0,"locktime": null,"scday": 40,"type": "usd","cj_money": "********.000","is_del": 0},{"id": 32,"strategy_num": "202406171416568034","agent_id": 399,"account": "999990","sender": "OAKRelay","name": "EquiBotics","money": "********.000","buy_time": **********,"dprofit": "0.000","aprofit": "0.000","pid": 4,"status": 0,"locktime": null,"scday": 40,"type": "usd","cj_money": "********.000","is_del": 0},]} */
						if (res.status == 1) {
							res.data.forEach((item) => {
								item.isShow = false;
								if (item.allstock && item.allstock.length) {
									item.allstock = item.allstock.filter(Boolean);
								}
							});

							this.gdData = res.data;
						}
					});
			},
			goItem(item) {
				this.$refs.loading.open(); //开启加载
				this.$storage.save("currentItem", item);
				setTimeout(() => {
					this.$refs.loading.close();

					this.$toPage("/trade/positionDetail?stockType=" + this.stockType);
				}, 500);
			},
			sellItem(item) {
				this.detailItem = item;
				this.show1 = true;
			},
			changeTime() {
				this.positionlistinteval = setInterval(() => {
					this.changeType();
				}, 10 * 1000);
			},
			changeType() {
				this.initData();
			},
			changeNav(index) {
				this.currmentIndex = index;
				clearInterval(this.positionlistinteval);
				this.$refs.loading.open(); //开启加载

				switch (index) {
					case 0:
						this.getXingu();
						break;
					case 1:
					case 2:
						this.positionList = [];
						this.positionCloseList = [];
						this.changeType();
						this.changeTime();
						break;
					case 3:
						this.getGd();
						break;
					default:
						break;
				}
			},
			// 平仓
			sellstrategy(id, status) {
				if (status == 3) {
					// 撤单
					this.cancelstrategy(id);
					return;
				}
				this.$server
					.post("/trade/sell_stock", {
						id,
						type: this.stockType
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							this.show1 = false;
							this.changeNav(2); //平倉成功切換顯示已平倉
							this.initData(true);
						}
					});
			},
			cancelstrategy(id) {
				this.$server.post("/trade/cancel_stock", {
					id,
					type: this.stockType,
				}).then((res) => {
					if (res.status == 1) {
						this.$toast(this.$t(res.msg));
						this.show1 = false;
						this.initData(true);
					}
				});
			},
			getXingu() {
				this.$server.post("/trade/usernewstocklist", {
					type: this.stockType,
					buy_type: 0,
				}).then((res) => {
					this.$refs.loading.close(); //关闭加载
					this.loading1 = false;
					//res = {"status": 1,"msg": "查询成功","data": [{"id": 14,"strategy_num": "202405261839034413","agent_id": 315,"account": "99999","stock_name": "CESHI","stock_code": "00123","apply_price": "10.000","apply_total": ********,"market_value": "100.000","status": 2,"lucky_total": 10,"subs_value": "100.000","buy_time": **********,"rjmoney": "100.000","rjnum": -4,"gb_time": **********,"buy_type": 0,"type": "sar","exchange": null,"start": **********,"end": **********,"gb_date": "2024-05-27T01:00:00.468Z","ss_date": "2024-05-28T01:00:00.384Z","fq_date": "2024-05-27T01:00:00.053Z","is_del": 0,"drawdate": null,"is_fq": 0,"qian": 10,"xgstate": "认缴成功"},{"id": 14,"strategy_num": "202405261839034413","agent_id": 315,"account": "99999","stock_name": "CESHI","stock_code": "00123","apply_price": "10.000","apply_total": ********,"market_value": "100.000","status": 2,"lucky_total": 10,"subs_value": "100.000","buy_time": **********,"rjmoney": "100.000","rjnum": -4,"gb_time": **********,"buy_type": 0,"type": "sar","exchange": null,"start": **********,"end": **********,"gb_date": "2024-05-27T01:00:00.468Z","ss_date": "2024-05-28T01:00:00.384Z","fq_date": "2024-05-27T01:00:00.053Z","is_del": 0,"drawdate": null,"is_fq": 0,"qian": 10,"xgstate": "认缴成功"}]}
					if (res.status == 1) {
						this.xinguList = res.data;
					}
				});
			},
			// 持仓&p平仓列表
			initData() {
				let url = "";
				if (this.currmentIndex == 1) {
					url = "/trade/userstocklist";
				} else {
					url = "/trade/userstocklists";
				}

				this.$server.post(url, {
					type: this.stockType,
					page: 1,
					size: 300,
				}).then(async (res) => {
					this.isLoading = false; //下拉刷新状态
					this.loading3 = false;
					/* if(this.currmentIndex==1){
						res = {"status": "1","msg": "获取成功","data": [{"id": 93,"strategy_num": "202406171358585442","agent_id": 315,"account": "99999","buyzd": 1,"stock_name": "NVIDIA Corporation","stock_code": "NVDA","stock_num": 1,"buy_price": "131.830","sell_price": null,"credit": "131.830","credit_add": "0.000","market_value": "131.830","status": 0,"true_getmoney": null,"buy_time": **********,"sell_time": null,"sell_type": null,"all_poundage": "0.013","buy_poundage": "0.013","sell_poundage": null,"yinhua_money": null,"defer_value": null,"defer_day": 0,"zhang": 1,"ganggang": 1,"cj_num": 0,"type": "sar","is_xg": 0,"amtdate": null,"is_buy": 0,"is_open": 0,"is_qc": 2,"gdlx": 1,"dz_type": 0,"buy_type": 0,"ss_date": null,"exchange": null,"is_del": 0,"nowprice": 0,"gain": 0,"yingkui": -99},{"id": 93,"strategy_num": "202406171358585442","agent_id": 315,"account": "99999","buyzd": 1,"stock_name": "NVIDIA Corporation","stock_code": "NVDA","stock_num": 1,"buy_price": "131.830","sell_price": null,"credit": "131.830","credit_add": "0.000","market_value": "131.830","status": 0,"true_getmoney": null,"buy_time": **********,"sell_time": null,"sell_type": null,"all_poundage": "0.013","buy_poundage": "0.013","sell_poundage": null,"yinhua_money": null,"defer_value": null,"defer_day": 0,"zhang": 1,"ganggang": 1,"cj_num": 0,"type": "sar","is_xg": 0,"amtdate": null,"is_buy": 0,"is_open": 0,"is_qc": 2,"gdlx": 1,"dz_type": 0,"buy_type": 0,"ss_date": null,"exchange": null,"is_del": 0,"nowprice": 0,"gain": 0,"yingkui": 12},{"id": 93,"strategy_num": "202406171358585442","agent_id": 315,"account": "99999","buyzd": 1,"stock_name": "NVIDIA Corporation","stock_code": "NVDA","stock_num": 1,"buy_price": "131.830","sell_price": null,"credit": "131.830","credit_add": "0.000","market_value": "131.830","status": 0,"true_getmoney": null,"buy_time": **********,"sell_time": null,"sell_type": null,"all_poundage": "0.013","buy_poundage": "0.013","sell_poundage": null,"yinhua_money": null,"defer_value": null,"defer_day": 0,"zhang": 1,"ganggang": 1,"cj_num": 0,"type": "sar","is_xg": 0,"amtdate": null,"is_buy": 0,"is_open": 0,"is_qc": 2,"gdlx": 1,"dz_type": 0,"buy_type": 0,"ss_date": null,"exchange": null,"is_del": 0,"nowprice": 0,"gain": 0,"yingkui": 123}]}
					}else if(this.currmentIndex==2){
						res = {"status": "1","msg": "获取成功","data": [{"id": 95,"strategy_num": "202406171538319025","agent_id": 315,"account": "99999","buyzd": 1,"stock_name": "الأهلي","stock_code": "1180","stock_num": 1,"buy_price": "35.150","sell_price": "35.150","credit": "35.150","credit_add": "0.000","market_value": "35.150","status": 1,"true_getmoney": "35.150","buy_time": **********,"sell_time": **********,"sell_type": 1,"all_poundage": "0.025","buy_poundage": "0.004","sell_poundage": "0.004","yinhua_money": "0.018","defer_value": null,"defer_day": 0,"zhang": 1,"ganggang": 1,"cj_num": 0,"type": "sar","is_xg": 0,"amtdate": null,"is_buy": 0,"is_open": 0,"is_qc": 0,"gdlx": 1,"dz_type": 0,"buy_type": 0,"ss_date": null,"exchange": "nse","is_del": 0,"gain": 0,"yingkui": 0},{"id": 95,"strategy_num": "202406171538319025","agent_id": 315,"account": "99999","buyzd": 1,"stock_name": "الأهلي","stock_code": "1180","stock_num": 1,"buy_price": "35.150","sell_price": "35.150","credit": "35.150","credit_add": "0.000","market_value": "35.150","status": 1,"true_getmoney": "35.150","buy_time": **********,"sell_time": **********,"sell_type": 1,"all_poundage": "0.025","buy_poundage": "0.004","sell_poundage": "0.004","yinhua_money": "0.018","defer_value": null,"defer_day": 0,"zhang": 1,"ganggang": 1,"cj_num": 0,"type": "sar","is_xg": 0,"amtdate": null,"is_buy": 0,"is_open": 0,"is_qc": 0,"gdlx": 1,"dz_type": 0,"buy_type": 0,"ss_date": null,"exchange": "nse","is_del": 0,"gain": 0,"yingkui": -88},{"id": 95,"strategy_num": "202406171538319025","agent_id": 315,"account": "99999","buyzd": 1,"stock_name": "الأهلي","stock_code": "1180","stock_num": 1,"buy_price": "35.150","sell_price": "35.150","credit": "35.150","credit_add": "0.000","market_value": "35.150","status": 1,"true_getmoney": "35.150","buy_time": **********,"sell_time": **********,"sell_type": 1,"all_poundage": "0.025","buy_poundage": "0.004","sell_poundage": "0.004","yinhua_money": "0.018","defer_value": null,"defer_day": 0,"zhang": 1,"ganggang": 1,"cj_num": 0,"type": "sar","is_xg": 0,"amtdate": null,"is_buy": 0,"is_open": 0,"is_qc": 0,"gdlx": 1,"dz_type": 0,"buy_type": 0,"ss_date": null,"exchange": "nse","is_del": 0,"gain": 0,"yingkui": 0},]}
					} */
					if (res.status == 1) {
						delete res.data.lsyk;
						delete res.data.fdyk;
						delete res.data.ccsz;
						let arr = Object.values(res.data);

						let szArr = []; //列表市值
						let ykArr = []; //列表盈亏
						let arr1 = []; //認繳的资金  买入本金+盈利(持仓冻结)

						arr.forEach((item) => {
							szArr.push(Number(item.market_value) + Number(item.yingkui));
							ykArr.push(Number(item.yingkui));
							arr1.push(
								Number(item.buy_price) * Number(item.stock_num) +
								Number(item.yingkui)
							);
						});

						this.szAssets = szArr.reduce((a, b) => a + b, 0);
						this.ykAssets = ykArr.reduce((a, b) => a + b, 0);
						// this.freezeAssets = arr1.reduce((a, b) => a + b, 0);

						const res1 = await this.$server.post("/user/getUserinfo");
						if (res.status == 1) {
							this.userInfo = res1.data;
						}

						// 总资产 可用+持仓资金
						// this.totalAssets = Number(this.userInfo.Germany) + this.freezeAssets;

						if (this.currmentIndex == 1) {
							this.positionList = arr;
						} else {
							this.positionCloseList = arr;
						}

						this.chartData = [
							// {
							//   value: this.totalAssets || 0,
							//   name: "",
							// },
							{
								value: this.szAssets || 0,
								name: "",
							},
							{
								value: this.ykAssets || 0,
								name: "",
							},
							{
								value: this.stockType=='try'?this.userInfo.try:this.userInfo.usd || 0,
								name: "",
							},
						];
						// this.getEcharts();
					}
					this.$refs.loading.close(); //关闭加载
				});
			},
			//盈利資金
			async getTotalProfit() {
				const res = await this.$server.post("/trade/userstocklist", {
					type: this.stockType,
					page: 1,
					size: 300,
				});
				let fdyk = 0;
				if (res.status == 1) {
					fdyk = Number(res.data.fdyk);
				}
				this.totalProfit = fdyk;
				//console.log(this.totalProfit, 999);
			},
			async getTotalAssets() {
				this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo");
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let krw = Number(this.stockType=='try'?res.data.try:res.data.usd || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: this.stockType,
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: this.stockType,
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 日内交易的申请冻结 接口报错
				const res3 = await this.$server.post("/trade/urnjylist", {
					type: this.stockType,
				});
				let dayDelFreeze = 0;
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					res3.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit));
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDelFreeze = total;
				}

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: this.stockType,
					page: 1,
					size: 300,
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: this.stockType,
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 日内交易持仓
				const res6 = await this.$server.post("/trade/userstocklist", {
					type: this.stockType,
					buy_type: 1
				});
				let dayDealFreeze = 0;
				delete res6.data.ccsz;
				delete res6.data.fdyk;
				let dataArr1 = res6.data;
				if (dataArr1.length) {
					let arr = [];
					dataArr1.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					dayDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					followingFreeze +
					dayDelFreeze +
					bigDealFreeze +
					positionFreeze +
					dayDealFreeze;
				// 总资产
				this.totalAssets = krw + this.freezeAssets;
				this.$refs.loading.close();

				this.chartData = [
					// {
					//   value: this.totalAssets || 0,
					//   name: "",
					// },
					{
						value: krw,
						name: "",
					},
					{
						value: this.totalProfit || 0,
						name: "",
					},
					{
						value: this.freezeAssets || 0,
						name: "",
					},
				];
				// console.log("this.chartData", this.chartData);
				// this.getEcharts();
			},
			getEcharts() {
				let that = this;
				if (that.myChart !== null) {
					echarts.dispose(that.myChart);
				}

				let chartDom = document.getElementById("main");
				that.myChart = echarts.init(chartDom);
				let option;
				option = {
					color: ["#6970AF", "#F1BABB", "#A2DDDD"], // 顺时针
					tooltip: {
						trigger: "item",
					},
					// 頂部圖例
					legend: {
						top: "5%",
						left: "center",
						show: false,
					},
					series: [{
						name: "",
						type: "pie",
						// radius: ['40%', '70%'], //圆环
						radius: "100%",
						center: ["50%", "50%"],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: "center",
						},
						emphasis: {
							label: {
								show: true,
								fontSize: "40",
								fontWeight: "bold",
							},
						},
						labelLine: {
							show: false,
						},
						data: this.chartData,
					}, ],
				};

				option && that.myChart.setOption(option);
			},
		},
	};
</script>

<style scoped lang="less">
	.trade {
		min-height: 100vh;
		padding: 1.8rem 0 .78rem;

    .nav-box {
      width: 100%;
      height: .4rem;
      margin-top: .1rem;
      .nav-item {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 0.14rem;
        color: #111111;
        text-align: center;
        margin-right: 0.3rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        span{
          width: .5rem;
          height: .03rem;
          background: transparent;
          margin-top: .05rem;
          display: block;
        }
        &.active {
          font-weight: 600;
          font-size: 0.14rem;
          color: #E10414;
          position: relative;
          span{
            background: #E10414;
          }
        }
      }
    }

		.header {
			width: 100vw;
			height: 1.5rem;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 999;
			background: #F3F2F2;
			padding: 0.15rem .12rem;
			border-radius: 0 0 0.22rem 0.22rem;

			.search {
				height: 0.36rem;
				background: #FFFFFF;
				border-radius: 0.18rem;
				font-weight: 400;
				font-size: .13rem;
				color: #A5A9AF;
				margin: 0;

				img {
					width: .14rem;
					margin-right: .1rem;
				}
			}

		}

		.tabs {
			padding: 0.1rem 0;
			position: relative;
			z-index: 888;

			.title {
				font-weight: 600;
				font-size: .16rem;
				color: #000000;
				margin-bottom: .18rem;
			}

			.tabBox {
        display: flex;
        flex-wrap: wrap;
			}

			.tab-item {
        width: 50%;
				border-radius: 0.3rem 0.3rem 0.3rem 0.3rem;
				height: 0.28rem;
				line-height: 0.28rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #111111;
				white-space: nowrap;

				&.active {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.17rem;
					color: #E10414;
				}
			}
		}

		.sbg {
			padding: 0.15rem;

			.txt {
				font-size: 0.14rem;
				color: #716d6d;

				.icon {
					margin-left: 0.05rem;
				}
			}

			.num {
				font-family: Poppins, Poppins;
				font-weight: 600;
				font-size: 0.28rem;
				color: #000000;
				padding: 0.05rem 0;
				margin-bottom: 0.15rem;
			}

			.t1 {
				font-size: 0.12rem;
				color: #000000;
			}

			.t2 {
				font-size: 0.12rem;
				margin-left: 0.1rem;
			}
		}

		.btns {
			padding: 0.15rem 0 0;
			margin: 0 0.15rem;

			.btn {
				width: 48%;
				background: #000000;
				border-radius: 0.1rem;
				padding: 0.1rem 0.2rem;
				color: #fff;
				font-size: 0.2rem;

				.icon {
					margin-right: 0.1rem;
				}
			}
		}

		#main {
			width: 0.66rem;
			height: 0.66rem;
			border-radius: 50%;
		}

		.money {
			padding: 0rem 0.12rem;

			.tops {
				// background: linear-gradient(90deg, #469d6f, #184856);
				margin-bottom: 0.1rem;

				.t {
					font-weight: 600;
					font-size: 0.12rem;

					.icon {
						margin-left: 0.1rem;
					}
				}

				.num {
					font-weight: 600;
					font-size: 0.24rem;
					color: #c5585e;
					margin: 5px 0;
				}

				.line {
					background: rgba(255, 255, 255, 0.5);
					border-radius: 0.03rem;
					height: 0.07rem;
					position: relative;

					&::after {
						content: "";
						width: 80%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
						z-index: 1;
						background: linear-gradient(45deg, #88d68b, #ffffff);
						border-radius: 0.03rem;
					}
				}

				.zc {
					margin-left: 0.3rem;
				}
			}

			.nums {
				padding: .16rem 0;
				background: #FFFFFF;
				border-radius: 0.13rem;

				.item {
					text-align: center;
					width: 30%;

					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.15rem;
					}

					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #666666;
						margin-bottom: .1rem;
					}
				}
			}
		}

		.cy-list {
			margin: 0.1rem 0.12rem 0;
			background: #FFFFFF;
			border-radius: 0.13rem;
			padding: .12rem;

			.cy-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}

			.cy-item {
				border-bottom: 0.01rem solid #F0F0F0;
				padding-bottom: 0.12rem;

				&:last-child {
					border-bottom: none;
				}

				.cy-top {
					padding: 0.15rem 0;

					.name {
						width: 33%;
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.16rem;
						color: #333333;

						span {
							display: inline-block;
							margin-top: .06rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #999999;
						}
					}

					.price {
						width: 33%;
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.16rem;
						color: #333333;

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #006AFF;
							display: inline-block;
							margin-top: .06rem;
						}
					}

					&.red {
						.price {
							span {
								color: #E97F88;
								background: rgba(233, 127, 136, .1);
							}
						}
					}

					&.green {
						.price {
							span {
								color: #0068FF;
								background: rgba(0, 104, 255, .1);
							}
						}
					}
				}

				.inner {
					flex-wrap: wrap;
					padding: 0.1rem 0;

					.inner-item {
						line-height: 0.24rem;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.t1 {
							font-size: 0.12rem;
							color: #8C8C8C;
						}

						.t2 {
							font-weight: 500;
							font-size: 0.12rem;
							color: #0E0E0E;
						}
					}
				}

				.cy-bottom {
					height: .4rem;
					background: #F5F5F5;
					border-radius: 0.03rem;
					overflow: hidden;
					padding: 0 .1rem;

					.per {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;

						span {
							display: inline-block;
							margin-left: 0.1rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.16rem;
						}
					}

					.status {
						position: relative;
						display: flex;
						align-items: center;
						justify-content: center;
						height: 0.2rem;
						background: url('../../assets/v2/ccz.png') no-repeat center/100%;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #E10414;
            padding: 0 .05rem;

						&.end {
							color: #6D7E96;
						}
					}
				}
			}

			.bts {
				padding: 0.1rem 0.1rem 0.15rem;

				.bt {
					width: 48%;
					height: 0.34rem;
					line-height: 0.3rem;
					border: 0.01rem solid #000000;
					border-radius: 0.04rem;
					font-weight: 500;
					font-size: 0.14rem;
					color: #000000;
					text-align: center;
					font-family: PingFang TC, PingFang TC;

					&.bt1 {
						color: #fff;
						background: #000000;
					}
				}
			}
		}

		::v-deep .van-popup {
			overflow-y: visible;
		}

		.popup {
			.title {
				text-align: center;
				padding: 0 .3rem .3rem;
				background: linear-gradient(rgba(225, 4, 20, 0.3) 0%, rgba(225, 4, 20, 0.01) 100%);

				img {
					margin-top: -0.5rem;
					width: 1.23rem;
					height: 1.28rem;
				}

				.name {
					font-weight: 600;
					font-size: 0.16rem;
					color: #000000;
				}

				.code {
					font-size: 0.12rem;
					color: #989898;
					margin-top: 0.05rem;
				}
			}

			.close {
				position: absolute;
				bottom: -0.5rem;
			}

			.pdd {
				padding: 0 0.2rem 0.3rem;
				text-align: center;

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.37rem;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #999999;
					margin-top: 0.05rem;
				}

				.bt {
					background: #F3F2F2;
					border-radius: 0.5rem 0rem 0rem 0.5rem;
					clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 0% 100%);
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #333333;
					width: 52%;
					margin-top: 0.3rem;
					padding: 0.12rem 0;

					&.bt1 {
						background: #E10414;
						border-radius: 0rem 0.5rem 0.5rem 0rem;
						border: 0.01rem solid #E10414;
						color: #fff;
						clip-path: polygon(20% 0%, 100% 0%, 100% 100%, 0% 100%);
					}
				}
			}
		}

		.gd-list {
			background: #FFFFFF;
			border-radius: 0.13rem;
			margin: 0 0.12rem;

			.gd-item {
				padding: 0.12rem;
				.top {
					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.15rem;
						color: #333333;

						span {
							margin-left: 0.05rem;
							display: inline-block;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.11rem;
							color: #999999;
						}
					}

					.shouyi {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
						padding: .05rem .1rem;

						span {
							margin-left: .06rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.13rem;
						}
					}

					.time {
						padding: 0.1rem 0;

						div,
						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #999999;
						}
					}
				}

				.inner {
					flex-wrap: wrap;
					padding: .12rem;
					background: #F5F5F5;
					border-radius: 0.03rem;

					.inner-item {
						line-height: 0.24rem;

						.t1 {
							text-align: center;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #999999;
						}

						.t2 {
							text-align: center;
							font-weight: 500;
							font-size: 0.14rem;
							color: #000000;
						}
					}
				}
				.inner-list{
					margin-top: 0.1rem;
					background: linear-gradient( 180deg, #F2F5F7 0%, #FFFFFF 100%);
					border-radius: 0.08rem;
					.inner-item{
						width: 48%;
						padding: 0.08rem 0.12rem;
					}
					.point{
						margin-right: 0.05rem;
						width: 0.04rem;
						height: 0.04rem;
						background: #596882;
						border-radius: 50%;
					}
					.t1{
						margin-right:0.2rem;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}
					.t-r{
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.16rem;
					}
				}


				.sylb {
					border-radius: 0.02rem;
					border: 0.01rem solid #c0c0c0;
					padding: 0.05rem;
					font-size: 0.12rem;
					color: #6970af;
					margin-left: 0.05rem;
				}
			}
		}

		.sg-list {
			margin: 0 0.12rem;
			background: #FFFFFF;
			border-radius: 0.13rem;
			padding: 0 0.12rem;
			.sg-item {
				padding: 0.12rem 0;
				border-bottom: 0.01rem solid #F0F0F0;
				&:last-child{
					border-bottom: none;
				}
				.s-btn {
					background: #3f5ad8;
					border-radius: 27px;
					padding: 10px;
					font-weight: 400;
					color: #ffffff;
					font-size: 26px;
					width: 32%;
					text-align: center;
					margin: 30px auto 10px;
				}

				.top {
					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.15rem;
						color: #333333;
						span {
							margin-left:0.05rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.11rem;
							color: #999999;
						}
					}

					.status {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #E10414;
					}
				}

				.data {
					text-align: center;
					flex-wrap: wrap;
					padding: 0.2rem 0 0;
					.t {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}

					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.2rem;
						color: #333333;
					}

					.data-item {
						line-height: 0.24rem;
					}
				}
			}
		}

	}
</style>