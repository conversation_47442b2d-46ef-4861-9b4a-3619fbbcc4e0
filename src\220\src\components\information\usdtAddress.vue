<template>
  <div class="page ">
    <top-back :title="$t('mine').menu13"></top-back>

    <template v-if="bankList.length > 0">
      <div class="card" v-for="(item, index) in bankList" :key="index">
        <div class="card-del flex" @click="delbakcard(item.id)">
          {{ $t("usdtAddress").txt1 }}
        </div>
        <div class="card-top">{{ item.bank_name }}</div>
        <div class="card-num">{{ formatNum(item.bank_num) }}</div>
      </div>
      <div class="btn-big" @click="clickNext('/pages/mine/bandBankCardNum')">
        {{ $t("usdtAddress").btn }}
      </div>
    </template>
    <template v-else>
      <div class="nodata-box">
        {{ $t("usdtAddress").not }}
      </div>
      <div class="btn-big" @click="clickNext('/pages/mine/bandBankCardNum')">
        {{ $t("usdtAddress").btn }}
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "usdtAddress",
  data() {
    return {
      imgList: {
        nodata: this.$server.globalData.style === "day" ? "" : "",
      },
      bankList: [],
      banmanagertimer: null,
    };
  },
  computed: {
    formatNum() {
      return (value) => {
        let str = value.slice(0, 4);
        return `${str} **** **** ****`;
      };
    },
  },
  created() {
    this.initData();
    this.banmanagertimer = setInterval(() => {
      this.initData();
    }, 2000);
    this.addTimer(this.banmanagertimer);
  },
  mounted() {
    this.initData();
  },
  beforeDestroy() {
    clearInterval(this.banmanagertimer);
  },
  methods: {
    delbakcard(bankid) {
      let that = this;
      this.$server.post("/user/delbakcard", { bankid }).then((res) => {
        if (res.status === 1) {
          // that.initData();
          uni.showToast({
            title: `${this.$t("new").a22}`,
            duration: 2000,
            icon: "success",
            complete() {
              // that.initData()
            },
          });
        } else {
          uni.showToast({
            title: this.$t(res.msg),
            icon: "none",
          });
        }
      });
    },
    initData() {
      let that = this;
      this.$server
        .post("/user/cardList", { size: 200, page: 1 }, (failres) => {
          that.bankList = [];
        })
        .then((res) => {
          var arr = [];
          for (var i in res.data) {
            var row = res.data[i];
            if (row.bank_name == "TRC" || row.bank_name == "ERC") {
              arr.push(row);
            }
          }
          that.bankList = arr;
        });
    },
  },
};
</script>

<style scoped lang="less">
.nodata-box {
  height: 560px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  img {
    width: 300px;
    height: 270px;
    margin-bottom: 40px;
  }
}

.card {
  margin: 30px 30px 0 30px;
  background: #333333;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  position: relative;
  .card-top {
    font-size: 28px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 400;
    color: #fff;
    line-height: 42px;
  }
  .card-num {
    font-size: 32px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
    color: #fff;
    line-height: 37px;
    margin-top: 30px;
  }
  .card-del {
    position: absolute;
    top: 30px;
    right: 30px;
    height: 48px;
    padding: 0 24px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 6px;
    font-size: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }
}
.btn-big {
  margin: 180px 30px 0 30px;
  image {
    width: 30px;
    height: 30px;
  }
}
</style>
