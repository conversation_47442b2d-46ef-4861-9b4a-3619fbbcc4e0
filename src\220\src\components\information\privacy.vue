<template>
	<div class="page ">
		<top-back :title="$t('login').txt6"></top-back>

		<div class="list">
			<div class="item">
				{{ $t("about").txt2 }}
			</div>
			<div class="item">
        HALK Gizlilik Politikası<br/><br/>
        HALK uygulaması, Türkiye'nin Kişisel Verilerin Korunması Kanunu (KVKK) ve uluslararası gizlilik standartlarına uygun olarak kullanıcı bilgilerinin güvenliğini sağlamayı taahhüt eder.<br/><br/>

        1. Kişisel Bilgilerin Toplanması<br/><br/>
        Toplanan Bilgi Türleri: HALK, kullanıcıların adını, kimlik numarasını, iletişim bilgilerini, finansal bilgilerini (örneğin banka hesap numarası, yatır<PERSON><PERSON> kayıtları), cihaz bilgilerini (örneğin IP adresi, cihaz kimlik numarası) ve diğer ilgili bilgileri toplar.<br/><br/>
        Veri Kaynağı: Kullanıcı tarafından sağlanan bilgiler, uygulama tarafından otomatik olarak toplanan veriler ve iş ortakları, finansal kurumlar gibi üçüncü taraflardan elde edilen bilgiler olabilir.<br/><br/>
        2. Kişisel Bilgilerin Kullanımı<br/><br/>
        Kullanım Amaçları: Toplanan kişisel bilgiler, kimlik doğrulama, hesap yönetimi, işlem gerçekleştirme, müşteri desteği, risk yönetimi ve yasal uyum gibi amaçlarla kullanılır.<br/><br/>
        Pazarlama Amaçları: Kullanıcıların izni ile, kişisel veriler pazarlama ve kişiselleştirilmiş hizmetler sunmak amacıyla kullanılabilir. Kullanıcılar bu tür işlemlerden çıkma hakkına sahiptir.<br/><br/>
        3. Bilgilerin Paylaşılması ve İfşası<br/><br/>
        Üçüncü Taraflarla Paylaşım: HALK, kullanıcı verilerini iş birliği yapılan finansal kuruluşlar, düzenleyici makamlar ve teknik hizmet sağlayıcılarla paylaşabilir.<br/><br/>
        Yasal Zorunluluklar: Yasal talepler doğrultusunda, kişisel veriler hükümet yetkilileri veya adli makamlarla paylaşılabilir.<br/><br/>
        4. Veri Koruma Önlemleri<br/><br/>
        Teknik ve Fiziksel Güvenlik Önlemleri: Verilerin gizliliği, şifreleme, erişim kontrolü ve güvenli veri merkezi yönetimi gibi ileri güvenlik önlemleri ile korunur.<br/><br/>
        5. Kullanıcı Hakları<br/><br/>
        Erişim Hakkı: Kullanıcılar, kendileriyle ilgili saklanan verilere erişim talep etme hakkına sahiptir.<br/><br/>
        Düzeltme ve Silme Hakkı: Kullanıcılar, yanlış verilerin düzeltilmesini veya kişisel bilgilerinin silinmesini talep edebilir.<br/><br/>
        İtiraz Hakkı: Kullanıcılar, kişisel verilerinin pazarlama gibi belirli amaçlar için kullanılmasına itiraz edebilir.<br/><br/>
        6. Veri Saklama ve Koruma<br/><br/>
        Veri Saklama Süresi: Kişisel veriler, yalnızca gerekli olduğu süre boyunca saklanır ve saklama süresi sona erdiğinde veriler silinir veya anonim hale getirilir.<br/><br/>
        Sınır Ötesi Veri Aktarımı: Verilerin Türkiye dışına aktarılması durumunda, bu aktarımın güvenliğine dair gerekli tedbirler alınır.
      </div>
<!--			<no-data v-if="!info"></no-data>-->
		</div>
	</div>
</template>

<script>
	export default {
		name: "aboutInfo",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			initData() {
				this.$server.post("/common/wenben", {
						type: this.$stockType,
						name: "隐私政策"
					})
					.then((res) => {
						if (res.status == 1) {
							this.info = res.data;
						}
					});
			},
		},
		created() {
			//this.initData();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	::v-deep .item p {
		width: 100%;
		word-break: break-word;
	}

	::v-deep .item span {
		display: block;
		width: 100%;
	}

	.page {
		// background: #fff;
		padding: 0.6rem 0.15rem 0.2rem;
		min-height: 100vh;
	}

	.list {
		background: #ffffff;
		border-radius: 0.08rem;
		padding: 0.15rem;

		.item {
			line-height: 0.3rem;
			// margin-bottom: 0.1rem;
		}
	}
</style>