<template>
	<!-- 自选 -->
	<div class="page ">
		<!-- <top-index></top-index> -->
		<div class="header flex flex-b">
			<!-- <div class="tab flex">
				<div class="item" :class="{'sel':tabIdx==idx}" v-for="(item,idx) in tabList" :key="idx" @click="clickTab(idx)">{{item}}</div>
			</div> -->
			<div class="icon mine" @click="$toPage('/information/index')"></div>
			<div class="tit">{{$t('menu').href4}}</div>
			<div class="flex">
				<div class="icon ss1" style="margin-right: 0.1rem;" @click="$toPage('/favorite/search')"></div>
				<div class="icon msg2" @click="$toPage('/information/userInfo')"></div>
			</div>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
        <div class="nav-box flex">
          <div class="nav-item" v-for="(item, index) in navList" :key="index"
               :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
            {{ item.name }}<span></span>
          </div>
        </div>
				<div class="title flex flex-b" v-if="false">
					<div>{{ $t("new").a1 }}</div>

					<template v-if="chooseList.length">
						<div class="icon bjbtn animate__animated animate__fadeIn" v-if="!show" @click="changeList">
						</div>
						<div v-if="show" @click="cancle">{{ $t("new").a2 }}</div>
					</template>
				</div>

				<!-- 指数列表 -->
				<div class="index" v-if="false">
					<!-- <div class="t"> {{ $t("newt").t47 }} </div> -->
					<div class="nums flex flex-b">
						<div class="nums-item" :class="{ center: i == 1 }" v-for="(item, i) in list" :key="i"
							@click=" $toDetail(`/market/stockDetailzs?symbol=${item.stock_id}&stockType=${stockType}`, item) ">
							<div class="name">
								{{ item.ko_name }}
							</div>
							<div class="t flex flex-c" :class="{ die: item.gain < 0 }">
								{{ $formatMoney(item.close, 2) }}
								<div class="icon animate__animated animate__fadeIn"
									:class="item.gain < 0 ? 'down1' : 'up1'"></div>
							</div>
							<div class="t1" :class="{ die: item.gain < 0 }">
								{{ item.gain <= 0 ? "" : "+" }}{{ $formatMoney(item.gainValue, 2) }}
								({{stockType=='try'?'%':''}}{{ item.gain <= 0 ? "" : "+" }}{{ item.gain }}{{stockType=='try'?'':'%'}})
							</div>
						</div>
					</div>
				</div>

				<div class="cot">
					<no-data v-if="isShow"></no-data>

					<div class="list" v-if="chooseList.length">
						<div class="flex flex-b titles">
							<div class="flex-1">{{ $t("newt").t57 }}</div>
							<div class="flex-1 t-c">{{ $t("newt").t58 }}</div>
							<div class="flex-1 t-r flex flex-e" @click="changeListup">
								{{ $t("newt").t59 }}
								<div class="icon" :class="isUp ? 'zf' : 'df'"></div>
							</div>
						</div>

						<div class="list-item flex flex-b" v-for="(item, index) in chooseList" :key="index"
							@click=" $toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item) ">
							<div class="flex-1 flex">
								<div v-if="item.showIcon" class="icon wxz animate__animated animate__fadeIn" :class="{ xz: item.choose }" @click.stop="changeItem(index)"></div>
								<div>
									<div class="name">{{ item.name || "-" }}</div>
									<div class="code">{{ item.symbol || "-" }}</div>
								</div>
							</div>
							<div class=" price flex flex-c flex-1">
								{{ $formatMoney(item.price,4) || 0 }}
								<!-- <div class="icon down animate__animated animate__fadeIn" :class="{ up: item.gain > 0, }"></div> -->
							</div>
							<div class="flex-1 per red t-r" :class="{ green: Number(item.gain) < 0, }">
								<div class="t">
									{{ item.gainValue > 0 ? "+" : "" }}{{ $formatMoney(item.gainValue,4) || 0 }}
								</div>
								<div class="t">
                  {{stockType=='try'?'%':''}}{{ item.gain > 0 ? "+" : "" }}{{ item.gain || 0 }}{{stockType=='try'?'':'%'}}
								</div>
							</div>
						</div>
					</div>

					<div class="btns flex flex-b" v-if="show">
						<div class="btn btn2 " @click="chooseAll">
							{{ $t("new").a3 }}
						</div>
						<div class="btn btn1" @click="delItem">
							{{ $t("new").a4 }}
						</div>
					</div>

					<div class="btn-box" v-if="!show">
						<div class="big_btn flex flex-c" @click="$toPage('/favorite/search')">
							<!-- <img src="../../assets/market/add.png" /> -->
							{{ $t("new").t36 }}
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>
		<tab-bar :current="2"></tab-bar>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "favorite",
		props: {},
		data() {
			return {
				isUp: false, //默认涨幅在前
				list: [],
				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,
				show: false,
				chooseList: [],
				lastSymbol: "",
				tabList: [this.$t('行情'), this.$t('自选')],
				tabIdx: 1,
        navList: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
			};
		},
		computed: {},
		created() {
			this.getMine();
			//this.getIndexList();
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		methods: {
      changeStock(type){
        this.stockType = type
        this.getMine();
      },
			clickTab(idx) {
				if (idx == 0) {
					this.$toPage('/market/index');
				}
			},
			onRefresh() {
				this.isShow = false;
				this.getMine();
			},
			changeList() {
				if (!this.chooseList.length) return;
				this.chooseList.forEach((item) => {
					item.showIcon = true;
				});
				this.show = true;
			},
			cancle() {
				this.chooseList.forEach((item) => {
					item.showIcon = false;
				});
				this.show = false;
			},
			changeItem(index) {
				this.chooseList.forEach((item, i) => {
					if (index == i) {
						item.choose = !item.choose;
					}
				});
			},
			chooseAll() {
				this.chooseList.forEach((item) => {
					item.choose = true;
				});
			},
			delItem() {
				let arr = this.chooseList.filter((item) => item.choose);
				if (arr.length) {
					this.lastSymbol = arr[arr.length - 1].symbol; //记录已选中最后一项的值
					// console.log("lastSymbol", this.lastSymbol);
					this.$refs.loading.open(); //开启加载
					arr.forEach((item) => {
						this.removeOptional(item);
					});
				}
			},
			getIndexList() { //指数
				this.$server.post("/parameter/zhishu", {
					type:this.stockType
				}).then((res) => {
					this.loading1 = false;
					if (res && res.data) {
						/* let arr = [];
						res.data.forEach((item) => {
							arr.push({
								name: item.name,
								time: this.$formatDate("YYYY.MM.DD hh:mm:ss",new Date().getTime()),
								gainValue: item.close - item.prev_close,
								gain: (((item.close - item.prev_close) / item.prev_close) *100).toFixed(2),
								...item,
							});
						}); */
						this.list = res.data;
					}
				});
			},
			changeListup() {
				this.isUp = !this.isUp;
				let arr = [];
				let arr1 = [];
				this.chooseList.forEach((item) => {
					if (item.gain > 0) {
						arr.push(item);
					} else {
						arr1.push(item);
					}
				});

				if (this.isUp) {
					arr.sort((a, b) => b.gain - a.gain);
					arr1.sort((a, b) => b.gain - a.gain);
					this.chooseList = [...arr, ...arr1]; //涨在前、高在前
				} else {
					arr.sort((a, b) => a.gain - b.gain);
					arr1.sort((a, b) => a.gain - b.gain);
					this.chooseList = [...arr1, ...arr]; //跌在前、低在前
				}
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					offset: 0,
					type: this.stockType
				}).then((res) => {
					this.isLoading = false; //下拉刷新状态
					// this.$refs.firstLoading.close(); //关闭初始加载的效果
					this.loading = false;
					if (res.status == 1) {
						if (this.show) {
							// 如果是已经选择删除，但是列表还有数据
							res.data.forEach((item) => {
								item.showIcon = true;
								item.choose = false;
							});
						} else {
							res.data.forEach((item) => {
								item.showIcon = false;
								item.choose = false;
							});
						}

						this.chooseList = res.data;
						this.changeListup(); //重新加载排序，涨在前

						if (!res.data.length) {
							this.show = false;
							this.isShow = true;
						}
						console.log("this.chooseList ", this.chooseList);
					}
				});
			},
			removeOptional(item) {
				this.$server.post("user/removeOptional", {
					symbol: item.symbol,
					type: this.stockType
				}).then((res) => {
					if (res.status == 1) {
						if (this.lastSymbol == item.symbol) {
							this.$refs.loading.close(); //删除最后一项成功，结束加载中
							this.getMine();
						}
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0.7rem;
		min-height: 100vh;
	}

  .nav-box {
    width: 100%;
    height: .4rem;
    top: .5rem;
    padding: 0.15rem;
    .nav-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.14rem;
      color: #111111;
      text-align: center;
      margin-right: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span{
        width: .5rem;
        height: .03rem;
        background: transparent;
        margin-top: .05rem;
        display: block;
      }
      &.active {
        font-weight: 600;
        font-size: 0.14rem;
        color: #E10414;
        position: relative;
        span{
          background: #E10414;
        }
      }
    }
  }
	.header {
		width: 100vw;
		height: .5rem;
		background: #F3F2F2;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0 0.12rem;
		font-weight: 500;
		font-size: .18rem;
		color: #000000;

		.tit {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.17rem;
			color: #111111;
		}

		.tab {
			.item {
				font-weight: 500;
				font-size: .14rem;
				color: rgba(255, 255, 255, 0.49);
				margin-right: .2rem;

				&.sel {
					font-size: .15rem;
					color: #FFFFFF;
				}
			}
		}

		img {
			width: .16rem;
		}
	}

	.btn-box {
		padding: 0.2rem 0.1rem;

		.big_btn {
			margin-left: .5rem;
			margin-right: .5rem;
			border-radius: 0.22rem;
		}

		img {
			width: .16rem;
			margin-left: .07rem;
		}
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.nums {
		text-align: center;
		padding: 0.1rem 0;
		// border-top: 0.01rem solid #f5f5f5;
		border-bottom: 0.01rem solid #f5f5f5;

		.nums-item {
			width: 32%;

			&.center {
				border-left: 0.02rem solid #bbc5c1;
				border-right: 0.02rem solid #bbc5c1;
			}

			.name {
				font-weight: 600;
				font-size: 0.12rem;
				margin-bottom: 0.05rem;
			}

			.icon {
				margin-left: 0.05rem;
			}

			.t {
				font-weight: 500;
				font-size: 0.16rem;
				color: #c5585e;
				margin: 0.1rem 0;

				&.die {
					color: #4f8672;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #c5585e;

				&.die {
					color: #4f8672;
				}
			}
		}
	}

	.title {
		padding: 0.1rem;
		font-weight: 500;
		color: #181818;

	}

	.cot {
		background: #FFFFFF;
		border-radius: 0.13rem;
		margin-top: .1rem;
		padding: .12rem;
		height: calc(100vh - 1.3rem);

		.list {
			.titles {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;

				.icon {
					margin-left: 0.05rem;
				}
			}

			.list-item {
				padding: 0.1rem 0;

				.wxz {
					margin-right: 0.05rem;
				}

				.name {
					font-weight: bold;
					font-family: PingFangSC, PingFang SC;
					font-size: 0.15rem;
					color: #333333;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					line-height: 0.11rem;
				}

				.price {
					font-weight: bold;
					font-family: PingFangSC, PingFang SC;
					font-size: 0.15rem;
					color: #333333;
					.icon {
						margin-left: 0.05rem;
					}
				}

				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.15rem;
				}
			}
		}
	}

	.btns {
		margin: 0.2rem 0.1rem;

		.btn {
			width: 48%;
			color: #4E9BE7;
			border-radius: 0.06rem;
			font-weight: 500;
			text-align: center;
			padding: 0.1rem 0;

			&.btn1 {
				background: #4E9BE7;
				color: #ffffff;
			}

			&.btn2 {
				border: 0.01rem solid #4E9BE7;
			}
		}
	}
</style>