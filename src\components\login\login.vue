<template>
	<div class="page flex flex-b">
		<!-- <div class="logo t-c animate__animated animate__fadeIn">
			<img :src="$cfg.logo" />
			<div class="txt">{{ $cfg.title }}</div>
		</div> -->
		<div class="form">
			<div class="title">
				<!-- <img class="img animate__animated animate__fadeIn" :src="$cfg.logo" alt="" /> -->
				<div class="txt">{{ $cfg.title }}</div>
				<div class="t">歡迎您的登入</div>
			</div>
			<div class="cot">
				<!-- <div class="t">帳戶</div> -->
				<div class="inner flex flex-b">
					<div class="icon zh"></div>
					<input placeholder="請輸入帳號" v-model="phone" maxlength="11" type="text" />
				</div>
				<!-- <div class="t">密碼</div> -->
				<div class="inner flex flex-b">
					<div class="icon mm"></div>
					<input v-if="show" placeholder="請輸入密碼" v-model="password" type="text" />
					<input v-else placeholder="請輸入密碼" v-model="password" type="password" />
					<div class="icon" :class="show ? 'by' : 'zy1'" @click="show = !show"></div>
				</div>
				<div class="flex flex-b forget">
					<van-checkbox v-model="checked" checked-color="#078BDE" shape="square" icon-size="14px"
						@change="save">記住密碼</van-checkbox>
					<div class="t1" @click="$openUrl($cfg.kefu)">忘記密碼</div>
				</div>
				<div class="b-btn animate__animated animate__fadeIn" @click="login">登入</div>
				<div v-if="false" class="b-btn02 animate__animated animate__fadeIn" @click="$toPage('/login/register')">沒有帳戶，立即開戶
				</div>
			</div>
			<!--点击按钮加载效果组件 -->
			<loading ref="loading" />
		</div>
	</div>
</template>
<script>
	import {
		mapMutations
	} from "vuex";
	export default {
		name: "login",
		data() {
			return {
				show: false,
				cfg: {},
				phone: "",
				password: "",
				logo: "",
				checked: false,
				jzFlag: false
			};
		},
		components: {},
		mounted() {
			this.getConfig();
			let phone = this.$storage.get("AC")
			let password = this.$storage.get("PS")
			if (phone && password) {
				this.phone = phone
				this.password = password
				this.checked = true
			}
		},
		destroyed() {},
		methods: {
			...mapMutations(["saveToken", "saveAccount"]),
			save(e) {
				this.jzFlag = e

			},
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "twd"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/login/logo.png");
				}
				this.cfg = val;
			},
      containsChinese(str) {
        const regex = /[\u4e00-\u9fff]/;
        return regex.test(str);
      },
			async login() {
				if (!this.phone) {
					this.$toast("請輸入帳號");
					// Toast({
					//   message: this.$t("login").phoneTip,
					//   duration: 2000,
					// });
					return;
				}
        if(this.containsChinese(this.phone)){
          this.$toast("帳號必須不含中文");
          return;
        }
				if (!this.password) {
					this.$toast("請輸入密碼");
					return;
				}

				let data = {
					account: this.phone,
					password: this.password,
				};

				this.$refs.loading.open(); //开启加载
				const res = await this.$server.post("/user/login", data);
				this.$refs.loading.close(); //关闭加载
				if (res.status == 1) {
					this.$toast(res.msg);
					// 登录时首次，赋值token
					this.$server.defaults.headers.token = res.data.token;
					this.$server.defaults.headers.account = res.data.account;

					this.saveToken(res.data.token);
					this.saveAccount(res.data.account);

					if (this.jzFlag) {
						this.$storage.save("AC", this.phone);
						this.$storage.save("PS", this.password);
					} else {
						this.$storage.remove("AC");
						this.$storage.remove("PS");
					}
					setTimeout(() => {
						this.$toPage("/login/loginAfter");
					}, 1000);
				} else {
					this.$toast(this.$t(res.msg));
				}
			},
		},
	};
</script>
<style lang="less" scoped="scoped">
	::v-deep .van-checkbox__label {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.12rem;
		color: #999999 !important;
	}

	.page {
		width: 100vw;
		min-height: 100vh;
		padding: 1rem 0 0;
		background: url("../../assets/123.png");
		background-size: contain;
		background-position: center top;
		background-repeat: no-repeat;
		background-color: #fff;
		flex-direction: column;
		width: 100vw;
		min-height: 100vh;
		background: url('../../assets/v1/newstart.png');
		background-size: 100%;
		.logo {
			width: 1rem;
			height: 1rem;
			margin: 0 auto 1rem;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.txt {
				font-weight: 600;
				font-size: 0.3rem;
				color: #333;
				margin-top: 0.1rem;
			}
		}

		.form {
			width: 100%;
			padding: 0 0.2rem;

			.title {
				.img {
					width: 0.5rem;
					height: 0.5rem;
				}

				.txt {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.35rem;
					color: #fff;
				}

				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #fff;
					margin-bottom: 0.1rem;
				}
			}

			.cot {
				margin: 0.4rem 0 0;
				background: #FFFFFF;
				border-radius: 0.14rem;
				padding: 0.15rem;
				.t {
					color: #222222;
					margin-right: 0.05rem;
					font-size: 0.16rem;
				}

				.inner {
					margin-bottom: 0.16rem;
					padding: 0.14rem 0;
					padding-right: .1rem;
					border-bottom: 0.01rem solid #D3D3D3;
					input {
						background-color: transparent;
						flex: 1;
						color: #333;
						padding: 0 0.1rem;
						font-size: 0.13rem;

						&::placeholder {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #999999;
						}
					}
				}

				.forget {
					margin: 0.1rem 0 0.5rem;

					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #293952;
					}
				}
			}

			.btn {
				background: #549d7e;
				border-radius: 0.04rem;
				height: 0.4rem;
				line-height: 0.4rem;
				color: #ffffff;
				text-align: center;
			}
		}
	}

	::v-deep .van-checkbox__label {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.12rem;
		color: #999999;
	}

	::v-deep .van-checkbox__icon--checked {
		.van-icon {
			background-color: transparent;
			border-color: #999;
		}
	}
</style>