<template>
  <div class="page">
    <img class="img" src="../../assets/home/<USER>" />
  </div>
</template>

<script>
export default {
  name: "start",
  props: {},
  data() {
    return {};
  },
  components: {},
  mounted() {
    this.setInt();
  },
  methods: {
    setInt() {
      setTimeout(() => {
        this.startTime();
      }, 3000);
    },

    startTime() {
      if (localStorage.getItem("tokend") && localStorage.getItem("account")) {
        this.$toPage("/home/<USER>");
      } else {
        this.$toPage("/login/start1");
      }
    },
  },
};
</script>

<style scoped lang="less">
.page {
  width: 100vw;
  min-height: 100vh;

  .img {
    width: 100%;
    height: 100%;
  }
}
</style>
