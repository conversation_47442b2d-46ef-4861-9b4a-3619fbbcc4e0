<template>
  <div class="animate__animated animate__fadeIn">
    <!-- <div class="cot">
      <div class="ic">
        <van-icon name="description" size="0.5rem" color="#333" />
      </div>
      <div class="t">{{ $t("new").t37 }}</div>
    </div> -->
    <van-empty  description="暫無數據" />
  </div>
</template>

<script>
export default {
  name: "noData",
  props: {},
  data() {
    return {};
  },
  components: {},
  methods: {},
  created() {},
  computed: {},
};
</script>

<style scoped lang="less">
.cot {
  text-align: center;
  padding: 0.5rem 0;
  margin: 0 auto;
  .t {
    font-size: 0.12rem;
    margin-top: 0.1rem;
  }
}
</style>
