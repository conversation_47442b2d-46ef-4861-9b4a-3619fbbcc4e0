<template>
  <div class="page flex flex-b">
    <!-- <div class="icon jz2 animate__animated animate__fadeIn"></div> -->
    <!-- icon qdt -->
    <div class=" logo animate__animated animate__fadeIn">
      <img class="img" :src="$cfg.logo" />
    </div>

    <div class="flex flex-b bottom">
      <div
        class="icon jzz animate__animated animate__rotateOut animate__infinite"
      ></div>

      <div class="flex-1">
        <div class="txt">{{ $t("new").t38 }}({{ percent }}%)</div>
        <!--  color="linear-gradient(to right, #6970AF, #C4C2EF)" -->
        <van-progress
          stroke-width="6"
          track-color="#F3F3F3"
          color="#CCBD72"
          pivot-text=""
          :percentage="percent"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "start",
  props: {},
  data() {
    return {
      cfg: {},
      percent: 10,
    };
  },
  components: {},
  mounted() {
    this.setInt();
    // this.getConfig();
  },
  methods: {
    setInt() {
      setTimeout(() => {
        this.percent = 25;
        setTimeout(() => {
          this.percent = 50;
          setTimeout(() => {
            this.percent = 75;
            setTimeout(() => {
              this.percent = 100;
              setTimeout(() => {
                this.startTime();
              }, 500);
            }, 500);
          }, 500);
        }, 500);
      }, 500);
    },
    startTime() {
      if (localStorage.getItem("tokend") && localStorage.getItem("account")) {
        this.$toPage("/home/<USER>");
      } else {
        this.$toPage("/login/start1");
      }
    },
    // 获取配置logo
    async getConfig() {
      const res = await this.$server.post("/common/config", { type: "twd" });
      let val = {};
      res.data.forEach((vo) => {
        val[vo.name] = vo.value;
      });
      let imgurl = this.$server.url.imgUrl.replace("/api", "");
      val.logo = imgurl + val.logo;

      if (process.env.NODE_ENV === "development") {
        val.logo = require("../../assets/home/<USER>");
      }
      this.cfg = val;
    },
  },
};
</script>

<style scoped lang="less">
.page {
  min-height: 100vh;
  padding: 1.2rem 0.2rem 0.2rem;
  flex-direction: column;
  background: #10100f;

  .logo {
    width: 100%;
    // width: 2.92rem;
    // height: 0.88rem;
    // border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .bottom {
    width: 100%;
    .jzz {
      width: 0.22rem;
      height: 0.22rem;
      margin-right: 0.05rem;
    }

    .txt {
      font-weight: 500;
      font-size: 0.12rem;
      // color: #6a6a6a;
      color: #ffffff;
      margin-bottom: 0.05rem;
    }
  }
}
</style>
