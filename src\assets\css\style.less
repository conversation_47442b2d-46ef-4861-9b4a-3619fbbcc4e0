@charset "utf-8";
html,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
input,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: baseline;
  background: transparent;
}

// @font-face {
// font-family: 'PingFang-TC-Medium';
// src: url('./PingFang-TC-Medium.otf');
// }

body {
  min-width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #000000;
  background: #F6F8FE;
}

* {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  box-sizing: border-box;
  font-size: 0.14rem;
  font-weight: 500;
  // font-family: PingFang TC, PingFang TC;
  font-family: PingFang SC, PingFang SC;
}

.van-dialog__header {
  color: #000;
}

// 这个会影响下拉刷新的组件的显示
.van-pull-refresh {
  overflow: unset !important;
}
.flex {
  display: flex;
  align-items: center;
  &.flex-a{
	  justify-content: space-around;
  }
  &.flex-b {
    justify-content: space-between;
  }
  &.flex-c {
    justify-content: center;
  }
  &.flex-s{
	  justify-content: flex-start;
  }
  &.flex-e {
    justify-content: flex-end;
  }
}

.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-wrap{
	flex-wrap: wrap;
}
.flex-column-item{
	display: flex;
	flex-direction: column;
	align-items: center;
}
.flex-only{
	display: flex;
}

.t-center,
.t-c {
  text-align: center;
}
.t-right,
.t-r {
  text-align: right;
}
.no-data {
  text-align: center;
  margin: 1rem 0;
}

.top-pad {
  padding-top: 0.6rem;
}
.red {
	color: #FF2415;
	}
.green {
	color: #2BC488;
	}
.b-btn {
	height: 0.39rem;
	background: linear-gradient( 180deg, #0DD0C7 0%, #0789DF 100%);
	border-radius: 0.2rem;
	line-height: 0.39rem;
	text-align: center;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 0.14rem;
	color: #FFFFFF;
	margin: 0.2rem 0;
}
.b-btn02 {
	height: 0.39rem;
	border: 0.01rem solid #078BDE;
	border-radius: 0.2rem;
	line-height: 0.39rem;
	text-align: center;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 0.14rem;
	color: #078BDE;
	margin: 0.2rem 0;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
	.red {
		color: #DA5B4C;
	}
	.green {
		color: #2BC488;
	}
.icon {
	&.zh {
	  width: 0.15rem;
	  height:0.17rem;
	  background: url("../v1/zh.png") no-repeat center/100%;
	}
	&.mm {
	  width: 0.16rem;
	  height:0.16rem;
	  background: url("../v1/mm.png") no-repeat center/100%;
	}
	&.yqm {
	  width: 0.16rem;
	  height:0.16rem;
	  background: url("../v1/yqm.png") no-repeat center/100%;
	}
  &.bg {
    width: 100%;
    height:1.65rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zy1 {
    width: 0.15rem;
    height: 0.11rem;
    background: url("../v2/by.png") no-repeat center/100%;
  }
  &.by {
    width: 0.15rem;
    height: 0.1rem;
    background: url("../v2/zy.png") no-repeat center/100%;
  }
  &.zy12 {
    width: 0.15rem;
    height: 0.11rem;
    background: url("../v1/by.png") no-repeat center/100%;
  }
  &.by12 {
    width: 0.15rem;
    height: 0.11rem;
    background: url("../v1/zy.png") no-repeat center/100%;
  }
  &.jl {
    width: 0.24rem;
    height: 0.24rem;
    background: url('../home/<USER>') no-repeat center/100%;
  }
  &.ss {
    width: 0.19rem;
    height: 0.2rem;
    background: url('../v2/ss.png') no-repeat center/100%;
  }
  &.xx {
    width: 0.18rem;
    height: 0.21rem;
    background: url('../v2/xx.png') no-repeat center/100%;
  }
  &.addzx {
    width: 0.18rem;
    height: 0.21rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.cutzx {
    width: 0.18rem;
    height: 0.21rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.cz {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tx {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zj {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sx {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.bageye {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.bagby {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.lines {
    width: 0.91rem;
    height: 0.61rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jzxl {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zxl {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sczx {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.wxz {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.xz {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../v1/xz.png") no-repeat center/100%;
  }
  &.gb {
    width: 0.3rem;
    height:0.3rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.yxz {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zd {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jzx {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.bjzx {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zq {
    width: 0.08rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.dq {
    width: 0.08rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.dq {
    width: 0.08rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.gm {
    width: 50%;
    height: 0.25rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.hbg {
    width: 100%;
    // height: 1.8rem;
    height: 1.5rem;
    background: url("../home/<USER>") no-repeat;
    background-size: 100% 100%;
  }
  &.zjmx {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jta {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }


  &.zy {
    width: 0.24rem;
    height: 0.22rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.rjt {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v1/back.png") no-repeat center/100%;
  }
  &.close {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v2/close.png") no-repeat center/100%;
  }
  &.wdzc {
    width: 0.28rem;
    height: 0.27rem;
    background: url("../v1/wdzcIcon.png") no-repeat center/100%;
  }
  &.wdcc {
    width: 0.26rem;
    height: 0.27rem;
    background: url("../v1/wdccIcon.png") no-repeat center/100%;
  }
  &.m1 {
    width: 0.28rem;
    height: 0.27rem;
    background: url("../v1/smrzIcon.png") no-repeat center/100%;
  }
  &.m2 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.m3 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.m4 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.m5 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.m6 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.m7 {
    width: 0.25rem;
    height: 0.27rem;
    background: url("../v1/lxkfIcon.png") no-repeat center/100%;
  }
  &.jtr {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tz {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tz1 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.kf1 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zk {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.mby {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.mzy {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.xzl {
    width: 0.22rem;
    height: 0.22rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.h1 {
    width: 0.34rem;
    height: 0.35rem;
    background: url("../v1/h1.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h2 {
    width: 0.33rem;
    height: 0.35rem;
    background: url("../v1/h2.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h3 {
    width: 0.35rem;
    height: 0.35rem;
    background: url("../v1/h3.png") no-repeat center/100%;
  }
  &.h4 {
    width: 0.32rem;
    height: 0.35rem;
    background: url("../v1/h4.png?t=1") no-repeat center/100%;
  }
  &.jp{
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/xingujingpai.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h5 {
    width: 0.36rem;
    height: 0.35rem;
    background: url("../v1/h5.png") no-repeat center/100%;
  }
  &.h6 {
    width: 0.35rem;
    height: 0.35rem;
    background: url("../v1/h6.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h7 {
    width: 0.35rem;
    height: 0.35rem;
    background: url("../v1/h7.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h8 {
    width: 0.35rem;
    height: 0.35rem;
    background: url("../v1/h8.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h9 {
    width: 0.35rem;
    height: 0.35rem;
    background: url("../v1/h9.png") no-repeat center/100%;
  }
  &.h10 {
    width: 0.44rem;
    height: 0.44rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.h11 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/hlgp.png?t=1");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.h12 {
    width: 0.44rem;
    height: 0.44rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zxadd {
    width: 0.16rem;
    height:0.16rem;
    background: url("../v2/zxadd.png") no-repeat center/100%;
  }

  &.bjbtn {
    width: 0.11rem;
    height: 0.11rem;
    background: url("../v2/edit.png") no-repeat center/100%;
  }
  &.zf {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.df {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.back {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v1/rjt.png") no-repeat center/100%;
  }
  &.jt1 {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v1/back.png") no-repeat center/100%;
  }
  &.dj {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jzz {
    width: 0.58rem;
    height: 0.58rem;
    background: url("../home/<USER>") no-repeat center/100%;
    margin: 0 auto;
  }
  &.user {
    width: 0.29rem;
    height: 0.29rem;
    background: #fff url("../200.png") no-repeat center/100%;
	border-radius: .05rem;
  }
  &.set {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.jt2 {
    width: 0.22rem;
    height: 0.22rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.checked {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.nocheck {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sf {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.add {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jj {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sou {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
    margin-right: 0.05rem;
  }

  &.copy {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.down {
    width: 0.08rem;
    height: 0.05rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.up {
    width: 0.08rem;
    height: 0.05rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.bk{
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sz{
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }


  &.jt {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.xl {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.up1 {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.down1 {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sou2 {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tm {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../v2/time.png") no-repeat center/100%;
  }
  &.s1 {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.x1 {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.ysc {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.gd0 {
    width: 1.26rem;
height: 0.8rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.gd1 {
    width: 1.26rem;
height: 0.8rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.gd2 {
    width: 1.26rem;
    height: 0.8rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.dd1 {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zz1 {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.rns {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.kset {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.sou2 {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.usd {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.qh {
    width: 0.39rem;
    height: 0.39rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.thb {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.del {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v2/del.png") no-repeat center/100%;
  }

  &.back1 {
    width: 0.1rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.sou1 {
    width: 0.16rem;
    height: 0.17rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.rz {
    width: 0.12rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.logo {
    width: 1.35rem;
    height: 0.35rem;
    // background: url("../home/<USER>") no-repeat center/100%;
    img {
      width: 100%;
      height: 100%;
    }
  }

  &.a {
    width: 0.23rem;
    height: 0.25rem;
    background: url("../v1/a.png?t=1") no-repeat center/100%;
  }
  &.ac {
	width: 0.23rem;
	height: 0.25rem;
    background: url("../v1/ac.png?t=1") no-repeat center/100%;
  }
  &.a1 {
	width: 0.22rem;
	height: 0.23rem;
    background: url("../v1/a1.png?t=1") no-repeat center/100%;
  }
  &.ac1 {
    width: 0.22rem;
    height: 0.23rem;
    background: url("../v1/ac1.png?t=1") no-repeat center/100%;
  }
  &.a2 {
    width: 0.23rem;
    height: 0.23rem;
    background: url("../v1/a2.png?t=1") no-repeat center/100%;
  }
  &.ac2 {
    width: 0.23rem;
    height: 0.23rem;
    background: url("../v1/ac2.png?t=1") no-repeat center/100%;
  }
  &.a3 {
    width: 0.22rem;
    height: 0.23rem;
    background: url("../v1/a3.png?t=1") no-repeat center/100%;
  }
  &.ac3 {
    width: 0.22rem;
    height: 0.23rem;
    background: url("../v1/ac3.png?t=1") no-repeat center/100%;
  }
  &.a4 {
    width: 0.25rem;
    height: 0.23rem;
    background: url("../v1/a4.png?t=1") no-repeat center/100%;
  }
  &.ac4 {
    width: 0.25rem;
    height: 0.23rem;
    background: url("../v1/ac4.png?t=1") no-repeat center/100%;
  }

  &.success {
    width: 0.97rem;
    height: 0.97rem;
    background: url("../login/sc.png") no-repeat center/100%;
  }
  &.txIcon {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../v2/txIcon.png") no-repeat center/100%;
  }
  &.more {
    width: 0.08rem;
    height: 0.13rem;
    background: url("../v2/more.png") no-repeat center/100%;
  }
}
