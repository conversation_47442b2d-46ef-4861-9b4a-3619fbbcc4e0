<template>
	<div class="page">
		<top-back :title="nameList[type]"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<!-- 申购 -->
			<div class="sg-list">
				<van-skeleton title :row="26" :loading="loading">
					<no-data v-if="!xinguList.length"></no-data>
					<div class="sg-item" v-for="(item, index) in xinguList" :key="index">
						<div class="top flex flex-b">
							<div class="">
								<div class="name">{{ item.stock_name || "-" }}</div>
								<div class="code">{{ item.stock_code || "-" }}</div>
							</div>
							<div class="status animate__animated animate__fadeIn">
								{{ $t(item.xgstate) }}
							</div>
						</div>

						<div class="data flex flex-b">
							<div class="data-item flex flex-b">
								<div class="t">承銷價</div>
								<div class="t1 ">
									{{ $formatMoney(item.apply_price) || "-" }}
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">中籤張數</div>
								<div class="t1">
									{{ $formatMoney(Number(item.lucky_total) / 1000, 0) || "-" }}
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">申購張數</div>
								<div class="t1">
									{{ $formatMoney(Number(item.apply_total) / 1000, 0) || "-" }}
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">應認繳金額</div>
								<div class="t1">
									{{ $formatMoney(item.subs_value) || "-" }}
								</div>
							</div>
							<!-- <div class="data-item flex flex-b ">
								<div class="t">報酬率</div>
								<div class="t1 price">{{ item.newstock.rate }}%</div>
							</div> -->
							<div class="data-item flex flex-b ">
								<div class="t">市值</div>
								<div class="t1 price">
									{{item.xgstate == "待中签"? "-": $formatMoney(item.market_value)}}
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">已認繳金額</div>
								<div class="t1">
									{{ $formatMoney(item.rjmoney) || "-" }}
								</div>
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
		</van-pull-refresh>
	</div>
</template>

<script>
	export default {
		name: "sgList",
		data() {
			return {
				marketType: 'taiwan', // 默认台湾市场
				loading: true,
				isLoading: false,
				xinguList: [
					// {
					// stock_name: 'stock_name',
					// stock_code: 'stock_code',
					// xgstate: '待中签',
					// apply_price: 10,
					// lucky_total: 111,
					// apply_total: 1000,
					// subs_value: 1000,
					// rjmoney:100
					// },
				],
				nameList: ["申購記錄", "中籤記錄"],
				type: 0,
			};
		},
		computed: {
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.marketType === 'taiwan' ? 'twd' : 'usd';
			}
		},
		mounted() {
			if (this.$route.query.type) {
				this.type = this.$route.query.type;
			}
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.marketType = 'us';
			} else {
				this.marketType = 'taiwan';
			}
			this.getXingu();
		},

		methods: {
			onRefresh() {
				this.getXingu();
			},
			getXingu() {
				this.$server
					.post("/trade/usernewstocklist", {
						buy_type: 0,
						type: this.apiMarketType,
					})
					.then((res) => {
						this.loading = false;
						this.isLoading = false;
						if (res.status == 1) {
							let arr = [];
							if (this.type == 0) {
								// 申购记录显示待中签和未中位的
								arr = res.data.filter(
									(item) => item.status == 0 || item.status == 4
								);
							} else {
								arr = res.data.filter(
									(item) => item.status != 0 && item.status != 4
								);
							}
							this.xinguList = arr;
							this.xinguList.reverse();
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.15rem 0.15rem;
		min-height: 100vh;
	}

	.sg-list {
		.sg-item {
			margin-bottom: 0.15rem;
			padding-bottom: 0.1rem;
			border-bottom: 0.01rem solid #EAEDF8;
			.top {
				margin-bottom: 0.1rem;
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #182133;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #69717D;
					margin-top: 0.05rem;
				}

				.status {
					background: #FFFFFF;
					border-radius: 0.13rem;
					padding: 0.05rem 0.1rem;
					color: #078bde;
					text-align: center;
					font-size: 0.12rem;

					&.pc {
						background-color: #f8f8f8;
						color: #000;
					}
				}
			}

			.data {
				text-align: center;
				flex-wrap: wrap;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #69717D;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #182133;
				}

				.price {
					// color: #c5585e;
				}

				.data-item {
					line-height: 0.3rem;
					width: 46%;
				}
			}
		}
	}
</style>