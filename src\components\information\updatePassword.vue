<template>
	<div class="page ">
		<top-back title="修改密碼"></top-back>

		<div class="tabs">
			<div class="tab-item flex flex-b" v-for="(item, i) in tabList" :key="i" @click="goUrl(item.url)">
				<div class="flex ">
					<!-- <div class="icon animate__animated animate__fadeIn" :class="item.icon"></div> -->
					<div class="t">{{ item.name }}</div>
				</div>
				<div class="icon more animate__animated animate__fadeIn"></div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "fundPass",
		props: {},
		data() {
			return {
				show: false,
				show1: false,
				show2: false,
				isChg: false,
				userInfo: {},
				form: {
					old_pass: "",
					new_pass: "",
					new_pass2: "",
				},
				tabList: [
					{
						name: "登入密碼",
						icon: "m2",
						url: "/information/loginPass",
					},
					{
						name: "資金密碼",
						icon: "m3",
						url: "/information/fundPass",
					},
					
				],
			};
		},
		components: {},
		created() {
			this.getUserInfo();
		},
		computed: {},
		methods: {
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						this.isChg = !!res.data.passwords;
					}
				});
			},
			goUrl(url) {
				if (url == "kefu") {
					this.getConfig();
				} else {
					this.$toPage(url);
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.tabs {
		margin: 0.5rem 0.15rem 0;
		.tab-item {
			margin: 0.1rem 0;
			height: 0.5rem;
			padding: 0 0.1rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
	
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #182133;
			}
		}
	}
	.page {
		padding: 0.2rem 0 0;
		min-height: 100vh;
		background: #f7f7f7;

		.cot {
			.bg {
				background: #ffffff;
				border-radius: 0.12rem;
				padding: 0.1rem 0.15rem;
			}
			.tt {
				font-size: 0.16rem;
				margin-bottom: 0.15rem;

				.tt1 {
					font-size: 0.12rem;
					color: #777777;
					margin-top: 0.05rem;
				}
			}

			.item {
				// margin-bottom: 0.2rem;
				.t {
					font-weight: 600;
					color: #0e1028;
					margin-bottom: 0.1rem;
				}

				.ipt {
					border-bottom: 0.01rem solid #cecece;
					height: 0.42rem;
					margin-bottom: 0.2rem;
					&.last {
						margin-bottom: 0;
					}
				}
				input {
					margin-right: 0.1rem;
					&::placeholder {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #182133;
					}
				}
			}
		}
	}
</style>