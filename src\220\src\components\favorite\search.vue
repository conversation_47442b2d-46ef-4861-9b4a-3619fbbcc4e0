<template>
	<div class="page ">
		<top-back :title="$t('search').title"></top-back>
    <div class="nav-box flex">
      <div class="nav-item" v-for="(item, index) in navList" :key="index"
           :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
        {{ item.name }}<span></span>
      </div>
    </div>

		<div class="cot">
			<div class="top">
				<div class="flex flex-b  search">
					<div class="icon sou animate__animated animate__fadeIn"></div>

					<input class="flex-1" @input="handleInput" type="text" v-model="keyword"
						:placeholder="$t('search').txt2" />
				</div>
			</div>

			<!-- 历史搜索 -->
			<div class="keyword-block" v-if="!!oldKeywordList.length">
				<div class="flex flex-b">
					<div class="keyword-list-header">{{ $t("search").txt3 }}</div>
					<div @click="clearHistory">
						<!-- <van-icon name="close" size=".2rem" /> -->
						<div class="icon sczx animate__animated animate__fadeIn"></div>
					</div>
				</div>
				<div class="keyword">
					<div v-for="(keyword, index) in oldKeywordList" @click="doSearch(keyword)" :key="index">
						{{ keyword }}
					</div>
				</div>
			</div>

			<div class="box" v-show="keyword" v-if="listData.length > 0">
				<!-- 股票列表 -->
				<div class="">
					<div class="titles flex flex-b">
						<div class="flex-1">{{ $t("market").txt1 }}</div>
						<div class="flex-1 t-right">{{ $t("market").txt2 }}</div>
						<div class="flex-1 t-right">{{ $t("market").txt3 }}</div>
					  </div>
					<div class="list">
						<div class="list-item flex flex-b" v-for="(item, idx) in listData" :key="idx" @click=" $toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item) ">
							<div class="flex-2">
								<div class="name">{{ item.local_name }}</div>
								<div class="code">{{ item.symbol }}</div>
							</div>

							<div class="flex-1 t-center">
								<div class="price red" :class="{ green: Number(item.gainValue) < 0, }">
									{{ $formatMoney(item.price) || 0 }}
								</div>
							</div>

							<div class="flex-1 flex flex-e">
								<div>
									<div class="per red" :class="{ 'green': Number(item.gainValue) < 0, }">
                    {{stockType=='try'?'%':''}}{{ item.gain > 0 ? "+" : "" }}{{ item.gain || 0 }}{{stockType=='try'?'':'%'}}
									</div>
									<div class="per red" :class="{ green: Number(item.gainValue) < 0, }">
										{{ item.gainValue > 0 ? "+" : "" }}{{ item.gainValue || 0 }}
									</div>
								</div>
								<div class="icon animate__animated animate__fadeIn"
									:class="item.isZx ? 'checked' : 'nocheck'" @click.stop="optional(item)"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "search",
		props: {},
		data() {
			return {
        navList: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
				currmentIndex: 0,
				keyword: "",
				imgList: {
					search: "",
					delete: "",
					hook: "",
					jia: "",
				},
				oldKeywordList: [],
				listData: [],
				flag: true,
				account: "",
			};
		},
		components: {},
		created() {
			this.account = this.$storage.get("account");
			this.oldKeywordList = !!this.$storage.get(this.account + "searchHistory") ?
				this.$storage.get(this.account + "searchHistory") :
				[];
		},
		mounted() {
			// 使用 lodash 的防抖函数
			this.handleInput = _.debounce(this.handleInput, 1000);
		},
		methods: {
      changeStock(type){
        this.stockType = type
        this.searchFn();
      },
			handleInput() {
				this.searchFn();
			},
			changePage(index) {
				this.currmentIndex = index;
			},
			optional(obj) {
				this.$refs.loading.open(); //开启加载

				if (!obj.isZx) {
					this.$server
						.post("/user/addOptional", {
							symbol: obj.symbol,
							type: this.stockType
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.listData = this.listData.map((item) => {
									if (item.symbol == obj.symbol) {
										item.isZx = true;
									}

									return item;
								});
							}
						});
				} else {
					this.$server
						.post("/user/removeOptional", {
							symbol: obj.symbol,
							type: this.stockType
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.listData = this.listData.map((item) => {
									if (item.symbol == obj.symbol) {
										item.isZx = false;
									}

									return item;
								});
							}
						});
				}
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: this.stockType
				}).then((res) => {
					if (res.status == 1) {
						// 判断当前是否在自选列表里面
						let arr = this.listData.map((item) => {
							res.data.forEach((it) => {
								if (item.symbol == it.symbol) {
									item.isZx = true;
								}
							});
							return item;
						});

						this.listData = arr;
					}
				});
			},
			doSearch(str) {
				this.keyword = str;
				this.searchFn();
			},
			clearHistory() {
				this.oldKeywordList = [];
				this.$storage.remove(this.account + "searchHistory");
			},
			searchFn() {
				let that = this;
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/search", {
						content: this.keyword,
						type: this.stockType
					})
					.then((res) => {
						this.$refs.loading.close();

						console.log("res", res);

						if (res.status == 1) {
							res.data = res.data ? res.data : [];
							if (res.data.length == 1) {
								let searchHistory = this.oldKeywordList;
								if (searchHistory.length > 10) {
									searchHistory.shift();
								}
								if (!searchHistory.includes(this.keyword)) {
									searchHistory.push(this.keyword);
								}

								this.$storage.save(this.account + "searchHistory", searchHistory);

								this.oldKeywordList = searchHistory;
							}

							if (res.data.length > 0) {
								this.listData = res.data;
								// 判断是否自选
								this.getMine();
							}
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0;
	}

  .nav-box {
    width: 100%;
    height: .4rem;
    top: .5rem;
    padding: 0.15rem;
    .nav-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.14rem;
      color: #111111;
      text-align: center;
      margin-right: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span{
        width: .5rem;
        height: .03rem;
        background: transparent;
        margin-top: .05rem;
        display: block;
      }
      &.active {
        font-weight: 600;
        font-size: 0.14rem;
        color: #E10414;
        position: relative;
        span{
          background: #E10414;
        }
      }
    }
  }

	.cot {
		padding: 0.15rem;
		background: #fff;
		min-height: 100vh;
		border-radius: 0.13rem 0.13rem 0 0;
		.top {
			.search {
				border-radius: 0.24rem;
				border: 0.01rem solid #ebebeb;
				padding: 0.1rem 0.15rem;

				input {
					font-size: 0.14rem;
					color: #000;
					margin-left: 0.1rem;

					&::placeholder {
						color: #999;
					}
				}
			}
		}

		.keyword-block {
			padding: 0.15rem 0 0;

			.keyword-list-header {
				font-size: 0.14rem;
				font-weight: 500;
				color: #000;
			}

			.keyword {
				display: flex;
				flex-flow: wrap;
				align-items: center;
				padding: 0.1rem 0;

				div {
					background: #eff0f4;
					border-radius: 0.06rem;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0.1rem 0.15rem;
					font-size: 0.14rem;
					color: #000000;
					margin: 0 0.1rem 0.1rem 0;
				}
			}
		}
	}

	.box {
		padding: 0.2rem 0;
		.titles {
			div {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}

			margin-bottom: 0.1rem;
		}

		.list {
			.list-item {
				padding: 0.1rem 0;
				border-bottom: 0.01rem solid #ebebeb;

				&:last-child {
					border-bottom: 0;
				}
				.icon {
					margin-left: 0.05rem;
				}
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #333333;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-top: 0.05rem;
				}

				.t {
					font-size: 0.12rem;
					margin-top: 0.05rem;
				}

				.price {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.15rem;
					color: #333333;
				}

				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.15rem;
				}
			}
		}

		/* .red {
			color: #df4645;
		}

		.green {
			color: #3561e5;
		} */

		.red-bg {
			background-color: #CB0000;
		}

		.green-bg {
			background-color: #0068FF;
		}
	}
</style>