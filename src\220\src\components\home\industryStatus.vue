<template>
	<div class="wbg">
		<div class="top">
			<div class="t">{{ $t("newt").t70 }}</div>
		</div>

		<div class="nums">
			<div class="item flex flex-b" v-for="(item, i) in list" :key="i">
				<!-- <div class="t1">{{ $t("newt").t71 }}</div> -->
				<div class="t1 flex-2">{{ item.name }}</div>

				<div class="flex-3">
					<div class="line">
						<div class="inner" :style="`width:${showPercent(item.avg_returns) * 10}%;`"
							:class="item.avg_returns < 0 ? 'green-bg' : 'red-bg'"></div>
					</div>
				</div>
				<div class="per flex-1 t-r" :class="item.avg_returns < 0 ? 'green' : 'red'">
					{{ item.avg_returns < 0 ? "" : "+" }}{{ item.avg_returns }}%
				</div>

				<!-- <div class="per">
					<u-circle-progress border-width="16" width="160" :percent="showPercent(item.avg_returns)"
						inactive-color="#F4F5F7" :active-color="item.avg_returns < 0 ? '#30E0A1' : '#E73E59'"
						bg-color="transparent">
						<div class="u-progress-info">
							{{ item.avg_returns < 0 ? "" : "+" }}{{ item.avg_returns }}%
						</div>
					</u-circle-progress>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "industryStatus",
		components: {},
		data() {
			return {
				list: [],
			};
		},
		created() {},
		mounted() {
			this.getInfo();
		},
		computed: {
			showPercent() {
				return (str) => {
					if (!str || str === null || str == undefined) return 0;
					str = str.toString();
					let isNum = str.indexOf("-");
					if (isNum > -1) {
						let num1 = Number(str.replace("-", ""));
						num1 = num1 > 100 ? 100 : num1;
						return num1;
					} else {
						let num2 = Number(str);
						num2 = num2 > 100 ? 100 : num2;
						return num2;
					}
				};
			},
		},
		onLoad() {},
		methods: {
			getInfo() {
				this.$server.post("/parameter/hyxz", {
					type: this.$stockType
				}).then((res) => {
					if (res.status == 1) {
						res.data.data.forEach((item) => {
							if (item.avg_returns < 0) {
								item.per = (((-1 * item.avg_returns) / 5) * 100).toFixed(2) + "%";
							} else {
								item.per = (((1 * item.avg_returns) / 5) * 100).toFixed(2) + "%";
							}
						});

						this.list = res.data.data;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.wbg {
		padding: 0.12rem;
	}

	.top {
		padding: 0 0.1rem;
		margin-bottom: 0.15rem;

		.t {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
		}
	}

	.nums {
		background: #FFFFFF;
		border-radius: 0.13rem;
		padding: 0.1rem;

		.line {
			height: 0.14rem;
			background: #F5F5F5;
			border-radius: 0.03rem;
			height: 0.1rem;
			overflow: hidden;
			position: relative;
			margin-right: 0.16rem;
			.inner {
				position: absolute;
				top: 0;
				left: calc(50% + .02rem);
				z-index: 99;
			}
			&:after {
				content: '';
				display: block;
				width: .02rem;
				height: .1rem;
				background: #fff;
				position: absolute;
				left: 50%;
				top: 0;
			}
		}

		.item {
			padding: 0.1rem 0;
			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #666666;
			}

			.per {
				font-family: OPPOSans, OPPOSans;
				font-weight: normal;
				font-size: 0.14rem;
			}

			.green-bg {
				background-color: #00BB51;
				height: 100%;
				left: 0;
			}

			.red-bg {
				background-color: #FD4D4C;
				height: 100%;
				border-radius: 0 .02rem .02rem 0;
			}
		}
	}
</style>