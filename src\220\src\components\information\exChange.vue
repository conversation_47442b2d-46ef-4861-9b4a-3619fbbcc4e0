<template>
	<div class="page">
		<top-back :title="$t('exchange').title"></top-back>

		<div class="change-box">
			<!-- <div class="title">{{$t('exchange').txt2}}</div> -->
			<div class="itemBox">
				<div class="fromTo flex flex-b">
					<div>{{ $t("从") }}</div>
					<div>{{ $t("到") }}</div>
				</div>
				<div class="flex flex-b">
					<div class="change-box-list flex flex-1">
						<div class="currency" @click="showTypeFrom = true">
							<template v-if="typeIndexFrom === 'try'">TRY</template>
							<template v-if="typeIndexFrom === 'usd'">USD</template>
							<template v-if="typeIndexFrom === 'hkd'">HKD</template>
							<template v-if="typeIndexFrom === 'usdt'">USDT</template>
						</div>
						<div class="ipt flex">
							<div class="right flex" @click="showTypeFrom = true">
								<!-- <div v-if="typeIndexFrom === 'try'" class="try"></div>
								<div v-if="typeIndexFrom === 'usd'" class="usd"></div>
								<div v-if="typeIndexFrom === 'hkd'" class="hkd"></div>
								<div v-if="typeIndexFrom === 'usdt'" class="usdt"></div> -->
								<div class="icon xl"></div>
							</div>
						</div>
					</div>
					<div class="zh flex flex-c" @click="changeIpt">
						<img src="../../assets/mine/change.png" />
					</div>
					<div class="change-box-list flex flex-1">
						<div class="currency" :class="language === 'ar' ? 't-right' : ''" @click="showTypeTo = true">
							<template v-if="typeIndexTo === 'try'">TRY</template>
							<template v-if="typeIndexTo === 'usd'">USD</template>
							<template v-if="typeIndexTo === 'hkd'">HKD</template>
							<template v-if="typeIndexTo === 'usdt'">USDT</template>
						</div>
						<div class="ipt flex">
							<div class="right flex" @click="showTypeTo = true">
								<!-- <div v-if="typeIndexTo === 'try'" class="try"></div>
								<div v-if="typeIndexTo === 'usd'" class="usd"></div>
								<div v-if="typeIndexTo === 'hkd'" class="hkd"></div>
								<div v-if="typeIndexTo === 'usdt'" class="usdt"></div> -->
								<div class="icon xl"></div>
							</div>
						</div>
					</div>
				</div>
				
				<div class="jeBox">
					<div>{{$t('transactionRecord').txt3}}</div>
					<div class="flex inputBox flex-b">
						<div class="currency">
							<template v-if="typeIndexFrom === 'try'">TRY</template>
							<template v-if="typeIndexFrom === 'usd'">USD</template>
							<template v-if="typeIndexFrom === 'hkd'">HKD</template>
							<template v-if="typeIndexFrom === 'usdt'">USDT</template>
						</div>
						<input type="number" class="change-input flex-1" v-model="money" @input="changeMoney" :placeholder="$t('exchange').txt1" />
						<div class="all" @click="money=userInfo[typeIndexFrom],changeMoney()">{{$t('全部')}}</div>
					</div>
				</div>
				<div class="jeBox">
					<div>{{$t('transactionRecord').txt3}}</div>
					<div class="inputBox flex">
						<div class="currency" :class="language === 'ar' ? 't-right' : ''">
							<template v-if="typeIndexTo === 'try'">try</template>
							<template v-if="typeIndexTo === 'usd'">USD</template>
							<template v-if="typeIndexTo === 'hkd'">HKD</template>
							<template v-if="typeIndexTo === 'usdt'">USDT</template>
						</div>
						<div class="ip " :class="language === 'ar' ? 't-right' : ''">{{ $formatMoney(Money, 2) }}</div>
					</div>
				</div>
			</div>
			
			<div class="myMoney" v-if="userInfo">
				{{$t('可用资金')}}
				{{userInfo[typeIndexFrom]}}
			</div>
			
			<div class="big_btn" @click="submit">
				{{ $t('提交') }}
			</div>
		</div>

		<van-action-sheet v-model="showTypeFrom" :actions="typeList" @select="selectTypeFrom" />
		<van-action-sheet v-model="showTypeTo" :actions="typeList2" @select="selectTypeTo" />
	</div>
</template>

<script>
	export default {
		name: "exchangeRate",
		props: {},
		data() {
			return {
				language: "",
				loading: false,
				allList: [],
				typeList: [],
				typeList2: [],
				lang: this.$t("exchange"),
				typeIndexTo: "usd",
				typeIndexFrom: "try",
				showTypeFrom: false,
				showTypeTo: false,
				formTo: "tryusd",
				rate: 0.0,
				money: "",
				show: true,
				Money: 0,
				userInfo:null
			};
		},
		components: {},
		computed: {},
		created() {
			this.getConfig();
		},
		mounted() {
			document.title = this.$t('exchange').title;
			this.language = window.localStorage.getItem("language");

			this.typeList = [
				{
					name: "try",
					type: "try"
				},
				{
					name: "USD",
					type: "usd"
				},
				{
					name: "USDT",
					type: "usdt"
				},
			];
			this.allList = [
				{
					name: "try",
					type: "try"
				},
				{
					name: "USD",
					type: "usd"
				},
				{
					name: "USDT",
					type: "usdt"
				},
			];
			this.getTypeList();
			this.selectTypeFrom(this.typeList[0]);
		},
		methods: {
			changeMoney() {
				this.Money = parseFloat(this.rate*10000 * this.money*10000)/100000000;
			},
			changeIpt() {
				let _this = this;
				_this.money = "";
				_this.Money = 0;
				let indexFrom = _this.typeIndexFrom;
				let indexTo = _this.typeIndexTo;
				_this.typeIndexFrom = indexTo;
				_this.typeIndexTo = indexFrom;
				_this.formTo = _this.typeIndexFrom + _this.typeIndexTo;
				_this.getTypeList();
				_this.getUser();
				_this.getConfig();
			},
			submit() {
				let _this = this;
				if (!_this.money) {
					this.$toast(_this.lang.txt1);
					return;
				}
				_this.loading = true;
				let parameter = {
					money: _this.money,
					from: _this.typeIndexFrom,
					to: _this.typeIndexTo,
				};
				_this.$server.post("/user/rotation", parameter).then((str) => {
					
					if (parseInt(str.status) === 1) {
						this.$toast(_this.$t('兑换成功'));
						_this.loading = false;
					} else {
						this.$toast(_this.$t(str.msg));
						_this.loading = false;
					}
				});
			},
			selectTypeFrom(item) {
				let _this = this;
				_this.showTypeFrom = false;
				this.money = "";
				this.Money = 0;
				_this.typeIndexFrom = item.type;
				_this.getTypeList()
				_this.getUser();
				_this.getConfig();
			},
			selectTypeTo(item) {
				let _this = this;
				_this.showTypeTo = false;
				this.money = "";
				this.Money = 0;
				_this.typeIndexTo = item.type;
				_this.formTo = _this.typeIndexFrom + _this.typeIndexTo;
				_this.getUser();
				_this.getConfig();
			},
			getUser() {
				let _this = this;
				_this.$server.post("/user/getUserinfo").then((str) => {
					if (str) {
						if (parseInt(str.status) == 1) {
							_this.userInfo = str.data;
						}
					}
				});
			},
			getConfig() {
				let _this = this;
				_this.$server.post("/common/config",{
					type: 'all'
				}).then((str) => {
					if (str) {
						if (parseInt(str.status) === 1) {
							let num = 0;
							for (var i in str.data) {
								var row = str.data[i];
								if (row.name == _this.formTo) {
									num += 1;
									_this.rate = row.value;
								}
							}
							if (num == 0) {
								_this.rate = 0.0;
							}
						}
						console.log(_this.rate);
					}
				});
			},
			getTypeList() {
				let _this = this
				let list = _this.allList
				let newList = []
				let length = list.length
				let a
				for (a = 0; a < length; a++) {
					if (_this.typeIndexFrom !== list[a].type) {
						newList.push(list[a])
					}
				}
				_this.typeList2 = newList
				//console.log(_this.typeList2[0].type)
				_this.typeIndexTo = _this.typeList2[0].type
				_this.formTo = _this.typeIndexFrom + _this.typeIndexTo;
			}
		},
	};
</script>

<style scoped lang="less">
	.t-right {
		text-align: right;
		direction: rtl;
	}

	.big-Btn {
		margin-top: .3rem;
	}

	.page {
		padding: .5rem .12rem;
		min-height: 100vh;
	}
	
	.change-box {
		padding: .16rem 0 0;
		.title{
			font-weight: 500;
			font-size: .16rem;
			color: #24272C;
			margin-bottom: .1rem;
		}
		.itemBox{
			.fromTo{
				margin-bottom: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #111111;
				img{
					width:.04rem;
					display: block;
					margin: .15rem 0;
				}
			}
		}
	}
	
	.change-box-list {
		background: #FFFFFF;
		border-radius: 0.08rem;
		padding: 0.12rem;
		&:last-child{
			border-bottom: 0;
		}
		.currency {
			font-weight: 500;
			font-size: .16rem;
			color: #24272C;
		}

		.ipt {
			flex:none;
			input {
				flex: 1;
				background-color: transparent;
				border: 0;
				height: 100%;width:100%;
				font-size: .133rem;
				color: #333;
			}

			.ip {
				font-size: .133rem;
				color: #333;
				flex: 1;
			}

			.right {
				.xl {
					margin-left: .1rem;
				}

				.xls {
					width: .133rem;
					height: .133rem;
					margin-left: .1rem;
				}
			}
		}
	}
	.jeBox{
		background: #FFFFFF;
		border-radius: 0.13rem;
		padding: 0.12rem;
		margin: 0.1rem 0;
		.currency{
			padding-right: 0.1rem;
			border-right: 0.01rem solid #000;
		}
		.inputBox{
			margin-top: 0.1rem;
			padding: 0 0.1rem;
			width: 100%;
			height: 0.44rem;
			background: #F5F5F5;
			border-radius: 0.08rem;
		}
		input{
			margin-left: 0.1rem;
		}
		.ip{
			margin-left: 0.1rem;
		}
		.all{
			color:#f00;padding-left:.1rem;
		}
	}
	

	.sar {
		width: .167rem;
		height: .167rem;
		//background: url("../../assets/img/mine/sar.png") no-repeat center/100%;
	}

	.usd {
		width: .167rem;
		height: .167rem;
		//background: url("../../assets/img/mine/usd.png") no-repeat center/100%;
	}

	.hkd {
		width: .167rem;
		height: .167rem;
		//background: url("../../assets/img/mine/hkd.png") no-repeat center/100%;
	}

	.usdt {
		width: .167rem;
		height: .167rem;
		//background: url("../../assets/img/mine/usdt.png") no-repeat center/100%;
	}

	/* .xl {
		width: .09rem;
		height: .18rem;
		//background: url("../../assets/img/mine/arrow.png") no-repeat center/100%;
	} */

	.zh {
		flex:none;
		margin: 0 0.1rem;
		width: 0.63rem;
		height: 0.4rem;
		background: #FFFFFF;
		border-radius: 0.08rem;
		img{
			width:.2rem;
		}
	}
</style>