<template>
	<div class="pages">
		<div class="about">
			<div class="name">{{ jbzl.companyName }}</div>
			<div class="ms">{{ jbzl.description }}</div>
			<div class="txt">
				<div class="txt-item">
					<div class="t1">產業別</div>
					<div class="t">{{ jbzl.industryType }}</div>
				</div>
				<div class="txt-item t-c">
					<div class="t1">董事長</div>
					<div class="t">{{ jbzl.president }}</div>
				</div>
				<div class="txt-item t-r">
					<div class="t1">總經理</div>
					<div class="t">{{ jbzl.generalManager }}</div>
				</div>
				<div class="txt-item ">
					<div class="t1">發言人</div>
					<div class="t">{{ jbzl.spokesman }}</div>
				</div>
				<div class="txt-item t-c">
					<div class="t1">實收資本</div>
					<div class="t">{{ $formatMoney(jbzl.marketValueE) }}億</div>
				</div>
				<div class="txt-item t-r">
					<div class="t1">每股淨值</div>
					<div class="t">{{ $formatMoney(jbzl.perValue) }}圓</div>
				</div>
				<div class="txt-item">
					<div class="t1">外資持股</div>
					<div class="t">{{ jbzl.per }}%</div>
				</div>
				<div class="txt-item t-c">
					<div class="t1">成立日期</div>
					<div class="t">{{ jbzl.listingDateS }}</div>
				</div>
				<div class="txt-item t-r">
					<div class="t1">上市日期</div>
					<div class="t">{{ jbzl.conferenceDate }}</div>
				</div>
				<div class="txt-item">
					<div class="t1">過戶機構電話</div>
					<div class="t">{{ jbzl.agencyPhone }}</div>
				</div>
			</div>
		</div>
		<!-- 切換顯示數據列表 -->
		<div class="bg">
			<div class="nav-boxs flex flex-b">
				<div class="nav-item" v-for="(item, index) in navList" :key="index"
					:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
					{{ item.name }}
				</div>
			</div>
			<div class="cmfx" v-if="currmentIndex == 0">
				<div class="title">
					<div class="t">財報分析</div>
				</div>
				<div class="" v-for="(item, index) in sy.range" :key="index + 'a'">
					<div class="tt flex flex-b">
						<div class="flex-1">項目</div>
						<div class="flex-1 t-c">{{ item }}</div>
						<div class="flex-1 t-r">{{ item }}</div>
					</div>
					<div class="item flex flex-b" v-for="(item2, index2) in sy.datas" :key="index2 + 'aa'">
						<div class="flex-1">{{ item2.name }}</div>
						<div class="flex-1 t-c">
							{{ $formatMoney(item2.datasets[index].amount, 0) || 0 }}
						</div>
						<div class="flex-1 t-r">
							{{ $formatMoney(item2.datasets[index].percent) || 0 }}
						</div>
					</div>
				</div>
			</div>
			<div class="cmfx" v-if="currmentIndex == 1">
				<div class="title">
					<div class="t">資產負債</div>
				</div>
				<div v-for="(item, index) in fzzc.range" :key="index + 'b'">
					<div class="tt flex flex-b">
						<div class="flex-1">項目</div>
						<div class="flex-1 t-c">{{ item }}</div>
						<div class="flex-1 t-r">{{ item }}</div>
					</div>
					<div class="item flex flex-b" v-for="(item2, index2) in fzzc.datas" :key="index2 + 'bb'">
						<div class="flex-1">{{ item2.name }}</div>
						<div class="flex-1 t-c">
							{{ $formatMoney(item2.datasets[index].amount, 0) }}
						</div>
						<div class="flex-1 t-r">
							{{ $formatMoney(item2.datasets[index].percent) }}
						</div>
					</div>
				</div>
			</div>
			<div class="cmfx" v-if="currmentIndex == 2">
				<div class="title">
					<div class="t">財務比例</div>
				</div>
				<div v-for="(item, index) in cwbl.range" :key="index + 'c'">
					<div class="tt flex flex-b">
						<div class="flex-1">項目</div>
						<div class="flex-1 t-c">{{ item }}</div>
						<div class="flex-1 t-r">{{ item }}</div>
					</div>
					<div class="item flex flex-b" v-for="(item2, index2) in cwbl.datas" :key="index2 + 'cc'">
						<div class="flex-1">{{ item2.name }}</div>
						<div class="flex-1 t-c">
							{{ $formatMoney(item2.datasets[index].amount, 0) || "-" }}
						</div>
						<div class="flex-1 t-r">
							{{ $formatMoney(item2.datasets[index].percent) || "-" }}
						</div>
					</div>
				</div>
			</div>
			<div class="cmfx" v-if="currmentIndex == 3">
				<div class="flex flex-b title">
					<div class="t">籌碼分析</div>
					<div class="time">
						{{ $formatDate("YYYY-MM-DD", cmfxEnd * 1000) }} -
						{{ $formatDate("YYYY-MM-DD", cmfxStart * 1000) }}
					</div>
				</div>
			
				<div class="tt flex flex-b">
					<div class=" flex-1">項目</div>
					<div class="flex-1 t-c">人數</div>
					<div class="flex-1 t-r">持股比例</div>
				</div>
			
				<div class="item flex flex-b" v-for="(item, index) in cmfx.shortMarginPercent" :key="index + 'dd'">
					<div class="flex-1">
						{{ $formatMoney(cmfx.margin[index], 0) || 0 }}
					</div>
					<div class="flex-1 t-c">
						{{ $formatMoney(cmfx.short[index], 0) || 0 }}
					</div>
					<div class="flex-1 t-r">{{ $formatMoney(item) || 0 }}%</div>
				</div>
			
				<div class="item flex flex-b">
					<div class="flex-1">合計</div>
					<div class="flex-1 t-c">{{ $formatMoney(cmfxNum) }}</div>
					<div class="flex-1 t-r">{{ parseFloat(cmfxPs).toFixed(2) }}%</div>
				</div>
			</div>
			<div class="cmfx" v-if="currmentIndex == 4">
				<div class="flex flex-b title">
					<div class="t">歷史走勢</div>
				</div>
			
				<div class="tt flex flex-b">
					<div class="flex-1">時間</div>
					<div class="flex-1 t-c">成交價</div>
					<div class="flex-1 t-c">漲跌</div>
					<div class="flex-1 t-r">成交張數</div>
				</div>
			
				<div class="item flex flex-b" v-for="(item, index) in cjhzList" :key="index + 'e'">
					<div class="flex-1">{{ item.time }}</div>
					<div class="flex-1 t-c">{{ $formatMoney(item.price) }}</div>
					<div class="flex-1 t-c" :class="item.change > 0 ? 'red' : 'green'">
						{{ $formatMoney(item.change) }}
					</div>
					<div class="flex-1 t-r">{{ $formatMoney(item.volumeK) }}</div>
				</div>
				<div class="more" @click="getCjhz">點擊加載更多</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>
<script>
	export default {
		name: "gkData",
		props: {
			symbol: {
				type: String,
				default: "",
			},
		},
		data() {
			return {
				currmentIndex: 0,
				navList: [{
						name: "財報分析",
						type: 0
					},
					{
						name: "資產負債",
						type: 1
					},
					{
						name: "財務比例",
						type: 2
					},
					{
						name: "籌碼分析",
						type: 3
					},
					{
						name: "歷史走勢",
						type: 4
					},
				],
				footIndex: 4,
				lastDate: "",
				cjhzList: [],
				cmfx: {},
				cmfxPs: 0,
				cmfxNum: 0,
				cmfxStart: "",
				cmfxEnd: "",
				cmfxYear: "",
				jqpd: [],
				gl: [],
				sy: [],
				fzzc: [],
				cwbl: [],
				jbzl: {},
				scyq: {},
				scyqFull: 0,
				fenZuList: [],
			};
		},

		watch: {
			symbol: {
				handler(newValue, oldValue) {
					if (newValue && newValue !== oldValue) {
						this.id = newValue;
						this.getAllData();
					}
				},
				// deep: true  // 深度监听
				immediate: true, // 第一次改变就执行
			},
		},

		computed: {},
		mounted() {
			this.$refs.loading.open();

			setTimeout(() => {
				this.$refs.loading.close();
			}, 2000);
		},
		methods: {
			changeNav(index) {
				this.currmentIndex = index;
			},
			getAllData() {
				this.getCjhz();
				this.getRzrq();
				// this.getJspd();
				this.getRzrqlb();
				this.getJbzl();
				this.getZcfzb();
				this.getCwbl();
				this.getSyb();
				this.getScyq();
			},
			getCjhz() {
				this.$server
					.post("/parameter/cjhz", {
						time: this.lastDate,
						symbol: this.id,
					})
					.then((res) => {
						function getTime(timestamp) {
							let date = new Date(parseInt(timestamp)); //时间戳为13位的话需要parseInt下
							let Y = date.getFullYear() + "-";
							let M =
								(date.getMonth() + 1 < 10 ?
									"0" + (date.getMonth() + 1) :
									date.getMonth() + 1) + "-";
							let D =
								(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) +
								" ";
							let h =
								(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) +
								":";
							let m =
								(date.getMinutes() < 10 ?
									"0" + date.getMinutes() :
									date.getMinutes()) + ":";
							let s =
								date.getSeconds() < 10 ?
								"0" + date.getSeconds() :
								date.getSeconds();
							return M + D + h + m + s;
						}
						if (res.status === 1) {
							this.lastDate =
								res.data.priceByTimes[res.data.priceByTimes.length - 1].time;
							for (let i = 0; i < res.data.priceByTimes.length; i++) {
								res.data.priceByTimes[i].time = getTime(
									Date.parse(res.data.priceByTimes[i].time)
								);
							}
							this.cjhzList = this.cjhzList.concat(res.data.priceByTimes);
						}
					});
			},
			getRzrq() {
				this.$server.post("/parameter/jh_rzrq", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.cmfx = res.data.data.datasets;
						let a;
						for (a = 0; a < this.cmfx.shortMarginPercent.length; a++) {
							this.cmfxPs += this.cmfx.shortMarginPercent[a];
							this.cmfxNum += this.cmfx.short[a];
						}
						this.cmfxStart =
							res.data.data.time[this.cmfx.shortMarginPercent.length - 1];
						this.cmfxEnd = res.data.data.time[0];
						this.fullDateYear(this.cmfxStart * 1000);
					}
				});
			},
			// 未展示數據 棄用
			getJspd() {
				this.$server.post("/parameter/jh_jspd", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.jqpd = res.data.data.rates;
					}
				});
			},
			getRzrqlb() {
				this.$server
					.post("/parameter/jh_rzrqlb", {
						symbol: this.id
					})
					.then((res) => {
						if (res.status === 1) {
							this.gl = res.data.data;
						}
					});
			},
			getSyb() {
				this.$server.post("/parameter/jh_syb", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.sy = res.data.data;
					}
				});
			},
			getZcfzb() {
				this.$server.post("/parameter/jh_zcfzb", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.fzzc = res.data.data;
					}
				});
			},
			getCwbl() {
				this.$server.post("/parameter/jh_cwbl", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.cwbl = res.data.data;
					}
				});
			},
			getJbzl() {
				this.$server.post("/parameter/jh_jbzl", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.jbzl = res.data.data;
					}
				});
			},
			getScyq() {
				this.$server.post("/parameter/jh_scyq", {
					symbol: this.id
				}).then((res) => {
					if (res.status === 1) {
						this.scyq = res.data.data;
						this.scyqFull =
							this.scyq.buy +
							this.scyq.under +
							this.scyq.hold +
							this.scyq.over +
							this.scyq.sell;
					}
				});
			},
			fullDateYear(time) {
				let date = new Date(time);
				this.cmfxYear = date.getFullYear();
			},
			fullDate(time) {
				let date = new Date(time);
				let Y = date.getFullYear() + "-";
				let M =
					(date.getMonth() + 1 < 10 ?
						"0" + (date.getMonth() + 1) :
						date.getMonth() + 1) + "-";
				let D = date.getDate() + " ";
				return Y + M + D;
			},
		},
	};
</script>
<style scoped lang="less">
	.pages {
		padding: 0.15rem 0.1rem;
	}
	.bg{
		margin: 0.1rem 0;
		background: #FFFFFF;
		box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
		border-radius: 0.07rem;
		padding: 0 0.1rem;
	}
	.nav-boxs {
		margin: 0 0 0.1rem;
		.nav-item {
			padding: 0.1rem 0;
			flex: 1;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #405476;
			text-align: center;
			position: relative;
			&::after {
				content: "";
				width: 100%;
				height: 0.02rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}

			&.active {
				color: #DCA25C;

				&::after {
					background: #DCA25C;
				}
			}
		}
	}

	.about {
		background: #FFFFFF;
		box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
		border-radius: 0.07rem;
		padding: 0.1rem;
		.name {
			font-weight: 500;
			font-size: 0.14rem;
			color: #000000;
			font-family: PingFang TC, PingFang TC;
			text-align: center;
		}
		.ms {
			font-size: 0.12rem;
			color: #858585;
			margin: 0.15rem 0;
		}

		.txt {
			flex-wrap: wrap;
			
			.txt-item {
				margin-bottom: 0.2rem;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #333333;
				}
				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #405476;
				}
			}
		}
	}

	.cmfx {
		padding: 0.1rem 0;
		.title {
			.t {
				font-family: PingFang TC, PingFang TC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #353535;
			}

			.time {
				font-size: 0.12rem;
				color: #555555;
			}
		}

		.tt {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #e3e3e3;

			div {
				font-size: 0.12rem;
				color: #787878;
			}
		}

		.item {
			padding: 0.15rem 0;
			border-bottom: 0.01rem solid #e3e3e3;

			div {
				font-family: PingFang TC, PingFang TC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #272727;
			}
		}

		.more {
			text-align: center;
			font-size: 0.12rem;
			color: #555555;
			padding: 0.1rem 0;
		}

		.green {
			color: #6bb831;
		}

		.red {
			color: #cf2829;
		}
	}
</style>