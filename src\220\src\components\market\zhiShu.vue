<template>
	<div class="page zhiShu">
		<top-back :title="$t('market').fast2"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
      <div class="nav-box flex">
        <div class="nav-item" v-for="(item, index) in navList" :key="index"
             :class="{ active: stockType == item.type }" @click="changeStock(item.type)">
          {{ item.name }}<span></span>
        </div>
      </div>
			
			<div class="list">
				<div class="flex flex-b titles">
					<div class="flex-1">{{ $t("newt").t57 }}</div>
					<div class="flex-1 t-r">{{ $t("newt").t58 }}</div>
					<div class="flex-1 t-r">{{ $t("newt").t59 }}</div>
				</div>
			
				<div class="lists">
					<div class="list-item flex flex-b" v-for="(item, idx) in list" :key="idx"
						@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}&stockType=${stockType}`, item)">
						<div class="flex col">
							<div class="img" v-if='item.logo'>
								<img :src="item.logo" />
							</div>
							<div>
								<div class="name"> {{ item.name }} </div>
								<div class="code"> {{ item.symbol }} </div>
							</div>
						</div>
						<div class="t-r col">
							<div class="price red" :class="{ green: Number(item.gainValue) < 0, }">
								{{ $formatMoney(item.price,2) || 0 }}
							</div>
			
							<!-- <div class="t red" :class="{ green: Number(item.gainValue) < 0, }" >
							  {{ item.gainValue > 0 ? "+" : "" }}{{ item.gainValue || 0 }}
							</div> -->
						</div>
			
						<div class="t-r col">
							<div class="per red" :class="{ 'green': Number(item.gainValue) < 0, }">
                {{stockType=='try'?'%':''}}{{ item.gainValue > 0 ? "+" : "" }}{{ item.gain.toFixed(2) }}{{stockType=='try'?'':'%'}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
	</div>
</template>

<script>
	export default {
		name: "zhiShu",
		components: {},
		data() {
			return {
				loading: true, //控制显示骨架屏占位显示
				isLoading: false,
				type: "returns_top",
				typeList: [{
						name: this.$t("newt").t60,
						type: "returns_top",
					},
					{
						name: this.$t("newt").t61,
						type: "returns_bottom",
					},
					{
						name: this.$t("newt").t62,
						type: "new_high_price",
					},
					{
						name: this.$t("newt").t63,
						type: "new_low_price",
					},
				],
				list: [],
        navList: [
          {
            name: this.$t("j1"),
            type: 'try',
          },
          {
            name: this.$t("j2"),
            type: 'usd',
          },
        ],
        stockType: 'try',
			};
		},
		created() {},
		mounted() {
			this.getInfo();
		},
		onLoad() {},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.getInfo();
			},
      changeStock(type){
        this.stockType = type
        this.getInfo();
      },
			getInfo() {
				this.$server.post("/parameter/zhishu", {
					type: this.stockType
				}).then((res) => {
					this.loading = false;
					this.isLoading = false;
					if (res&&res.data) {
						this.list = res.data;
					}
				});
			},
		},
	};
</script>

<style lang="less">
	.page {
		padding: 0.5rem 0 0;
		min-height: 100vh;
	}

  .nav-box {
    width: 100%;
    height: .4rem;
    top: .5rem;
    padding: 0.15rem;
    .nav-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.14rem;
      color: #111111;
      text-align: center;
      margin-right: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span{
        width: .5rem;
        height: .03rem;
        background: transparent;
        margin-top: .05rem;
        display: block;
      }
      &.active {
        font-weight: 600;
        font-size: 0.14rem;
        color: #E10414;
        position: relative;
        span{
          background: #E10414;
        }
      }
    }
  }
	.zhiShu{
		
		.list {
			background: #fff;
			border-radius: .1rem .1rem 0 0;
			margin-top:.1rem;padding:0 .1rem;
			
			.titles {
				padding: 0 .2rem;
				border-bottom: 0.01rem solid #f5f5f5;
				font-size: .14rem;
				color: #A2A7AA;
				height:.41rem;
			}
			
			.lists {
				height:calc(100vh - 1.01rem);
				overflow: scroll;
				.list-item {
					padding: .2rem .17rem;
					border-bottom: 0.01rem solid #ebebeb;
					font-size: 0.12rem;
					
					&:last-child {
						border-bottom: 0;
					}
					
					.col{
						width:33%;
					}
			
					.img {
						margin-right: .12rem;
						width: .28rem;
						height: .28rem;
						img{
							width: .28rem;
							height: .28rem;
							border-radius: .02rem;
						}
					}
			
					.name {
						font-weight: 600;
						font-size: 0.14rem;
					}
			
					.code {
						font-size: 0.12rem;
						color: #989898;
						margin-top: 0.02rem;
					}
			
					.price {
						font-weight: 600;
						font-size: 0.16rem;
					}
			
				}
			}
			
		}
	}
	
</style>