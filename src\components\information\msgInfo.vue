<template>
	<div class="page ">
		<top-back title="詳情"></top-back>

		<div class="list-box">
			<div class="list-item">
				<div>
					<div class="t">{{ item.title }}</div>
					<div class="time">{{ item.create_time }}</div>
				</div>
				<div class="cot" v-html="item.content"></div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "msgInfo",
		data() {
			return {
				item: {
					// title: "通知",
					// create_time: "2024-01-26",
					// content: "contentcontentcontent",
				},
			};
		},
		computed: {},
		components: {},
		created() {
			let id = this.$route.query.id;
			this.initData(id);
		},

		methods: {
			initData(id) {
				this.$server
					.post("/user/noticedetail", {
						id,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.item = res.data;
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0.1rem;
		min-height: 100vh;
	}

	.t {
		font-weight: bold;
	}

	.list-box {

		// background: #ffffff;
		// border-radius: 0.1rem;
		// padding: 0.2rem 0.1rem;
		.t {
			font-weight: bold;
		}

		.time {
			font-size: 0.12rem;
			color: #999;
			margin-top: 0.1rem;
		}

		.cot {
			margin-top: 0.1rem;
		}
	}
</style>