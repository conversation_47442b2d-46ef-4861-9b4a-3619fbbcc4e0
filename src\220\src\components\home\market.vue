<template>
  <div class="page ">
    <div class="flex flex-b top-fixed">
      <div></div>
      <div class="flex-1 t-c">{{ $t("交易市场") }}</div>
      <div
        class="icon ss1 animate__animated animate__fadeIn"
        @click="$toPage('/favorite/search')"
      ></div>
    </div>

    <div class="tabs flex flex-b">
      <div
        class="tabs-item"
        v-for="(item, i) in tabList"
        :key="i"
        @click="$toPage(item.url)"
      >
        <div
          class="icon animate__animated animate__fadeIn"
          :class="item.icon"
        ></div>
        <div class="t">{{ $t(item.name) }}</div>
      </div>
    </div>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="26" :loading="loading">
        <!-- 新股申购显示部分 -->
        <div class="cot" v-if="list.length">
          <div class="title flex flex-b">
            <div class="t">{{ $t("new").b4 }}</div>
            <div class="flex t1" @click="$toPage('/home/<USER>')">
              {{ $t("更多") }}
              <div class="icon jt1"></div>
            </div>
          </div>

          <!-- 展示列表前10 -->
          <div class="list">
            <div
              class=""
              v-show="i < 10"
              v-for="(item, i) in list"
              :key="i"
              @click="toDetail(item)"
            >
              <div class="item flex flex-b ">
                <div class="flex-1">
                  <div class="tt">{{ item.name || "-" }}</div>
                  <div class="tt1">{{ item.code || "-" }}</div>
                </div>
                <div class="flex-1">
                  <div class="tt t-c">
                    {{ $formatMoney(parseFloat(item.price).toFixed(2)) || "0" }}
                  </div>
                  <div class="tt1 t-c">{{ $t("newt").a7 }}</div>
                </div>
                <div class="flex-1">
                  <div class="tt t-r">
                    {{
                      $formatMoney(item.amount) ||
                        $formatMoney(item.num) ||
                        "0"
                    }}{{ $t("home").txt9 }}
                  </div>
                  <div class="tt1 t-r">{{ $t("newt").a9 }}</div>
                </div>
              </div>

              <div class="time">
                {{ $t("market").txt6 }}:
                {{ $formatDate("DD-MM-YYYY hh:mm:ss", item.end * 1000) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 大宗交易 -->
        <div class="cot" v-if="list1.length">
          <div class="title flex flex-b">
            <div class="t">{{ $t("new").b7 }}</div>
            <div class="flex t1" @click="$toPage('/home/<USER>')">
              {{ $t("更多") }}
              <div class="icon jt1"></div>
            </div>
          </div>

          <div class="list" @click="$toPage('/home/<USER>')">
            <div
              class="item flex flex-b"
              v-show="i < 10"
              v-for="(item, i) in list1"
              :key="i"
            >
              <div class="flex-1">
                <div class="tt">{{ item.name || "-" }}</div>
                <div class="tt1">{{ item.code || "-" }}</div>
              </div>
              <div class="flex-1">
                <div class="tt t-c">{{ $formatMoney(item.price) }}</div>
                <div class="tt1 t-c">{{ $t("new").t2 }}</div>
              </div>
              <div class="flex-1">
                <div class="tt t-r">{{ $formatMoney(item.stock_num) }}</div>
                <div class="tt1 t-r">{{ $t("最低购买数量") }}</div>
              </div>
            </div>

            <!-- <div class="time">
              截止时间:2024/04/18 12:00:00
            </div> -->
          </div>
        </div>

        <!-- 量化跟单 -->
        <div class="cot" v-if="list2.length">
          <div class="title flex flex-b">
            <div class="t">{{ $t("home").tab4 }}</div>
            <div class="flex t1" @click="$toPage('/home/<USER>')">
              {{ $t("更多") }}
              <div class="icon jt1"></div>
            </div>
          </div>

          <div class="list">
            <div
              v-for="(item, i) in list2"
              :key="i"
              @click="$toPage('/home/<USER>')"
            >
              <div class="item flex flex-b">
                <div class="flex-1">
                  <div class="tt">{{ item.name || "-" }}</div>
                  <div class="tt1">
                    {{ $t("newt").t18 }} {{ item.sender || "-" }}
                  </div>
                </div>

                <div class="flex-1">
                  <div class="tt t-c">{{ item.dprofit || "-" }}</div>
                  <div class="tt1 t-c">{{ $t("每日盈利") }}</div>
                </div>
                <div class="flex-1">
                  <div class="tt t-r">
                    {{ $formatMoney(item.money) || "-" }}
                  </div>
                  <div class="tt1 t-r">{{ $t("最低跟单金额") }}</div>
                </div>
              </div>

              <div class="time">{{ $t("结束时间") }}: {{ item.end }}</div>

              <div class="time tm">
                {{ $t("new").a69 }}: {{ item.locktime }}
              </div>
            </div>
          </div>
        </div>

        <no-data
          v-if="!list.length && !list1.length && !list2.length"
        ></no-data>
      </van-skeleton>
    </van-pull-refresh>

    <loading ref="loading" />

    <tab-bar :current="2"></tab-bar>
  </div>
</template>

<script>
export default {
  name: "market",
  data() {
    return {
      loading: true,
      isLoading: false,
      tabList: [
        // ipo
        {
          name: this.$t("new").b4,
          icon: "mk1",
          url: "/home/<USER>",
        },
        // 大宗123
        {
          name: this.$t("new").b7,
          icon: "mk2",
          url: "/home/<USER>",
        },
        {
          name: this.$t("日内交易"),
          icon: "mk3",
          // url: "/home/<USER>", //大宗交易模式
          url: "/home/<USER>", //单独模式
        },
        // 跟单
        {
          name: this.$t("new").b5,
          icon: "mk4",
          url: "/home/<USER>",
        },
      ],
      list: [],
      list1: [],
      list2: [],
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.getList();
    this.getList1();
    this.getList2();
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.getList();
      this.getList1();
      this.getList2();
    },
    toDetail(item) {
      this.$storage.save("itemTemp", item);
      this.$toPage(`/home/<USER>
    },
    // 申购列表
    getList() {
      this.$server
        .post("/trade/placinglist", { type: "krw", buy_type: 0 })
        .then((res) => {
          // res = {
          //   status: 1,
          //   msg: "查询成功",
          //   data: [
          //     {
          //       id: 13,
          //       start: 1718758800,
          //       end: 1718982000,
          //       code: "GEM Enviro Management",
          //       bprice: "71.00",
          //       minmoney: "1000.00",
          //       type: 0,
          //       name: "GEM Enviro Management",
          //       exchange: "bse",
          //       num: 5990400,
          //       nums: "149760",
          //       drawdate: "2024/06/24",
          //       market: null,
          //       amtdate: "2024/06/25",
          //       ssdate: "2024/06/26",
          //       cxs: null,
          //       price: "71.000",
          //     },
          //     {
          //       id: 12,
          //       start: 1718758800,
          //       end: 1718982000,
          //       code: "DEE Development Engineer",
          //       bprice: "193.00",
          //       minmoney: "1000.00",
          //       type: 0,
          //       name: "DEE Development Engineer",
          //       exchange: "bse",
          //       num: 20591852,
          //       nums: "16009852",
          //       drawdate: "2024/06/24",
          //       market: null,
          //       amtdate: "2024/06/25",
          //       ssdate: "2024/06/26",
          //       cxs: null,
          //       price: "193.000",
          //     },
          //     {
          //       id: 11,
          //       start: 1718326800,
          //       end: 1718809200,
          //       code: "GP Eco Solutions India",
          //       bprice: "90.00",
          //       minmoney: "1000.00",
          //       type: 0,
          //       name: "GP Eco Solutions India",
          //       exchange: "nse",
          //       num: 3276000,
          //       nums: "1032000",
          //       drawdate: "2024/06/20",
          //       market: null,
          //       amtdate: "2024/06/21",
          //       ssdate: "2024/06/24",
          //       cxs: null,
          //       price: "90.000",
          //     },
          //   ],
          // };

          this.loading = false;
          this.isLoading = false;
          if (res.status == 1) {
            this.list = res.data;
          }
        });
    },
    // 大宗列表
    getList1() {
      this.$server
        .post("/trade/nbhllist", { type: "krw", dz_type: 0 })
        .then((res) => {
          // res = {
          //   status: 1,
          //   msg: "查询成功",
          //   data: [
          //     {
          //       id: 1,
          //       start: 1717948800,
          //       end: 1718697600,
          //       code: "N01",
          //       name: "NILE",
          //       price: "1160.50",
          //       ganggang: 1,
          //       password: "",
          //       stock_num: 1,
          //       type: 0,
          //       exchange: "bse",
          //       symbol: "N01",
          //     },
          //   ],
          // };

          if (res.status == 1) {
            this.list1 = res.data;
          }
        });
    },

    // 量化跟单
    getList2() {
      this.$server.post("/trade/productlist", { type: "krw" }).then((res) => {
        if (res.status == 1) {
          this.list2 = res.data;

          // this.list2 = [
          //   {
          //     name: "名称",
          //     sender: "发单人",
          //     capital: 100000,
          //     dprofit: "-1%",
          //     yprofit: "10%",
          //     money: 1000,
          //   },
          //   {
          //     name: "名称",
          //     sender: "发单人",
          //     capital: 100000,
          //     dprofit: "1%",
          //     yprofit: "10%",
          //     money: 1000,
          //   },
          // ];
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.5rem 0.15rem 0.6rem;
  min-height: 100vh;
}

.tabs {
  padding: 0.15rem 0;
  .tabs-item {
    flex: 1;
    text-align: center;
    .icon {
      margin: 0 auto 0.05rem;
    }
    .t {
      font-size: 0.12rem;
      color: #232323;
    }
  }
}

.top-fixed {
  width: 100%;
  height: 0.5rem;
  padding: 0 0.15rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #eff0f4;
  .zw {
    width: 0.24rem;
    height: 0.24rem;
  }
  div {
    font-size: 0.14rem;
    color: #111111;
    font-family: PingFang TC, PingFang TC;
  }
}

.cot {
  padding: 0.15rem 0;

  .title {
    .t {
      font-weight: 600;
      font-size: 0.16rem;
      color: #000000;
    }
    .t1 {
      font-size: 0.12rem;
      color: #8e8e8e;
    }
  }

  .list {
    background: #ffffff;
    border-radius: 0.12rem;
    margin-top: 0.15rem;
    .item {
      padding: 0.15rem;
      border-bottom: 0.01rem solid #ebebeb;
      .tt {
        font-weight: 600;
        // font-size: 0.16rem;
        font-size: 0.14rem;
        color: #000000;
      }
      .tt1 {
        font-size: 0.12rem;
        color: #979797;
        margin-top: 0.05rem;
      }
    }
    .time {
      font-size: 0.12rem;
      color: #505050;
      padding: 0.1rem 0.15rem;
      &.tm {
        // border-bottom: 0.01rem solid #ebebeb;
        margin-bottom: 0.1rem;
      }
    }
  }
}
</style>
