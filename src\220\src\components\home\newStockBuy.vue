<template>
	<div class="page ">
		<top-back :title="$t('申购详情')"></top-back>

		<div class="cot">
			<div class="list">
				<div class="item">
					<div class=" ">
						<div class="flex flex-b ">
							<div class="flex-1 flex">
								<div class="tt">{{ item.name || "-" }}</div>
								<div class="tt1">{{ item.symbol || "-" }}</div>
							</div>
							<!-- <div class=" flex flex-e animate__animated animate__fadeIn">
								<div class="st-btn" v-if="item.isKsg">
								  {{ $t("newt").a5 }}
								</div>
								<div class="st-btn" v-else>{{ $t("newt").a6 }}</div>
							</div> -->
							<div class="per flex">
								<div class="t2">{{ $t("newt").a4 }}</div>
								<div class="t1 red">
									{{ ((item.price - item.bprice) / item.bprice) * 100 == -100 ? "-" : ( ((item.price - item.bprice) / item.bprice) * 100 ).toFixed(2) }}%
								</div>
							</div>
						</div>

						<div class="item-middle flex flex-b flex-wrap">
							<div class="item-list flex-column-item">
								<div class="t3 red">
									{{ $formatMoney(item.price - item.bprice) }}
								</div>
								<div class="t2 ">{{ $t("newt").a8 }}</div>
							</div>

							<div class="item-list flex-column-item">
								<div class="t3">
									{{ $formatMoney(parseFloat(item.price),2) || "0" }}
								</div>
								<div class="t2">{{ $t("newt").a7 }}</div>
							</div>
							<div class="item-list flex-column-item">
								<div class="t3">
									{{ $formatMoney(item.num) || "0" }}
									<!-- {{ $t("home").txt9 }} -->
								</div>
								<div class="t2 ">{{ $t("newt").a9 }}</div>
							</div>
							<div class="item-list flex-column-item">
								<div class="t3 ">
									{{ $formatMoney(parseFloat(item.bprice),2) || "0" }}
								</div>
								<div class="t2 ">{{ $t("newt").a10 }}</div>
							</div>
							<!--  <div class="item-list">
								<div class="t3">{{ item.markets || item.nums || '0' }}{{ $t('home').txt9 }}</div>
								 <div class="t2">{{ $t('home').txt7 }}</div>
							</div> -->
						</div>
					</div>
					<div class="time" v-if="false">
						{{ $t("market").txt6 }}:
						{{ $formatDate("DD-MM-YYYY hh:mm:ss", item.end * 1000) }}
					</div>
				</div>
			</div>

			<div class="info">
				<div class="title">{{ $t("new").t27 }}</div>
				<div class="">
					<div class="info-item flex flex-b">
						<div class="t">{{ $t("new").t34 }}</div>
						<div class="t1">
							{{ $formatDate("DD-MM-YYYY", item.end * 1000) }}
						</div>
					</div>
					<div class="info-item flex flex-b" v-if="item.drawdate">
						<div class="t">{{ $t("new").t24 }}</div>
						<span class="t1">{{ item.drawdate.slice(0, 10) || "-" }}</span>
					</div>
					<div class="info-item flex flex-b" v-if="item.ssdate">
						<div class="t">{{ $t("new").t25 }}</div>
						<span class="t1">{{ item.ssdate.slice(0, 10) || "-" }}</span>
					</div>
					<div class="info-item flex flex-b" v-if="item.amtdate">
						<div class="t">{{ $t("new").t26 }}</div>
						<span class="t1">{{ item.amtdate.slice(0, 10) || "-" }}</span>
					</div>
					<!-- <div class="info-item flex flex-b">
						<div class="t">{{ $t("new").t28 }}</div>
						<div class="t1">-</div>
					</div> -->
					<div class="info-item flex flex-b">
						<div class="t">{{ $t("new").t29 }}</div>
						<div class="t1  num-font">
							{{ $formatMoney(parseFloat(item.price),2) || "0" }}
						</div>
					</div>
					<!-- <div class="info-item flex flex-b">
						<div class="t">{{ $t("new").t30 }}</div>
						<div class="t1 ">{{ item.chuzhibi || "-" }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">{{ $t("new").t31 }}</div>
						<div class="t1">{{ item.zhuganshi || "-" }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">{{ $t("new").t32 }}</div>
						<div class="t1  num-font">
							{{ $formatMoney(parseFloat(item.bprice).toFixed(2)) || "0" }}
						</div>
					</div> -->
				</div>
			</div>
			<div class="bottom">
				<div class="wbg">
					<div class="t">{{ $t("new").t33 }}</div>
					<input v-model="quantity" @input="quantity = quantity.replace(/[^0-9]/g, '')"
						:placeholder="$t('new').t35" type="number" />
				</div>
				<div @click="submitSg" class="big_btn animate__animated animate__fadeIn">
					{{ $t("new").t16 }}
				</div>
			</div>
			<!-- 是否可申购  v-if="item.isKsg"-->
		</div>

		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newStockBuy",
		data() {
			return {
				item: this.$storage.get("itemTemp") || {},
				quantity: "",
				flag: false,
				type: 0,
        stockType: 'try'
			};
		},
		created() {
			this.type = this.$route.query.type;
      this.stockType = this.$route.query.stockType;
		},
		methods: {
			submitSg() {
				if (!this.quantity) {
					this.$toast(this.$t("new").t35);
					return;
				}

				if (this.flag) {
					return;
				}

				this.flag = true;
				this.$refs.loading.open(); //开启加载

				this.$server.post("/trade/buy_newstock", {
					symbol: this.item.symbol,
					zhang: this.quantity,
					type: this.stockType,
					id: this.item.id,
					buy_type: 0
				}).then((res) => {
					this.$refs.loading.close(); //关闭加载

					this.$toast(this.$formText(res.msg));
					setTimeout(() => {
						this.flag = false;
					}, 2000);
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0.12rem 0.1rem;
		min-height: 100vh;
	}

	.list {
		background: #FFFFFF;
		border-radius: 0.13rem;
		margin-bottom: .1rem;
		.item {
			padding: 0.12rem;
			.tt {
				font-weight: 600;
				font-family: PingFangSC, PingFang SC;
				font-size: 0.15rem;
				color: #333333;
			}

			.tt1 {
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
			}

			.item-middle {
				padding: 0.1rem 0;
				margin-top:.2rem;
				background: linear-gradient( 90deg, #FFF0F1 0%, #FFFFFF 100%);
				border-radius: 0.08rem;
				padding: 0.12rem;
				.item-list {
					line-height: 0.24rem;

					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #999999;
					}

					.t3 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #333333;
					}
				}
			}
		}

		.time {
			font-size: 0.12rem;
			color: #505050;
			padding: 0.1rem 0.15rem;
			//   &.tm {
			//     border-top: 0.01rem solid #ebebeb;
			//   }
		}

		.per {
			background: #FFFFFF;
			border-radius: 0.02rem;
			padding: 0.02rem 0.08rem;
			border: 0.01rem solid #DBDBDB;
			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #e10414;
			}

			.t2 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
				margin-right: 0.05rem;
			}
		}

		.red {
			color: #df4645;
		}
	}

	.cot {
		.info {
			background: #FFFFFF;
			border-radius: 0.13rem;
			padding: 0.15rem;

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.18rem;
				color: #333333;
			}

			.info-item {
				padding: 0.15rem 0 0;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #999999;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #333333;
				}
			}
		}

		.bottom {
			background: #FFFFFF;
			border-radius: 0.13rem;
			padding: 0.12rem;
			margin-top: 0.1rem;
			.wbg {
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.17rem;
					color: #333333;
				}

				input {
					width: 100%;
					height: 0.44rem;
					background: #F5F5F5;
					border-radius: 0.08rem;
					padding: 0 0.1rem;
					margin-top: 0.1rem;
					background: #F5F7FA;

					&::placeholder {
						font-size: 0.12rem;
						color: #9a9fa5;
					}
				}
			}

			.big_btn {
				border-radius: 0.22rem;
				margin: 0.2rem 0rem 0;
			}
		}

		.item-top {
			.t {
				font-weight: 500;
				font-size: 0.14rem;
				color: #666666;
			}

			.t1 {
				font-weight: 500;
				font-size: 0.14rem;
				color: #ed1d1d;
				margin-left: 0.05rem;
			}

			.name {
				font-weight: 600;
			}

			.code {
				font-weight: 500;
				font-size: 0.12rem;
				color: #464646;
				margin-top: 0.05rem;
			}
		}
	}
</style>
