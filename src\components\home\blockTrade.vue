<template>
	<div class="page ">
		<!-- <top-back title="大宗交易"></top-back> -->
		<top-back title="低價承接"></top-back>

		<div class="nav-box flex ">
			<div class="nav-item" v-for="(item, index) in navList" :key="index"
				:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
				{{ item.name }}
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot">
					<div class="list" v-if="currmentIndex == 0">
						<div class="list-item" v-for="(item, index) in chooseList" :key="index"
							@click="stockDetails(item)">
							<div class="flex flex-b">
								<div class="">
									<div class="name">{{ item.name }}</div>
									<div class="code">{{ item.symbol }}</div>
								</div>
								<div class="st">買入</div>
							</div>
							<div class="bg">
								<div class="flex flex-b">
									<div class="tt">買入價</div>

									<div class="price red">{{ $formatMoney(item.price) }}</div>
								</div>

								<div class="flex flex-b">
									<div class="tt">最小買入股數</div>

									<div class="price">
										{{ $formatMoney(item.stock_num) || "-" }}
									</div>
								</div>
							</div>
						</div>
						<no-data v-if="!chooseList.length"></no-data>
					</div>

					<!-- 買入記錄 -->
					<div class="list" v-else>
						<div class="list-items" v-for="(item, index) in myList" :key="index">
							<div class="flex flex-b">
								<div>
									<div class="name">
										{{ item.stock_name }}
									</div>
									<div class="code">
										{{ item.stock_code }}
									</div>
								</div>

								<div class="st" :class="item.state != '审核中' ? 'sc' : ''">
									{{ $t(item.state) }}
								</div>
							</div>

							<div class="inner flex flex-b">
								<div class="inner-item flex flex-b">
									<div class="t">
										價格
									</div>
									<div class="t1 red">
										{{ $formatMoney(item.buy_price) }}
									</div>
								</div>

								<div class="inner-item flex flex-b">
									<div class="t">
										買進張數
									</div>
									<div class="t1">
										{{ $formatMoney(item.zhang, 0) }}
									</div>
								</div>

								<div class="inner-item flex flex-b">
									<div class="t">
										成交量
									</div>
									<div class="t1">
										{{ $formatMoney(item.cj_num) }}
									</div>
								</div>
							</div>
						</div>

						<no-data v-if="!myList.length"></no-data>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<van-popup v-model="show" round position="center" :style="{ width: '90%' }">
			<div class="pop">
				<!-- <div class="pop-title ">{{ stockObj.name + "/" + stockObj.code }}</div> -->
				<div class="pop-price t-c">
					<div class="t1">買入價格</div>
					<div class="t">
						最低買入：<span>{{ $formatMoney(stockObj.price) || 0 }}</span>
					</div>
				</div>

				<div class="pad">
					<div class="ipt ">
						<div class="tt">買入張數</div>
						<input class="flex-1" v-model="buyObj.handle" type="number" placeholder="請輸入買入張數"
							@input="TypeInput($event)" />
						<div class="flex">
							<div class="t1">帳戶可用資金：</div>
							<div class="t2">
								{{ $formatMoney(userInfo.twd) || 0 }}
							</div>
						</div>

						<div class="flex mt10">
							<div class="t1">申請額：</div>
							<div class="t2">
								{{ $formatMoney(countMoney) || 0 }}
							</div>
						</div>
					</div>

					<!--        <div class="pop-num">-->
					<!--          <div>{{ $t("new").b47 }}</div>-->
					<!--          <input-->
					<!--            :placeholder="$t('new').t1"-->
					<!--            type="password"-->
					<!--            v-model="password"-->
					<!--          />-->
					<!--        </div>-->

					<div class="flex flex-b">
						<!-- <div class="b-btn bt" @click="show = false">
            取消
          </div> -->
						<div @click="buyFn" class="b-btn">買入</div>
					</div>
				</div>
			</div>
			<div class="icon close" @click="show = false"></div>
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "blockTrade",
		data() {
			return {
				loading: true,
				isLoading: false,
				market: 'taiwan', // 默认台湾市场
				navList: [{
						name: "股票列表",
						type: 0,
					},
					{
						name: "購買記錄",
						type: 1,
					},
				],
				currmentIndex: 0,
				chooseList: [
					{
					  name: "名称",
					  code: "007",
					  price: "1000",
					  stock_num: 1000,
					},
				],
				myList: [
					{
					  stock_name: "stock_name",
					  stock_code: "stock_code",
					  buy_price: "1000",
					  zhang: "1000",
					  cj_num: "1000",
					  status: 1,
					  state: "审核中",
					},
				],
				show: false,
				stockObj: {},
				buyObj: {
					handle: null,
				},
				password: "",
				userInfo: {},
				currentItem: {},
			};
		},
		computed: {
			countMoney() {
				return this.stockObj.price * this.buyObj.handle * 1000;
			},
			// 根据市场类型返回对应的API类型参数
			apiMarketType() {
				return this.market === 'taiwan' ? 'twd' : 'usd';
			}
		},
		created() {
			// 从URL参数获取市场类型
			const market = this.$route.query.market;
			if (market === 'us') {
				this.market = 'us';
			} else {
				this.market = 'taiwan';
			}

			this.initData();
			this.getNew();
		},
		mounted() {},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.currmentIndex = 0;
				this.initData();
				this.getNew();
			},
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: this.apiMarketType
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			TypeInput(e) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.buyObj.handle = e.target.value.replace(inputType, "");
				});
			},
			getNew() {
				this.$server
					.post("/trade/nbhllist", {
						type: this.apiMarketType,
						dz_type: 0,
					})
					.then((res) => {
						this.isLoading = false;
						this.loading = false;
						this.$refs.loading.close(); //关闭加载

						if (res.status == 1) {
							// this.chooseList = res.data;
						}
					});
			},
			getMine() {
				this.$server
					.post("/trade/ustockslist", {
						type: this.apiMarketType,
						dz_type: 0,
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						if (res.status == 1) {
							// this.myList = res.data;
						}
					});
			},
			stockDetails(stock) {
				this.show = true;
				this.currentItem = stock;
				this.stockObj = stock;
				// this.$server
				//   .post("/trade/stocksdetails", {
				//     symbol: stock.code,
				//   })
				//   .then((res) => {
				//     this.stockObj = res.data;
				//   });
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.$refs.loading.open(); //开启加载

				if (index) this.getMine();
				else this.getNew();
			},
			buyFn() {
				if (!this.buyObj.handle) {
					this.$toast("請輸入買入張數");
					return;
				}
				// if (!this.password) {
				//   this.$toast(this.$t("new").t);
				//   return;
				// }
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/buy_stock", {
						symbol: this.stockObj.code,
						zhang: this.buyObj.handle,
						//password: this.password,
						buyzd: 1,
						ganggan: 1,
						type: this.apiMarketType,
						is_qc: 2,
						is_type: 0,
						id: this.currentItem.id, //大宗增加传递参数，列表id
					})
					.then((res) => {
						this.$refs.loading.close();

						this.show = false;
						if (res.msg) {
							this.$toast(this.$t(res.msg));
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 1rem 0.1rem 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	.nav-box {
		position: fixed;
		top: 0.5rem;
		left: 0;
		width: 100%;
		background-color: #fff;
		z-index: 999;
		.nav-item {
			padding: 0.1rem 0;
			flex: 1;
			font-size: 0.15rem;
			color: #8e8e8e;
			text-align: center;
			position: relative;
			&::after {
				content: "";
				width: 100%;
				height: 0.02rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}

			&.active {
				font-weight: 500;
				color: #DCA25C;

				&::after {
					background: #DCA25C;
				}
			}
		}
	}

	.titles {
		padding: 0.1rem 0.1rem 0;
		border-bottom: 0.01rem solid #f5f5f5;

		div {
			font-size: 0.12rem;
			color: #464646;
		}
	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background-color: #fff;
		// padding: 0.15rem;
		border-radius: 0.08rem;
		position: relative;

		.pad {
			padding: 0.2rem 0.15rem;
		}

		.btips {
			padding: 0.15rem 0;
			line-height: 0.24rem;

			.t2 {
				color: #a91111;
			}
		}

		.pop-title {
			font-size: 0.16rem;
			text-align: center;
		}

		.pop-price {
			padding: 0.15rem 0;
			background: #F6F8FE;
			.t {
				font-size: 0.14rem;
				color: #9a9fa5;
				span {
					color: #60bb74;
				}
			}

			.t1 {
				font-size: 0.16rem;
				color: #000000;
				margin-bottom: 0.05rem;
			}
		}

		.ipt {
			.tt {
				font-size: 0.14rem;
				color: #0e1028;
			}
			input {
				background: transparent;
				border-radius: 0.04rem;
				border: 0.01rem solid #cecece;
				height: 0.42rem;
				line-height: 0.42rem;
				padding: 0 0.1rem;
				// margin-left: 0.1rem;
				margin: 0.1rem 0;
				width: 100%;

				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}

			.t2 {
				font-size: 0.12rem;
				color: #dba25c;
			}

			.mt10 {
				margin-top: 0.05rem;
			}
		}

		.pop-num {
			margin-top: 0.15rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				background: #f8f8f8;
				border-radius: 0.06rem;
				border: 0.01rem solid #b8b8b8;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #606060;
				}
			}
		}

		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;

			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}

		.b-btn {
			width: 100%;
		}
	}

	.cot {
		.title {
			padding: 0.1rem;

			div {
				font-weight: 500;
				font-size: 0.12rem;
				color: #666666;
			}
		}

		.list {
			.list-item {
				margin-bottom: 0.15rem;
				border-bottom: 0.01rem solid #EAEDF8;
				padding-bottom: 0.1rem;
				.name {
					font-size: 0.14rem;
					color: #000000;
				}

				.code {
					font-size: 0.12rem;
					color: #909090;
				}

				.mtb10 {
					margin: 0.1rem 0;
				}

				.bg {
					margin-top: 0.1rem;
					line-height: 0.24rem;
					background: #fff;
					border-radius: 0.06rem;
					padding: 0.1rem 0.5rem;
				}

				.tt {
					font-size: 0.12rem;
					color: #656565;
				}

				.price {
					font-size: 0.14rem;
				}

				.red {
					color: #cf2829;
				}

				.st {
					background: #FFFFFF;
					border-radius: 0.13rem;
					padding: 0.05rem 0.15rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #DCA25C;
				}

				.t {
					font-weight: bold;
				}
			}
		}

		.list-items {
			margin-bottom: 0.15rem;
			border-bottom: 0.01rem solid #EAEDF8;
			padding-bottom: 0.1rem;
			.name {
				font-size: 0.14rem;
				color: #000000;
			}

			.code {
				font-size: 0.12rem;
				color: #909090;
			}

			.st {
				background: #FFFFFF;
				border-radius: 0.13rem;
				padding: 0.05rem 0.15rem;
				font-weight: 500;
				font-size: 0.12rem;
				color: #dba25c;

				&.sc {
					color: #9f9f9f;
				}
			}

			.inner {
				flex-wrap: wrap;
				padding: 0.1rem;
				background: #fff;
				border-radius: 0.06rem;
				margin-top: 0.1rem;
				.inner-item {
					width: 100%;
					line-height: 0.24rem;
					.t {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #9FA9BA;
					}
					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #182133;
					}
					.red {
						color: #cf2829;
					}
				}
			}
		}
	}
</style>