<template>
  <div class="page">
    <top-back :title="$t('端午福籤')"></top-back>

    <div class="icon rns animate__animated animate__fadeIn" @click="$toPage('/favorite/search')"></div>

    <!--    <div class="nav-box flex flex-b">-->
    <!--      <div-->
    <!--        class="nav-item"-->
    <!--        v-for="(item, index) in navList"-->
    <!--        :key="index"-->
    <!--        :class="{ active: currmentIndex === item.type }"-->
    <!--        @click="changeNav(item.type)"-->
    <!--      >-->
    <!--        {{ $t(item.name) }}-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- 购买 -->
    <template v-if="currmentIndex == 0">
      <div class="tj">
        <div class="tj-box">
          <img src="../../assets/1212.png" class="tj-box-img" />
          <div class="tj-box-right">
            <div class="tj-box-top">
              <div class="tj-box-top-left">端午福籤<span>9188</span></div>
              <div class="tj-box-top-right">活動價：1000元</div>
            </div>
            <div class="tj-box-info">參與端午福籤，可中1萬元-100萬元現金紅包，同時還有超級神秘大驚喜</div>
          </div>
        </div>


        <div class="b-btn" @click="buyFn">{{ $t("立即參與") }}</div>
      </div>
    </template>

    <template v-if="currmentIndex == 1">
      <div class="jy-list" v-if="chooseList.length">
        <div
            class="jy-item"
            v-for="(item, index) in chooseList"
            :key="index"
            @click="sellstrategy(item)"
        >
          <div class="flex flex-b">
            <div class="">
              <div class="name">{{ item.stock_name }}</div>
              <div class="code">{{ item.stock_code }}</div>
            </div>
            <div class="flex flex-e">
              <div class="s-btn">{{ $t("抛售") }}</div>
            </div>
          </div>

          <div class="hbg flex flex-b">
            <div class="item">
              <div class="t1">{{ $t("拥有数量") }}</div>
              <div class="t">{{ $formatMoney(item.stock_num) }}</div>
            </div>
            <div class="item">
              <div class="t1">{{ $t("评估金额") }}</div>
              <div class="t">{{ $formatMoney(item.buy_price) }}</div>
            </div>
            <div class="item">
              <div class="t1">{{ $t("评估损益") }}</div>
              <div class="t">{{ $formatMoney(item.yingkui) }}</div>
            </div>
            <div class="item">
              <div class="t1">{{ $t("收益率") }}</div>
              <div class="t" :class="item.gain < 0 ? 'green' : 'red'">
                {{ item.gain.toFixed(2) }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      <no-data v-else></no-data>
    </template>

    <template v-if="currmentIndex == 2">
      <div class="mx-list" v-if="chooseList1.length">
        <div class="titles flex flex-b">
          <div class="flex-1">{{ $t("self").tit1 }}</div>
          <div class="flex-1 t-c">{{ $t("盈利金额") }}</div>
          <div class="flex-1 t-c">{{ $t("收益率") }}</div>
          <div class="flex-1 t-r">{{ $t("卖出时间") }}</div>
        </div>
        <div
            class="mx-item flex flex-b"
            v-for="(item, index) in chooseList1"
            :key="index"
        >
          <div class="flex-1">
            <div class="name">{{ item.stock_name || "-" }}</div>
            <div class="code">{{ item.stock_code || "-" }}</div>
          </div>
          <div class="flex-1 t-c t">
            {{ $formatMoney(item.yingkui) || "-" }}
          </div>
          <div class="flex-1 t-c t" :class="item.gain < 0 ? 'green' : 'red'">
            {{ item.gain ? item.gain.toFixed(2) : "0" }}%
          </div>
          <div class="flex-1 t-r t">{{ item.sell_time || "-" }}</div>
        </div>
      </div>
      <no-data v-else></no-data>
    </template>
    <!-- 已申请清单 -->
    <template v-if="currmentIndex == 3">
      <div class="mx-list" v-if="myList.length">
        <div class="titles flex flex-b">
          <div class="flex-1 ">{{ $t("金额") }}</div>
          <div class="flex-1 t-c">{{ $t("申请时间") }}</div>
          <div class="flex-1 t-c">{{ $t("审核时间") }}</div>
          <div class="flex-1 t-r">{{ $t("状态") }}</div>
        </div>
        <div
            class="mx-item flex flex-b"
            v-for="(item, index) in myList"
            :key="index"
        >
          <div class="flex-1 t">{{ $formatMoney(item.credit) }}</div>
          <div class="flex-1 t-c t">{{ item.create_time || "-" }}</div>
          <div class="flex-1  t-c t">{{ item.end_time || "-" }}</div>
          <div
              class="flex-1 t t-r"
              :class="item.state == '已拒绝' ? 'red' : 'green'"
          >
            {{ $t(item.state) }}
          </div>
        </div>
      </div>

      <no-data v-else></no-data>
    </template>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      money: "1000",
      userInfo: {},
      market: 'taiwan', // 默认台湾市场
      navList: [
        { name: "订单提交", type: 0 },
        { name: "交易中", type: 1 },
        { name: "交易明细", type: 2 },
        { name: "已申请", type: 3 },
      ],
      currmentIndex: 0,
      chooseList: [
        // {
        //   name: '名城',
        //   price: '1000',
        //   code: '008900'
        // }
      ],
      chooseList1: [
        // {
        //   name: '名城',
        //   price: '1000',
        //   code: '008900'
        // }
      ],
      myList: [
        // {
        //   stock_name: '名城',
        //   stock_code: '008900',
        //   buy_price: '100',
        //   buyzd: 1000,
        //   cj_num: 100,
        //   state: '成功'
        // }
      ],
      show: false,
      stockObj: {},
      buyObj: {
        handle: null,
      },
    };
  },
  computed: {
    // 根据市场类型返回对应的API类型参数
    apiMarketType() {
      return this.market === 'taiwan' ? 'twd' : 'usd';
    }
  },
  created() {
    // 从URL参数获取市场类型
    const market = this.$route.query.market;
    if (market === 'us') {
      this.market = 'us';
    } else {
      this.market = 'taiwan';
    }
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    inputEvent(e) {
      let value = e.detail.value;
      // 限制输入数字
      this.$nextTick(() => {
        this.money = value.replace(/[^0-9]/g, "");
      });
    },
    getUserInfo() {
      this.$server.post("/user/getUserinfo", {}).then((res) => {
        this.userInfo = res.data;
      });
    },
    TypeInput(e) {
      // 只能输入数字的验证;
      const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
      this.$nextTick(function() {
        this.buyObj.handle = e.target.value.replace(inputType, "");
      });
    },
    //普通股票的持仓列表
    getNew() {
      this.$server
          .post("/transaction/userstocklist", { buy_type: 1 })
          .then((res) => {
            this.$refs.loading.close();
            if (res.status == "1") {
              delete res.data.ccsz;
              delete res.data.fdyk;
              this.chooseList = Object.values(res.data);
            }
          });
    },
    // 普通股票的平仓列表
    getNew1() {
      this.$server
          .post("/transaction/userstocklists", { buy_type: 1 })
          .then((res) => {
            this.$refs.loading.close();
            if (res.status == "1") {
              delete res.data.ccsz;
              delete res.data.lsyk;
              this.chooseList1 = Object.values(res.data);
            }
          });
    },
    // 平仓
    sellstrategy(item) {
      this.$refs.loading.open(); //开启加载

      this.$server
          .post("/transaction/sell_stock", { id: item.id, buy_type: 1 })
          .then((res) => {
            if (res.status == 1) {
              this.$toast(this.$t("position").txt4);
              this.getNew();
            } else {
              this.$toast(this.$formText(res.msg));
            }
          });
    },
    // 已申请列表
    getMine() {
      this.$server.post("/transaction/urnjylist", { type: 1 }).then((res) => {
        this.$refs.loading.close(); //开启加载

        this.myList = res.data;
      });
    },
    changeNav(index) {
      this.currmentIndex = index;

      if (index !== 0) {
        this.$refs.loading.open(); //开启加载
      }

      switch (index) {
        case 1:
          this.getNew();
          break;
        case 2:
          this.getNew1();
          break;
        case 3:
          this.getMine();
          break;

        default:
          break;
      }
    },
    buyFn() {
      if (!this.money) {
        this.$toast(this.$t("请输入金额"));
        return;
      }
      this.$refs.loading.open(); //开启加载
      this.$server.post("/trade/rnjy", { money: this.money, type: this.apiMarketType }).then((res) => {
        this.$refs.loading.close();

        if (res.msg) {
          this.$toast(this.$t(res.msg));
          return;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.mx-list {
  .titles {
    padding: 0.1rem;
    border-bottom: 0.01rem solid #f5f5f5;
    div {
      font-size: 0.12rem;
      color: #757575;
    }
  }
  .mx-item {
    padding: 0.1rem;
    border-bottom: 0.01rem solid #f5f5f5;
    .name {
      font-weight: bold;
    }
    .code {
      font-size: 0.12rem;
      color: #464646;
    }
    .t {
      font-weight: bold;
      font-size: 0.12rem;
    }
    .red {
      color: #c5585e;
    }
    .green {
      color: #4f8672;
    }
  }
}

.jy-list {
  .jy-item {
    padding: 0.1rem;
    border-bottom: 0.01rem solid #f5f5f5;
    .name {
      font-weight: bold;
    }
    .code {
      font-weight: 500;
      font-size: 0.12rem;
      color: #464646;
    }
    .s-btn {
      background: #6970af;
      border-radius: 0.04rem;
      padding: 0.05rem 0.1rem;
      font-size: 0.12rem;
      color: #ffffff;
    }
    .hbg {
      margin-top: 0.1rem;
      flex-wrap: wrap;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 48%;
        line-height: 0.2rem;
      }
      .t {
        font-weight: 600;
        font-size: 0.12rem;
      }
      .t1 {
        font-size: 0.12rem;
        color: #464646;
      }

      .red {
        color: #c5585e;
      }
      .green {
        color: #4f8672;
      }
    }
  }
}
.pd30 {
  padding: 20px 30px;
  .mx-item {
    border: 4px solid #133ca3;
    border-radius: 20px;
    padding: 30px 20px;
    margin-bottom: 20px;
    .b-btn {
      margin: 0;
      &.dis {
        background-color: #e71616;
      }
    }
    .name {
      font-size: 32px;
      color: #333333;
      &.st {
        color: #014b8d;
        font-family: Roboto;
        font-weight: bold;
      }
    }
    .code {
      display: inline-block;
      background: #f0f3fa;
      border-radius: 10px;
      padding: 10px 20px;
      font-size: 24px;
      color: #333;
      margin-top: 10px;
    }
    .num {
      font-size: 36px;
      font-family: Roboto;
      font-weight: bold;
      color: #333333;
    }

    .lv {
      font-size: 24px;
      color: #333333;
      .red {
        font-size: 36px;
        font-family: Roboto;
        font-weight: 400;
        // color: #F2232B;
        color: #e71616 !important;
        margin-left: 10px;
        &.die {
          color: #014b8d !important;
        }
      }
    }
    .time {
      font-size: 24px;
      color: #666666;
      margin-top: 30px;
    }
  }
}

.tj {
  padding: 0.1rem 0.2rem;
  .tj-box{
    background: #59ae7c;
    border-radius: .1rem;
    display: flex;
    align-items: center;
    padding: .2rem .1rem;
    margin-bottom: .2rem;
    .tj-box-img{
      width: .8rem;
      margin-right: .1rem;
    }
    .tj-box-right{
      width: calc(100% - .9rem);
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .tj-box-top{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tj-box-top-left{
        display: flex;
        align-items: center;
        font-size: .14rem;
        color: #333;
        span{
          font-size: .14rem;
          color: #286432;
          padding-left: .1rem;
        }
      }
      .tj-box-top-right{
        height: .3rem;
        border-radius: .15rem;
        padding: 0 .1rem;
        border: .01rem solid #fff;
        background: #419646;
        font-size: .14rem;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .tj-box-info{
      color: #2c6c39;
      font-size: .14rem;
      line-height: .22rem;
      margin-top: .1rem;
    }
  }
  .b-btn {
    margin: 0 auto 0.15rem;
  }
  .txt {
    line-height: 0.24rem;
    .t {
      color: #737581;
      margin-top: 0.05rem;
    }
    .t1 {
      color: #919191;
      font-size: 0.12rem;
    }
  }
  .ipt {
    .title {
      font-weight: 500;
      font-size: 0.12rem;
    }
    input {
      width: 100%;
      border-radius: 0.04rem;
      border: 0.01rem solid #c0c0c0;
      padding: 0 0.1rem;
      margin: 0.1rem 0 0.3rem;
      height: 0.4rem;
      &::placeholder {
        font-size: 0.12rem;
        color: #9a9fa5;
      }
    }
  }
}

.page {
  padding: 0.56rem 0 0.1rem;
  min-height: 100vh;
}
.rns {
  position: fixed;
  top: 0.15rem;
  right: 0.15rem;
  z-index: 999;
}

.pop {
  background: #fff;
  // padding: 30px;
  border-radius: 10px;
  .pop-title {
    font-size: 32px;
    color: #fff;
    text-align: center;
    background: #014b8d;
    border-radius: 10px 10px 0 0;
    padding: 30px 0;
  }
  .pdd {
    padding: 0 30px 30px;

    .t2 {
      margin: 30px 0;
      color: #333333;
    }

    input {
      height: 80px;
      line-height: 80px;
      border-radius: 20px;
      background-color: #e9eff5;
      padding: 0 20px;
    }
  }
  .pop-price {
    padding: 30px 0;
    text-align: center;
    border-bottom: 2px solid #d4e0eb;
    .t {
      font-size: 32px;
      color: #333;
    }
    .t1 {
      font-size: 60px;
      font-family: Roboto;
      font-weight: bold;
      color: #014b8d;
    }
  }
  .pop-num {
    height: 60px;
    display: flex;
    align-items: center;
    // font-size: 30px;
    color: #333;
    margin-top: 20px;
    input {
      height: 88px;
      line-height: 88px;
      border: 2px solid #cccccc;
      border-radius: 20px;
      background-color: transparent;
      padding: 0 20px;
    }
  }

  .total {
    height: 100px;
    line-height: 100px;
    background: #f0f2f4;
    border-radius: 0 0 10px 10px;
    font-size: 32px;
    color: #014b8d;
    text-align: center;
    span {
      font-size: 40px;
      font-family: Roboto;
      font-weight: bold;
      color: #ff6000;
      margin-left: 10px;
    }
  }

  .b-btn {
    height: 100px;
    line-height: 100px;
    border-radius: 0 0 10px 0;
    text-align: center;
    margin: 0;
  }
}

.nav-box {
  padding: 0 0.1rem;
  background: #ffffff;
  box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);
  position: fixed;
  width: 100%;
  top: 0.5rem;
  left: 0;
  z-index: 999;
  .nav-item {
    padding: 0.1rem 0;
    flex: 1;
    font-size: 0.12rem;
    color: #a1a1a1;
    text-align: center;
    position: relative;
    &::after {
      content: "";
      width: 50%;
      height: 0.02rem;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background: transparent;
    }
    &.active {
      color: #6970af;
      &::after {
        background: #6970af;
      }
    }
  }
}

.th {
  // padding: 20px 30px;
  padding: 20px;
  background: #edf0f2;
  .th-td {
    color: #91a2b1;
    div {
      color: #91a2b1;
    }
  }
}

.list-box {
  .list-content {
    .list-item {
      padding: 30px 20px;
      border-bottom: 2px solid #cccccc;
      .b-btn {
        margin: 0 0 0 10px;
      }
      .shares-name-box {
        font-size: 32px;
        font-weight: bold;
        color: #333;
        div {
          font-size: 32px;
          // font-weight: bold;
          color: #333;
        }

        .code {
          background: #f0f3fa;
          border-radius: 10px;
          padding: 10px 0;
          font-size: 24px;
          font-weight: 400;
          font-family: Roboto;
          color: #333333;
          width: 140px;
          text-align: center;
          margin-top: 10px;
        }

        &.shares-price-num {
          font-family: Roboto;
          font-weight: bold;
          color: #ff3636;
        }
      }

      .new-price {
        display: flex;
        width: 17%;

        .shares-price-num {
          font-size: 28px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
        }
      }

      .shares-price {
        display: flex;
        width: 17%;

        .shares-price-num {
          font-size: 28px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
        }
      }

      .number {
        display: flex;
        width: 17%;

        .shares-price-num {
          font-size: 28px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
        }
      }

      .new-code {
        display: flex;
        width: 17%;
        justify-content: flex-end;
      }
    }
  }
}

::v-deep .uni-input-input {
  font-size: 32px;
  color: #333;
}

.red {
  div {
    color: #e71616 !important;
  }

  &.die {
    div {
      color: #014b8d !important;
    }
  }
}
</style>
