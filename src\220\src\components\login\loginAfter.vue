<template>
	<div class="page flex flex-b">
		<div class="logo animate__animated animate__fadeIn">
			<img :src="$cfg.logo" />
		</div>

		<div class="cot">
			<!-- <div class="icon jz2 animate__animated animate__fadeIn"></div> -->

			<div class="txt">
				{{ $t("new").b22 }}
			</div>
			<div class="txt t ">
				{{ $t("new").b23 }}
			</div>
			<div class="txt  t">
				{{ $t("new").b24 }}
			</div>
		</div>

		<div class="icon jzz animate__animated animate__rotateOut animate__infinite"></div>
	</div>
</template>

<script>
	export default {
		name: "loginAfter",
		props: {},
		data() {
			return {
				cfg: {},
			};
		},
		components: {},
		mounted() {
			
			this.setInt();
		},
		methods: {
			setInt() {
				setTimeout(() => {
					this.$toPage("/home/<USER>");
				}, 2000);
			},

			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post('/common/config', {
					type: 'all'
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/logo.png");
				}
				this.cfg = val;
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		min-height: 100vh;
		padding: 1.2rem 0.4rem 2rem;
		flex-direction: column;
		background-color: #fff;
		.logo {
			width: 1.17rem;
			// height: 0.88rem;
			// border-radius: 50%;
			overflow: hidden;
			margin: 0 auto;

			img {
				width: 1.17rem;
				height: 100%;
				object-fit: cover;
			}
		}

		.cot {
			.txt {
				font-weight: 600;
				font-size: 0.22rem;
				text-align: center;
				margin-top: 0.4rem;

				&.t {
					font-size: 0.12rem;
					color: #616161;
					margin-top: 0.1rem;
				}
			}
		}

		.t {
			font-weight: 500;
			font-size: 0.12rem;
			text-align: center;
		}
		.jzz{
			margin-top: 0.5rem;
		}
	}
</style>